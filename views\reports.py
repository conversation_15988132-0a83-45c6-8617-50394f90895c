from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout,
                          QFrame, QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
                          QDateEdit, QFormLayout, QDialog, QMessageBox, QMenu, QAction,
                          QAbstractItemView, QTabWidget, QScrollArea, QGroupBox, QStackedWidget,
                          QGridLayout, QLineEdit, QCheckBox, QRadioButton, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, QDate, QSize
from PyQt5.QtGui import QFont, QIcon, QColor, QPainter, QDoubleValidator, QCursor

# Define RTL helper classes directly
class RTLComboBox(QComboBox):
    """Replacement class for RTLComboBox"""
    pass

def apply_rtl_to_all_widgets(widget):
    """Replacement function for apply_rtl_to_all_widgets"""
    pass

from styles import AppStyles
from models.reports import ReportModel
import datetime
import sqlite3
import re
from utils.date_utils import DateTimeUtils

class ReportsView(QWidget):
    def __init__(self):
        super().__init__()

        # إعداد التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 15, 25, 25)  # زيادة الهامش العلوي من 0 إلى 15

        # تهيئة متغير لتخزين مؤشر التبويب السابق
        self.previous_tab_index = 0

        # إنشاء تبويبات التقارير
        self.reports_tabs = QTabWidget()
        self.reports_tabs.setObjectName("reports_tabs")
        self.reports_tabs.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه التبويبات من اليمين إلى اليسار

        # إنشاء تبويبات مختلفة للتقارير
        self.daily_sales_tab = self.create_daily_sales_tab()
        self.monthly_sales_tab = self.create_monthly_sales_tab()
        self.inventory_tab = self.create_inventory_tab()
        self.profits_tab = self.create_profits_tab()
        self.top_selling_products_tab = self.create_top_selling_products_tab()  # إضافة تبويب جديد للمنتجات الأكثر مبيعًا

        # إضافة التبويبات إلى widget التبويبات
        self.reports_tabs.addTab(self.daily_sales_tab, "المبيعات اليومية")
        self.reports_tabs.addTab(self.monthly_sales_tab, "المبيعات الشهرية")
        self.reports_tabs.addTab(self.top_selling_products_tab, "المنتجات الاكثر مبيعاً")
        self.reports_tabs.addTab(self.profits_tab, "تقرير الأرباح")
        self.reports_tabs.addTab(self.inventory_tab, "تقرير المخزون")

        # تخزين مؤشرات التبويبات للاستخدام لاحقًا
        self.tab_indices = {
            "المبيعات اليومية": 0,
            "المبيعات الشهرية": 1,
            "المنتجات الاكثر مبيعاً": 2,
            "تقرير الأرباح": 3,
            "تقرير المخزون": 4
        }

        # ربط تغيير التبويب بدالة تحديث التقرير
        self.reports_tabs.currentChanged.connect(self.on_tab_changed)

        # تعديل تنسيق التبويبات - إضافة هامش علوي للتابات
        self.reports_tabs.setStyleSheet("""
            QTabWidget::pane {
                top: 0px;
                padding-top: 5px; /* تقليل التباعد الداخلي العلوي من 10px إلى 5px */
                margin-top: 0px; /* إزالة الهامش العلوي */
            }
        """)

        layout.addWidget(self.reports_tabs)

        # تطبيق الأنماط
        self.apply_styles()

        # توليد التقرير بشكل افتراضي
        self.on_tab_changed(0)
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def on_tab_changed(self, index):
        """معالجة تغيير التبويب وعرض التقرير المناسب"""
        # التحقق من أن التبويب مفعل
        if not self.reports_tabs.isTabEnabled(index):
            # إذا كان التبويب غير مفعل، نعود إلى التبويب السابق
            self.reports_tabs.setCurrentIndex(self.previous_tab_index)
            return

        tab_name = self.reports_tabs.tabText(index)

        # تحديث مؤشر التبويب السابق
        self.previous_tab_index = index

        # عرض التقرير المناسب
        if tab_name == "المبيعات اليومية":
            self.generate_daily_sales_report()
        elif tab_name == "المبيعات الشهرية":
            self.generate_monthly_sales_report()
        elif tab_name == "تقرير المخزون":
            self.generate_inventory_report()
        elif tab_name == "تقرير الأرباح":
            self.generate_profits_report()
        elif tab_name == "المنتجات الاكثر مبيعاً":
            self.generate_top_selling_products_report()
        else:
            # عرض رسالة أن التقرير قيد التطوير
            if tab_name == "تقرير العملاء" or tab_name == "تقرير الموردين":
                table = None
                if tab_name == "تقرير العملاء":
                    table = self.customers_table
                else:
                    table = self.suppliers_table

                table.setRowCount(0)
                table.insertRow(0)
                message_item = QTableWidgetItem(f"{tab_name} قيد التطوير...")
                message_item.setTextAlignment(Qt.AlignCenter)
                table.setSpan(0, 0, 1, table.columnCount())
                table.setItem(0, 0, message_item)

                # تصفير الإحصائيات
                if tab_name == "تقرير العملاء":
                    self.update_customers_stats(0, 0, 0)
                else:
                    self.update_suppliers_stats(0, 0, 0)

    def create_daily_sales_tab(self):
        """إنشاء تبويب المبيعات اليومية"""
        tab = QWidget()
        tab.setLayoutDirection(Qt.RightToLeft)
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(15, 10, 15, 10)  # توحيد الهوامش العلوية والسفلية

        # عنوان قسم التصفية - نقله للأعلى خارج الفريم
        filter_title = QLabel("خيارات التصفية")
        filter_title.setObjectName("filter_title")
        filter_title.setFont(QFont("Arial", 12, QFont.Bold))
        tab_layout.addWidget(filter_title)

        # إضافة مسافة بين العنوان والفريم
        tab_layout.addSpacing(5)

        # إنشاء إطار الفلتر لتبويب المبيعات اليومية
        filter_frame = QFrame()
        filter_frame.setObjectName("filter_frame")
        filter_frame.setFrameShape(QFrame.StyledPanel)
        filter_frame.setFrameShadow(QFrame.Raised)

        filter_layout = QVBoxLayout(filter_frame)
        filter_layout.setContentsMargins(10, 5, 10, 5)  # توحيد الهوامش العلوية والسفلية
        filter_layout.setSpacing(4)  # تقليل المسافة العمودية بين عناصر التخطيط العمودي

        # حذف عنوان قسم التصفية من داخل الفريم

        # استخدام Grid Layout لتنظيم حقول التصفية
        filter_grid = QGridLayout()
        filter_grid.setHorizontalSpacing(2)  # تقليل المسافة الأفقية من 8 إلى 2
        filter_grid.setVerticalSpacing(4)  # تقليل المسافة العمودية بين صفوف الحقول

        # تصفية حسب التاريخ - إزالة العنوان

        # 1. إضافة كومبوبوكس لليوم
        self.sales_day_combo = RTLComboBox()
        self.sales_day_combo.setObjectName("combo_box")
        self.sales_day_combo.setFixedHeight(28)
        self.sales_day_combo.setFixedWidth(50)  # تصغير عرض كومبو اليوم

        # إضافة الأيام من 1 إلى 31
        # إضافة خيار الكل أولاً
        self.sales_day_combo.addItem("الكل")
        for day in range(1, 32):
            self.sales_day_combo.addItem(str(day))

        # تعيين اليوم الحالي
        current_day = QDate.currentDate().day()
        self.sales_day_combo.setCurrentIndex(current_day)  # زيادة الفهرس بمقدار 1 بسبب إضافة عنصر "الكل"

        # 2. إضافة كومبوبوكس للشهر
        self.sales_month_combo = RTLComboBox()
        self.sales_month_combo.setObjectName("combo_box")
        self.sales_month_combo.setFixedHeight(28)
        self.sales_month_combo.setFixedWidth(90)  # عرض متوسط لكومبو الشهر

        # إضافة الأشهر العربية
        arabic_months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]
        self.sales_month_combo.addItems(arabic_months)

        # تعيين الشهر الحالي
        current_month = QDate.currentDate().month()
        self.sales_month_combo.setCurrentIndex(current_month - 1)

        # 3. إضافة كومبوبوكس للسنة
        self.sales_year_combo = RTLComboBox()
        self.sales_year_combo.setObjectName("combo_box")
        self.sales_year_combo.setFixedHeight(28)
        self.sales_year_combo.setFixedWidth(70)  # عرض مناسب لكومبو السنة

        # إضافة السنوات (من السنة الحالية إلى خمس سنوات قبلها)
        current_year = QDate.currentDate().year()
        for year in range(current_year, current_year - 6, -1):
            self.sales_year_combo.addItem(str(year))

        # تخطيط أفقي للتاريخ
        date_layout_horizontal = QHBoxLayout()
        date_layout_horizontal.setSpacing(2)  # تقليل المسافة بين الكومبوبوكسات
        date_layout_horizontal.addWidget(self.sales_day_combo)
        date_layout_horizontal.addWidget(self.sales_month_combo)
        date_layout_horizontal.addWidget(self.sales_year_combo)
        date_layout_horizontal.addSpacing(15)  # إضافة مسافة بين التاريخ والزر

        # إنشاء زر توليد التقرير في تخطيط التاريخ
        self.generate_sales_btn = QPushButton("🔍   عرض")
        self.generate_sales_btn.setFixedSize(92, 36)
        self.generate_sales_btn.setObjectName("action_button")
        font = QFont("Arial", 9)  # تقليل حجم الخط من 10 إلى 9
        font.setBold(False)
        self.generate_sales_btn.setFont(font)
        self.generate_sales_btn.setCursor(Qt.PointingHandCursor)
        self.generate_sales_btn.clicked.connect(self.generate_daily_sales_report)

        date_layout_horizontal.addWidget(self.generate_sales_btn)
        date_layout_horizontal.addStretch(1)  # تقليل مقدار التمديد لإفساح المجال لعناصر أخرى

        # إنشاء تخطيط عمودي لفلتر التاريخ مع هوامش صغيرة لتوسيطه
        date_layout = QVBoxLayout()
        date_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش الداخلية
        date_layout.addSpacing(8)  # إضافة مسافة ثابتة صغيرة في الأعلى
        date_layout.addLayout(date_layout_horizontal)  # إضافة التخطيط الأفقي للتاريخ والزر
        date_layout.addSpacing(8)  # إضافة مسافة ثابتة صغيرة في الأسفل

        # تصفية حسب العميل والمنتج - وضعهم في عمود واحد أقصى اليسار
        self.sales_client_input = QLineEdit()
        self.sales_client_input.setObjectName("search_input")
        self.sales_client_input.setPlaceholderText("العميل...")  # تعديل النص التوضيحي
        self.sales_client_input.setFixedHeight(30)
        self.sales_client_input.setFixedWidth(150)  # تضييق عرض حقل البحث عن العميل

        self.sales_product_input = QLineEdit()
        self.sales_product_input.setObjectName("search_input")
        self.sales_product_input.setPlaceholderText("المنتج...")  # تعديل النص التوضيحي
        self.sales_product_input.setFixedHeight(30)
        self.sales_product_input.setFixedWidth(150)  # تضييق عرض حقل البحث عن المنتج

        # إضافة مربع تعليم لعرض المنتجات غير المدفوعة فقط
        self.sales_unpaid_only_checkbox = QCheckBox("غير المدفوعة فقط")
        self.sales_unpaid_only_checkbox.setObjectName("filter_checkbox")
        self.sales_unpaid_only_checkbox.setFixedHeight(30)

        # إنشاء تخطيط عمودي للعميل والمنتج ومربع التعليم مع هوامش متساوية
        client_product_layout = QVBoxLayout()
        client_product_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش الداخلية
        client_product_layout.setSpacing(8)  # توحيد المسافة بين الحقلين
        client_product_layout.addItem(QSpacerItem(0, 1, QSizePolicy.Minimum, QSizePolicy.Expanding))  # إضافة مساحة مرنة في الأعلى
        client_product_layout.addWidget(self.sales_client_input, 0, Qt.AlignCenter)  # محاذاة العميل في المنتصف
        client_product_layout.addWidget(self.sales_product_input, 0, Qt.AlignCenter)  # محاذاة المنتج في المنتصف
        client_product_layout.addWidget(self.sales_unpaid_only_checkbox, 0, Qt.AlignCenter)  # محاذاة مربع التعليم في المنتصف
        client_product_layout.addItem(QSpacerItem(0, 1, QSizePolicy.Minimum, QSizePolicy.Expanding))  # إضافة مساحة مرنة في الأسفل

        # إضافة عناصر التصفية إلى التخطيط الشبكي
        filter_grid.addLayout(date_layout, 0, 0, 1, 1)  # التاريخ يأخذ عمود واحد
        filter_grid.addLayout(client_product_layout, 0, 1, 2, 1)  # العميل والمنتج يأخذان عمود واحد ويمتدان لصفين

        # إضافة التخطيط الشبكي إلى تخطيط الفلتر
        filter_layout.addLayout(filter_grid)

        # ربط أحداث التحديث التلقائي للفلاتر (بعد إنشاء العناصر)
        self.sales_client_input.textChanged.connect(self.generate_daily_sales_report)
        self.sales_product_input.textChanged.connect(self.generate_daily_sales_report)
        self.sales_unpaid_only_checkbox.stateChanged.connect(self.generate_daily_sales_report)

        tab_layout.addWidget(filter_frame)
        tab_layout.addSpacing(5)

        # حفظ مرجع لعنوان التقرير (سيتم استخدامه لتحديث التاريخ ولكن لن يتم عرضه)
        self.sales_section_title = QLabel("المبيعات اليومية")
        self.sales_section_title.setVisible(False)
        self.sales_date_label = QLabel("")
        self.sales_date_label.setVisible(False)

        # إنشاء جدول لعرض عناصر الفواتير
        self.sales_table = QTableWidget()
        self.sales_table.setObjectName("items_table")
        self.sales_table.setColumnCount(7)  # تقليل عدد الأعمدة من 8 إلى 7
        self.sales_table.setHorizontalHeaderLabels([
            "كود المنتج", "اسم المنتج", "السعر (ج.م)", "الكمية", "الإجمالي (ج.م)",
            "رقم الفاتورة", "العميل"  # حذف "حالة الفاتورة"
        ])

        # تعديل عرض الأعمدة - جعل عمود اسم المنتج أكبر
        header = self.sales_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)

        # تعيين عرض ثابت للأعمدة الأخرى
        self.sales_table.setColumnWidth(0, 100)  # كود المنتج
        self.sales_table.setColumnWidth(2, 100)  # السعر
        self.sales_table.setColumnWidth(3, 80)   # الكمية
        self.sales_table.setColumnWidth(4, 120)  # الإجمالي
        self.sales_table.setColumnWidth(5, 100)  # رقم الفاتورة
        self.sales_table.setColumnWidth(6, 120)  # العميل

        # جعل عمود اسم المنتج يأخذ المساحة المتبقية
        header.setSectionResizeMode(1, QHeaderView.Stretch)

        self.sales_table.verticalHeader().setVisible(False)
        self.sales_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.sales_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.sales_table.setAlternatingRowColors(False)

        # إضافة قائمة السياق (Context Menu) للجدول
        self.sales_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.sales_table.customContextMenuRequested.connect(self.show_sales_context_menu)

        tab_layout.addWidget(self.sales_table)

        # إضافة إحصائيات التقرير
        stats_layout = QHBoxLayout()

        self.sales_total_items_label = QLabel("إجمالي العناصر: 0")
        self.sales_total_items_label.setObjectName("stats_label")
        stats_layout.addWidget(self.sales_total_items_label)

        stats_layout.addStretch()

        self.sales_total_amount_label = QLabel("إجمالي المبالغ: 0.00 ج.م")
        self.sales_total_amount_label.setObjectName("stats_label")
        stats_layout.addWidget(self.sales_total_amount_label)

        stats_layout.addSpacing(20)

        self.sales_total_paid_label = QLabel("إجمالي المدفوع: 0.00 ج.م")
        self.sales_total_paid_label.setObjectName("stats_label")
        self.sales_total_paid_label.setStyleSheet("color: #27ae60; font-weight: bold;")  # لون أخضر للمدفوع
        stats_layout.addWidget(self.sales_total_paid_label)

        stats_layout.addSpacing(20)

        self.sales_total_debt_label = QLabel("إجمالي الدين: 0.00 ج.م")
        self.sales_total_debt_label.setObjectName("stats_label")
        self.sales_total_debt_label.setStyleSheet("color: #e74c3c; font-weight: bold;")  # لون أحمر للدين
        stats_layout.addWidget(self.sales_total_debt_label)

        stats_layout.addSpacing(20)

        self.sales_total_quantity_label = QLabel("إجمالي الكميات: 0")
        self.sales_total_quantity_label.setObjectName("stats_label")
        stats_layout.addWidget(self.sales_total_quantity_label)

        tab_layout.addLayout(stats_layout)

        return tab

    def show_sales_context_menu(self, position):
        """عرض قائمة السياق عند النقر بزر الماوس الأيمن على جدول المبيعات اليومية"""
        try:
            # التحقق من وجود صف محدد
            row = self.sales_table.currentRow()
            if row < 0:
                return

            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            user_id = None

            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')

            # استيراد وحدة التحكم بالمستخدمين
            from controllers.user_controller import UserController

            # الحصول على رقم الفاتورة من الصف المحدد
            invoice_ref_item = self.sales_table.item(row, 5)  # عمود رقم الفاتورة
            if not invoice_ref_item:
                return

            invoice_ref = invoice_ref_item.text()

            # إنشاء قائمة السياق
            context_menu = QMenu(self)
            context_menu.setLayoutDirection(Qt.RightToLeft)

            # إضافة عنوان القائمة
            title_action = QAction(f"الفاتورة: {invoice_ref}", self)
            title_action.setEnabled(False)
            title_font = title_action.font()
            title_font.setBold(True)
            title_action.setFont(title_font)
            context_menu.addAction(title_action)

            # إضافة فاصل
            context_menu.addSeparator()

            # التحقق من حالة الفاتورة وحالة العميل
            from models.invoices import InvoiceModel
            invoice_data = InvoiceModel.get_invoice_by_reference(invoice_ref)

            if invoice_data:
                invoice_status = invoice_data.get('status')
                customer_name = invoice_data.get('customer_name', '')
                is_registered_customer = customer_name and customer_name.strip() != '' and customer_name != 'عميل غير مسجل'

                # إضافة الأزرار حسب حالة الفاتورة والعميل
                if invoice_status == 'غير مدفوعة':
                    # زر "دفع" للفواتير غير المدفوعة
                    paid_action = QAction("💰  تحويل إلى مدفوع", self)
                    paid_action.triggered.connect(lambda: self.mark_invoice_as_paid(invoice_ref))
                    context_menu.addAction(paid_action)

                elif invoice_status == 'مدفوعة' and is_registered_customer:
                    # زر "تأجيل" للفواتير المدفوعة مع عملاء مسجلين فقط
                    defer_action = QAction("⏳  تأجيل الدفع", self)
                    defer_action.triggered.connect(lambda: self.mark_invoice_as_deferred(invoice_ref))
                    context_menu.addAction(defer_action)

                # إضافة فاصل إذا تم إضافة أي زر
                if (invoice_status == 'غير مدفوعة') or (invoice_status == 'مدفوعة' and is_registered_customer):
                    context_menu.addSeparator()

            # إضافة خيار حذف العملية - فقط إذا كان المستخدم لديه صلاحية
            if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "حذف عملية بيع"):
                delete_action = context_menu.addAction("🗑️  حذف العملية")
                delete_action.setIcon(QIcon("ui/icons/delete.png"))  # يمكن تغيير مسار الأيقونة حسب الحاجة

            # عرض القائمة وتنفيذ الإجراء المناسب عند اختيار أحد الخيارات
            action = context_menu.exec_(self.sales_table.viewport().mapToGlobal(position))

            # التحقق من الإجراء المحدد
            if 'delete_action' in locals() and action == delete_action:
                self.delete_sale_item(row)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض قائمة السياق: {str(e)}")

    def delete_sale_item(self, row):
        """حذف عملية البيع المحددة"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية حذف عملية بيع
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "حذف عملية بيع", show_message=True, parent_widget=self):
                    return

            # الحصول على بيانات الصف المحدد
            product_code = self.sales_table.item(row, 0).text()
            product_name = self.sales_table.item(row, 1).text()
            invoice_number = self.sales_table.item(row, 5).text()
            customer_name = self.sales_table.item(row, 6).text()
            quantity = int(self.sales_table.item(row, 3).text())
            total_price = float(self.sales_table.item(row, 4).text().replace(',', ''))
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")
            return

        # عرض رسالة تأكيد
        confirmation = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المنتج '{product_name}' من الفاتورة رقم {invoice_number}؟\n\n"
            f"سيتم إعادة الكمية ({quantity}) إلى المخزون وتحديث حساب العميل '{customer_name}'.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirmation == QMessageBox.Yes:
            try:
                # استدعاء المحرك لحذف المنتج من الفاتورة وتحديث المخزون وحساب العميل
                from models.invoices import InvoiceModel
                from models.products import ProductModel
                from models.customers import CustomerModel

                # الحصول على معرّف الفاتورة من الرقم المرجعي
                invoice_id = InvoiceModel.get_invoice_id_by_reference(invoice_number)

                if not invoice_id:
                    QMessageBox.warning(self, "خطأ", "لا يمكن العثور على الفاتورة المحددة.")
                    return

                # حذف المنتج من الفاتورة
                success = InvoiceModel.delete_invoice_item(invoice_id, product_code, quantity)

                if success:
                    # إعادة المنتج إلى المخزون
                    ProductModel.update_stock(product_code, quantity, 'add')

                    # تحديث حساب العميل
                    if customer_name != "عميل نقدي":
                        CustomerModel.update_account(customer_name, -total_price)

                    # التحقق مما إذا كانت الفاتورة فارغة الآن
                    remaining_items = InvoiceModel.get_invoice_items_count(invoice_id)

                    if remaining_items == 0:
                        # حذف الفاتورة بالكامل إذا لم يتبق أي عناصر
                        InvoiceModel.delete_invoice(invoice_id)
                        QMessageBox.information(self, "نجاح", "تم حذف العملية وإلغاء الفاتورة بالكامل بنجاح.")
                    else:
                        QMessageBox.information(self, "نجاح", "تم حذف العملية بنجاح.")

                    # إعادة تحميل التقرير
                    self.generate_daily_sales_report()

                    # تحديث تاب العملاء ونافذة تفاصيل العميل
                    self.refresh_customer_related_views()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في حذف العملية. يرجى المحاولة مرة أخرى.")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف العملية: {str(e)}")
                print(f"Error in delete_sale_item: {str(e)}")

    def refresh_customer_related_views(self):
        """تحديث تاب العملاء ونوافذ تفاصيل العملاء المفتوحة"""
        try:
            # البحث عن النافذة الرئيسية
            main_window = self.window()
            if hasattr(main_window, 'content_widget'):
                # تحديث تاب العملاء
                from views.customers import CustomersView
                for i in range(main_window.content_widget.count()):
                    widget = main_window.content_widget.widget(i)
                    if isinstance(widget, CustomersView):
                        widget.refresh_customers_table()
                        break

            # تحديث جميع نوافذ تفاصيل العملاء المفتوحة
            from PyQt5.QtWidgets import QApplication
            from views.customers import CustomerDetailsDialog
            for widget in QApplication.allWidgets():
                if isinstance(widget, CustomerDetailsDialog) and widget.isVisible():
                    widget.refresh_customer_data()

        except Exception as e:
            print(f"[ERROR] خطأ في تحديث واجهات العملاء: {str(e)}")

    def refresh_invoices_tab(self):
        """تحديث تاب الفواتير إذا كان موجوداً"""
        try:
            # البحث عن النافذة الرئيسية
            main_window = None
            parent = self.parent()

            while parent:
                if hasattr(parent, 'invoices_view'):
                    main_window = parent
                    break
                parent = parent.parent()

            # تحديث تاب الفواتير إذا كان موجوداً
            if main_window and hasattr(main_window, 'invoices_view'):
                if hasattr(main_window.invoices_view, 'populate_invoices_table'):
                    main_window.invoices_view.populate_invoices_table()
                    print("[INFO] تم تحديث تاب الفواتير")

        except Exception as e:
            print(f"[ERROR] خطأ في تحديث تاب الفواتير: {str(e)}")

    def show_monthly_context_menu(self, position):
        """عرض قائمة السياق عند النقر بزر الماوس الأيمن على جدول المبيعات الشهرية"""
        try:
            # التحقق من وجود صف محدد
            row = self.monthly_table.currentRow()
            if row < 0:
                return

            # الحصول على رقم الفاتورة من الصف المحدد
            invoice_ref_item = self.monthly_table.item(row, 5)  # عمود رقم الفاتورة
            if not invoice_ref_item:
                return

            invoice_ref = invoice_ref_item.text()

            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            user_id = None

            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')

            # إنشاء قائمة السياق
            context_menu = QMenu(self)
            context_menu.setLayoutDirection(Qt.RightToLeft)

            # إضافة عنوان القائمة
            title_action = QAction(f"الفاتورة: {invoice_ref}", self)
            title_action.setEnabled(False)
            title_font = title_action.font()
            title_font.setBold(True)
            title_action.setFont(title_font)
            context_menu.addAction(title_action)

            # إضافة فاصل
            context_menu.addSeparator()

            # التحقق من حالة الفاتورة وحالة العميل
            from models.invoices import InvoiceModel
            invoice_data = InvoiceModel.get_invoice_by_reference(invoice_ref)

            if invoice_data:
                invoice_status = invoice_data.get('status')
                customer_name = invoice_data.get('customer_name', '')
                is_registered_customer = customer_name and customer_name.strip() != '' and customer_name != 'عميل غير مسجل'

                # إضافة الأزرار حسب حالة الفاتورة والعميل
                if invoice_status == 'غير مدفوعة':
                    # زر "دفع" للفواتير غير المدفوعة
                    paid_action = QAction("💰  تحويل إلى مدفوع", self)
                    paid_action.triggered.connect(lambda: self.mark_invoice_as_paid(invoice_ref))
                    context_menu.addAction(paid_action)

                elif invoice_status == 'مدفوعة' and is_registered_customer:
                    # زر "تأجيل" للفواتير المدفوعة مع عملاء مسجلين فقط
                    defer_action = QAction("⏳  تأجيل الدفع", self)
                    defer_action.triggered.connect(lambda: self.mark_invoice_as_deferred(invoice_ref))
                    context_menu.addAction(defer_action)

            # عرض القائمة
            context_menu.exec_(self.monthly_table.viewport().mapToGlobal(position))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض قائمة السياق: {str(e)}")

    def mark_invoice_as_paid(self, invoice_ref):
        """تحويل الفاتورة من غير مدفوعة إلى مدفوعة مع تحديث ذكي للحالة"""
        try:
            # عرض رسالة تأكيد
            confirmation = QMessageBox.question(
                self,
                "تأكيد تحويل الفاتورة إلى مدفوعة",
                f"هل أنت متأكد من تحويل الفاتورة رقم {invoice_ref} إلى مدفوعة؟\n\n"
                f"سيتم تحديث حالة الفاتورة وحساب العميل.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if confirmation == QMessageBox.Yes:
                # استيراد النماذج المطلوبة
                from models.invoices import InvoiceModel

                # الحصول على معرف الفاتورة من الرقم المرجعي
                invoice_id = InvoiceModel.get_invoice_id_by_reference(invoice_ref)

                if not invoice_id:
                    QMessageBox.warning(self, "خطأ", "لا يمكن العثور على الفاتورة المحددة.")
                    return

                # الحصول على بيانات الفاتورة
                invoice = InvoiceModel.get_invoice_by_id(invoice_id)
                if not invoice:
                    QMessageBox.warning(self, "خطأ", "لا يمكن العثور على بيانات الفاتورة.")
                    return

                # التحقق من أن الفاتورة غير مدفوعة
                if invoice.get('status') == 'مدفوعة':
                    QMessageBox.information(self, "تنبيه", "الفاتورة مدفوعة بالفعل.")
                    return

                # الحصول على عناصر الفاتورة لتحديد الحالة الجديدة
                invoice_items = InvoiceModel.get_invoice_items(invoice_id)
                total_amount = invoice.get('total', 0)

                # تحديث حالة الفاتورة بناءً على عدد العناصر
                if len(invoice_items) > 1:
                    # إذا كانت الفاتورة تحتوي على أكثر من منتج، تحويلها إلى "مدفوع جزئياً"
                    new_status = 'مدفوع جزئياً'
                    paid_amount = total_amount / 2  # دفع جزئي (يمكن تحسين هذا المنطق)
                    remaining_amount = total_amount - paid_amount
                else:
                    # إذا كانت الفاتورة تحتوي على منتج واحد فقط، تحويلها إلى "مدفوعة"
                    new_status = 'مدفوعة'
                    paid_amount = total_amount
                    remaining_amount = 0

                update_data = {
                    'customer_id': invoice.get('customer_id'),
                    'paid_amount': paid_amount,
                    'remaining_amount': remaining_amount,
                    'status': new_status,
                    'total': total_amount
                }

                success = InvoiceModel.update_invoice(invoice_id, update_data)

                if success:
                    QMessageBox.information(self, "نجاح", f"تم تحويل الفاتورة رقم {invoice_ref} إلى {new_status} بنجاح.")

                    # إعادة تحميل التقارير
                    current_tab_index = self.reports_tabs.currentIndex()
                    if current_tab_index == 0:  # تاب المبيعات اليومية
                        self.generate_daily_sales_report()
                    elif current_tab_index == 1:  # تاب المبيعات الشهرية
                        self.generate_monthly_sales_report()

                    # تحديث تاب العملاء ونوافذ تفاصيل العملاء
                    self.refresh_customer_related_views()

                    # تحديث تاب الفواتير إذا كان موجوداً
                    self.refresh_invoices_tab()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في تحديث حالة الفاتورة. يرجى المحاولة مرة أخرى.")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحويل الفاتورة إلى مدفوعة: {str(e)}")
            print(f"Error in mark_invoice_as_paid: {str(e)}")

    def mark_invoice_as_deferred(self, invoice_ref):
        """تأجيل دفع الفاتورة وإضافتها إلى دين العميل"""
        try:
            # عرض رسالة تأكيد
            confirmation = QMessageBox.question(
                self,
                "تأكيد تأجيل الدفع",
                f"هل أنت متأكد من تأجيل دفع الفاتورة رقم {invoice_ref}؟\n\n"
                f"سيتم تحويل الفاتورة إلى غير مدفوعة وإضافة المبلغ إلى دين العميل.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if confirmation == QMessageBox.Yes:
                # استيراد النماذج المطلوبة
                from models.invoices import InvoiceModel

                # الحصول على معرف الفاتورة من الرقم المرجعي
                invoice_id = InvoiceModel.get_invoice_id_by_reference(invoice_ref)

                if not invoice_id:
                    QMessageBox.warning(self, "خطأ", "لا يمكن العثور على الفاتورة المحددة.")
                    return

                # الحصول على بيانات الفاتورة
                invoice = InvoiceModel.get_invoice_by_id(invoice_id)
                if not invoice:
                    QMessageBox.warning(self, "خطأ", "لا يمكن العثور على بيانات الفاتورة.")
                    return

                # التحقق من أن الفاتورة مدفوعة
                if invoice.get('status') != 'مدفوعة':
                    QMessageBox.information(self, "تنبيه", "الفاتورة غير مدفوعة بالفعل.")
                    return

                # التحقق من وجود عميل مسجل
                customer_name = invoice.get('customer_name', '')
                if not customer_name or customer_name.strip() == '' or customer_name == 'عميل غير مسجل':
                    QMessageBox.warning(self, "خطأ", "لا يمكن تأجيل الدفع للعملاء غير المسجلين.")
                    return

                # تحديث حالة الفاتورة إلى غير مدفوعة
                total_amount = invoice.get('total', 0)
                update_data = {
                    'customer_id': invoice.get('customer_id'),
                    'paid_amount': 0,
                    'remaining_amount': total_amount,
                    'status': 'غير مدفوعة',
                    'total': total_amount
                }

                success = InvoiceModel.update_invoice(invoice_id, update_data)

                if success:
                    QMessageBox.information(self, "نجاح", f"تم تأجيل دفع الفاتورة رقم {invoice_ref} بنجاح.\nتم إضافة المبلغ إلى دين العميل {customer_name}.")

                    # إعادة تحميل التقارير
                    current_tab_index = self.reports_tabs.currentIndex()
                    if current_tab_index == 0:  # تاب المبيعات اليومية
                        self.generate_daily_sales_report()
                    elif current_tab_index == 1:  # تاب المبيعات الشهرية
                        self.generate_monthly_sales_report()

                    # تحديث تاب العملاء ونوافذ تفاصيل العملاء
                    self.refresh_customer_related_views()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في تأجيل دفع الفاتورة. يرجى المحاولة مرة أخرى.")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تأجيل دفع الفاتورة: {str(e)}")
            print(f"Error in mark_invoice_as_deferred: {str(e)}")

    def create_monthly_sales_tab(self):
        """إنشاء تبويب المبيعات الشهرية"""
        tab = QWidget()
        tab.setLayoutDirection(Qt.RightToLeft)
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(15, 10, 15, 10)  # توحيد الهوامش العلوية والسفلية

        # عنوان قسم التصفية - نقله للأعلى خارج الفريم
        filter_title = QLabel("خيارات التصفية")
        filter_title.setObjectName("filter_title")
        filter_title.setFont(QFont("Arial", 12, QFont.Bold))
        tab_layout.addWidget(filter_title)

        # إضافة مسافة بين العنوان والفريم
        tab_layout.addSpacing(5)

        # إنشاء إطار الفلتر لتبويب المبيعات الشهرية
        filter_frame = QFrame()
        filter_frame.setObjectName("filter_frame")
        filter_frame.setFrameShape(QFrame.StyledPanel)
        filter_frame.setFrameShadow(QFrame.Raised)

        filter_layout = QVBoxLayout(filter_frame)
        filter_layout.setContentsMargins(10, 5, 10, 5)  # توحيد الهوامش العلوية والسفلية
        filter_layout.setSpacing(4)

        # حذف عنوان قسم التصفية من داخل الفريم

        # استخدام Grid Layout لتنظيم حقول التصفية
        filter_grid = QGridLayout()
        filter_grid.setHorizontalSpacing(2)  # تقليل المسافة الأفقية من 8 إلى 2
        filter_grid.setVerticalSpacing(4)  # تقليل المسافة العمودية بين صفوف الحقول

        # تصفية حسب الشهر والسنة - إزالة العناوين

        self.monthly_month_combo = RTLComboBox()
        self.monthly_month_combo.setObjectName("combo_box")
        self.monthly_month_combo.setFixedHeight(28)
        self.monthly_month_combo.setFixedWidth(90)  # تحديد عرض ثابت لكومبو الشهر

        # إضافة الأشهر العربية
        arabic_months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]
        self.monthly_month_combo.addItems(arabic_months)

        # تعيين الشهر الحالي
        current_month = QDate.currentDate().month()
        self.monthly_month_combo.setCurrentIndex(current_month - 1)

        self.monthly_year_combo = RTLComboBox()
        self.monthly_year_combo.setObjectName("combo_box")
        self.monthly_year_combo.setFixedHeight(28)
        self.monthly_year_combo.setFixedWidth(70)  # تحديد عرض ثابت لكومبو السنة

        # إضافة السنوات (من السنة الحالية إلى خمس سنوات قبلها)
        current_year = QDate.currentDate().year()
        for year in range(current_year, current_year - 5, -1):
            self.monthly_year_combo.addItem(str(year))

        # تصفية حسب العميل - إزالة العنوان

        self.monthly_client_input = QLineEdit()
        self.monthly_client_input.setObjectName("search_input")
        self.monthly_client_input.setPlaceholderText("العميل...")  # تعديل النص التوضيحي
        self.monthly_client_input.setFixedHeight(30)
        self.monthly_client_input.setFixedWidth(150)  # تضييق عرض حقل البحث عن العميل

        # تصفية حسب المنتج - إزالة العنوان

        self.monthly_product_input = QLineEdit()
        self.monthly_product_input.setObjectName("search_input")
        self.monthly_product_input.setPlaceholderText("المنتج...")  # تعديل النص التوضيحي
        self.monthly_product_input.setFixedHeight(30)
        self.monthly_product_input.setFixedWidth(150)  # تضييق عرض حقل البحث عن المنتج

        # إضافة مربع تعليم لعرض المنتجات غير المدفوعة فقط
        self.monthly_unpaid_only_checkbox = QCheckBox("غير المدفوعة فقط")
        self.monthly_unpaid_only_checkbox.setObjectName("filter_checkbox")
        self.monthly_unpaid_only_checkbox.setFixedHeight(30)

        # تخطيط أفقي للتاريخ (الشهر والسنة)
        date_layout_horizontal = QHBoxLayout()
        date_layout_horizontal.setSpacing(2)  # تقليل المسافة بين الكومبوبوكسات
        date_layout_horizontal.addWidget(self.monthly_month_combo)
        date_layout_horizontal.addWidget(self.monthly_year_combo)
        date_layout_horizontal.addSpacing(15)  # إضافة مسافة بين التاريخ والزر

        # إنشاء زر توليد التقرير في تخطيط التاريخ
        self.generate_monthly_btn = QPushButton("🔍   عرض")
        self.generate_monthly_btn.setFixedSize(92, 36)
        self.generate_monthly_btn.setObjectName("action_button")
        font = QFont("Arial", 9)  # تقليل حجم الخط من 10 إلى 9
        font.setBold(False)
        self.generate_monthly_btn.setFont(font)
        self.generate_monthly_btn.setCursor(Qt.PointingHandCursor)
        self.generate_monthly_btn.clicked.connect(self.generate_monthly_sales_report)

        date_layout_horizontal.addWidget(self.generate_monthly_btn)
        date_layout_horizontal.addStretch(1)  # تقليل مقدار التمديد

        # إنشاء تخطيط عمودي لفلتر التاريخ مع هوامش صغيرة لتوسيطه
        date_layout = QVBoxLayout()
        date_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش الداخلية
        date_layout.addSpacing(8)  # إضافة مسافة ثابتة صغيرة في الأعلى
        date_layout.addLayout(date_layout_horizontal)  # إضافة التخطيط الأفقي للتاريخ والزر
        date_layout.addSpacing(8)  # إضافة مسافة ثابتة صغيرة في الأسفل

        # إنشاء تخطيط عمودي للعميل والمنتج ومربع التعليم مع هوامش متساوية
        client_product_layout = QVBoxLayout()
        client_product_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش الداخلية
        client_product_layout.setSpacing(8)  # توحيد المسافة بين الحقلين
        client_product_layout.addItem(QSpacerItem(0, 1, QSizePolicy.Minimum, QSizePolicy.Expanding))  # إضافة مساحة مرنة في الأعلى
        client_product_layout.addWidget(self.monthly_client_input, 0, Qt.AlignCenter)  # محاذاة العميل في المنتصف
        client_product_layout.addWidget(self.monthly_product_input, 0, Qt.AlignCenter)  # محاذاة المنتج في المنتصف
        client_product_layout.addWidget(self.monthly_unpaid_only_checkbox, 0, Qt.AlignCenter)  # محاذاة مربع التعليم في المنتصف
        client_product_layout.addItem(QSpacerItem(0, 1, QSizePolicy.Minimum, QSizePolicy.Expanding))  # إضافة مساحة مرنة في الأسفل

        # إضافة عناصر التصفية إلى التخطيط الشبكي
        filter_grid.addLayout(date_layout, 0, 0, 1, 1)  # التاريخ يأخذ عمود واحد
        filter_grid.addLayout(client_product_layout, 0, 1, 2, 1)  # العميل والمنتج يأخذان عمود واحد ويمتدان لصفين

        # إضافة التخطيط الشبكي إلى تخطيط الفلتر
        filter_layout.addLayout(filter_grid)

        # ربط أحداث التحديث التلقائي للفلاتر (بعد إنشاء العناصر)
        self.monthly_client_input.textChanged.connect(self.generate_monthly_sales_report)
        self.monthly_product_input.textChanged.connect(self.generate_monthly_sales_report)
        self.monthly_unpaid_only_checkbox.stateChanged.connect(self.generate_monthly_sales_report)

        tab_layout.addWidget(filter_frame)
        tab_layout.addSpacing(5)

        # حفظ مرجع لعنوان التقرير (سيتم استخدامه لتحديث التاريخ ولكن لن يتم عرضه)
        self.monthly_section_title = QLabel("المبيعات الشهرية")
        self.monthly_section_title.setVisible(False)
        self.monthly_date_label = QLabel("")
        self.monthly_date_label.setVisible(False)

        # إنشاء جدول لعرض عناصر الفواتير
        self.monthly_table = QTableWidget()
        self.monthly_table.setObjectName("items_table")
        self.monthly_table.setColumnCount(7)  # تقليل عدد الأعمدة من 8 إلى 7
        self.monthly_table.setHorizontalHeaderLabels([
            "كود المنتج", "اسم المنتج", "السعر (ج.م)", "الكمية", "الإجمالي (ج.م)",
            "رقم الفاتورة", "العميل"  # حذف "حالة الفاتورة"
        ])

        # تعديل عرض الأعمدة - جعل عمود اسم المنتج أكبر
        header = self.monthly_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)

        # تعيين عرض ثابت للأعمدة الأخرى
        self.monthly_table.setColumnWidth(0, 100)  # كود المنتج
        self.monthly_table.setColumnWidth(2, 100)  # السعر
        self.monthly_table.setColumnWidth(3, 80)   # الكمية
        self.monthly_table.setColumnWidth(4, 120)  # الإجمالي
        self.monthly_table.setColumnWidth(5, 100)  # رقم الفاتورة
        self.monthly_table.setColumnWidth(6, 120)  # العميل

        # جعل عمود اسم المنتج يأخذ المساحة المتبقية
        header.setSectionResizeMode(1, QHeaderView.Stretch)

        self.monthly_table.verticalHeader().setVisible(False)
        self.monthly_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.monthly_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.monthly_table.setAlternatingRowColors(False)

        # إضافة قائمة السياق (Context Menu) للجدول الشهري
        self.monthly_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.monthly_table.customContextMenuRequested.connect(self.show_monthly_context_menu)

        tab_layout.addWidget(self.monthly_table)

        # إضافة إحصائيات التقرير
        stats_layout = QHBoxLayout()

        self.monthly_total_items_label = QLabel("إجمالي العناصر: 0")
        self.monthly_total_items_label.setObjectName("stats_label")
        stats_layout.addWidget(self.monthly_total_items_label)

        stats_layout.addStretch()

        self.monthly_total_amount_label = QLabel("إجمالي المبالغ: 0.00 ج.م")
        self.monthly_total_amount_label.setObjectName("stats_label")
        stats_layout.addWidget(self.monthly_total_amount_label)

        stats_layout.addSpacing(20)

        self.monthly_total_paid_label = QLabel("إجمالي المدفوع: 0.00 ج.م")
        self.monthly_total_paid_label.setObjectName("stats_label")
        self.monthly_total_paid_label.setStyleSheet("color: #27ae60; font-weight: bold;")  # لون أخضر للمدفوع
        stats_layout.addWidget(self.monthly_total_paid_label)

        stats_layout.addSpacing(20)

        self.monthly_total_debt_label = QLabel("إجمالي الدين: 0.00 ج.م")
        self.monthly_total_debt_label.setObjectName("stats_label")
        self.monthly_total_debt_label.setStyleSheet("color: #e74c3c; font-weight: bold;")  # لون أحمر للدين
        stats_layout.addWidget(self.monthly_total_debt_label)

        stats_layout.addSpacing(20)

        self.monthly_total_quantity_label = QLabel("إجمالي الكميات: 0")
        self.monthly_total_quantity_label.setObjectName("stats_label")
        stats_layout.addWidget(self.monthly_total_quantity_label)

        tab_layout.addLayout(stats_layout)

        return tab

    def generate_daily_sales_report(self):
        """توليد تقرير المبيعات اليومية"""
        # إعادة ضبط الجدول
        self.sales_table.setRowCount(0)

        # الحصول على قيم التصفية
        selected_day_text = self.sales_day_combo.currentText()
        selected_month_idx = self.sales_month_combo.currentIndex() + 1  # تحويل الفهرس إلى رقم الشهر (1-12)
        selected_year = int(self.sales_year_combo.currentText())

        # تم إزالة فلتر حالة الفاتورة بناء على طلب المستخدم
        # invoice_status = self.sales_status_combo.currentText() if self.sales_status_combo.currentText() != "الكل" else None
        client_name = self.sales_client_input.text() if self.sales_client_input.text() else None
        product_name = self.sales_product_input.text() if self.sales_product_input.text() else None

        # إنشاء كائنات QDate للتاريخ المحدد
        if selected_day_text == "الكل":
            # إذا كان الاختيار "الكل"، استخدم أول يوم من الشهر
            from_date = QDate(selected_year, selected_month_idx, 1)
            # حساب آخر يوم في الشهر
            if selected_month_idx == 12:
                to_date = QDate(selected_year, selected_month_idx, 31)
            else:
                # الشهر التالي اليوم الأول ناقص يوم واحد = آخر يوم في الشهر الحالي
                to_date = QDate(selected_year, selected_month_idx + 1, 1).addDays(-1)

            # تعديل عنوان التقرير ليظهر الشهر كاملا
            report_title = f"المبيعات الشهرية - {self.sales_month_combo.currentText()} {selected_year}"
        else:
            # إذا كان اختيار يوم محدد
            selected_day = int(selected_day_text)
            from_date = QDate(selected_year, selected_month_idx, selected_day)
            to_date = from_date  # افتراضيًا، التقرير ليوم واحد فقط

            # التحقق من صحة التاريخ (مثل 30 فبراير غير صالح)
            if not from_date.isValid():
                QMessageBox.warning(
                    self,
                    "تاريخ غير صالح",
                    "التاريخ المحدد غير صالح. سيتم استخدام التاريخ الحالي بدلاً منه.",
                    QMessageBox.Ok
                )
                from_date = QDate.currentDate()
                to_date = from_date

            report_title = f"المبيعات اليومية - {from_date.toString('yyyy-MM-dd')}"

        # تحويل التواريخ إلى سلاسل نصية بالتنسيق YYYY-MM-DD
        from_date_str = from_date.toString("yyyy-MM-dd")
        to_date_str = to_date.toString("yyyy-MM-dd")

        # عرض تاريخ التقرير في العنوان
        self.sales_section_title.setText(report_title)

        # الحصول على قيم التصفية
        customer_filter = self.sales_client_input.text().lower()
        product_filter = self.sales_product_input.text().lower()
        unpaid_only_filter = self.sales_unpaid_only_checkbox.isChecked()
        # تم إزالة فلتر حالة الفاتورة
        # status_filter = self.sales_status_combo.currentText()

        # استخدام نموذج التقارير للحصول على البيانات الفعلية
        from models.reports import ReportModel
        from models.invoices import InvoiceModel

        # الحصول على تقرير المبيعات اليومية
        daily_report = ReportModel.get_daily_sales_report(from_date_str, to_date_str)

        # متغيرات للإحصائيات
        total_items = 0
        total_amount = 0
        total_quantity = 0

        # الحصول على إحصائيات المدفوع والدين من التقرير
        summary = daily_report.get('summary', {})
        total_paid = summary.get('paid_amount', 0) or 0
        total_debt = summary.get('unpaid_amount', 0) or 0

        # إذا كان لدينا بيانات مبيعات
        sales_data = daily_report.get('sales', [])

        # تم إزالة فلترة المبيعات حسب الحالة
        # if status_filter != "الكل":
        #     sales_data = [invoice for invoice in sales_data if invoice.get('status') == status_filter]

        # فلترة المبيعات حسب اسم العميل إذا تم تحديده
        if customer_filter:
            sales_data = [invoice for invoice in sales_data if invoice.get('customer_name') and customer_filter in invoice.get('customer_name', '').lower()]

        # فلترة المبيعات حسب حالة الدفع إذا تم تحديد "غير المدفوعة فقط"
        if unpaid_only_filter:
            sales_data = [invoice for invoice in sales_data if invoice.get('status') == 'غير مدفوعة']

        print(f"Invoices after initial filtering (status, customer, unpaid): {len(sales_data)}")

        # متغير للتبديل بين لوني الصفوف
        row_color_toggle = True
        current_invoice_id = None

        # إضافة قائمة السياق (Context Menu) للجدول
        self.sales_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.sales_table.customContextMenuRequested.connect(self.show_sales_context_menu)

        # إضافة العناصر إلى الجدول
        for invoice in sales_data:
            invoice_id = invoice.get('id')

            # الحصول على عناصر الفاتورة
            invoice_items = InvoiceModel.get_invoice_items(invoice_id)
            print(f"  Invoice ID {invoice_id}: Original items count = {len(invoice_items)}")

            # فلترة العناصر حسب اسم المنتج إذا تم تحديده
            if product_filter:
                invoice_items = [item for item in invoice_items if item.get('product_name') and product_filter in item.get('product_name', '').lower()]
                print(f"  Invoice ID {invoice_id}: Items count after product filter ('{product_filter}') = {len(invoice_items)}")

            # إذا كانت الفاتورة تحتوي على عناصر بعد الفلترة
            if invoice_items:
                # تبديل اللون عند تغيير الفاتورة
                if current_invoice_id != invoice_id:
                    current_invoice_id = invoice_id
                    row_color_toggle = not row_color_toggle

                # Usar un color de fondo uniforme para todas las filas
                row_background_color = QColor(248, 250, 252)  # #f8fafc

                # إضافة عناصر الفاتورة إلى الجدول
                for item in invoice_items:
                    # إضافة صف جديد
                    row = self.sales_table.rowCount()
                    self.sales_table.insertRow(row)

                    # كود المنتج
                    product_code = item.get('product_code', '')
                    product_id_item = QTableWidgetItem(product_code)
                    product_id_item.setTextAlignment(Qt.AlignCenter)
                    product_id_item.setBackground(row_background_color)
                    self.sales_table.setItem(row, 0, product_id_item)

                    # اسم المنتج
                    product_name = item.get('product_name', '')
                    name_item = QTableWidgetItem(product_name)
                    name_item.setTextAlignment(Qt.AlignCenter)
                    name_item.setBackground(row_background_color)
                    self.sales_table.setItem(row, 1, name_item)

                    # سعر المنتج
                    unit_price = item.get('unit_price', 0)
                    price_item = QTableWidgetItem(f"{unit_price:.2f}")
                    price_item.setTextAlignment(Qt.AlignCenter)
                    price_item.setBackground(row_background_color)
                    self.sales_table.setItem(row, 2, price_item)

                    # الكمية
                    quantity = item.get('quantity', 0)
                    quantity_item = QTableWidgetItem(str(quantity))
                    quantity_item.setTextAlignment(Qt.AlignCenter)
                    quantity_item.setBackground(row_background_color)
                    self.sales_table.setItem(row, 3, quantity_item)

                    # إجمالي السعر
                    total_price = item.get('total_price', 0)
                    total_item = QTableWidgetItem(f"{total_price:.2f}")
                    total_item.setTextAlignment(Qt.AlignCenter)
                    total_item.setBackground(row_background_color)
                    self.sales_table.setItem(row, 4, total_item)

                    # رقم الفاتورة
                    ref_number = invoice.get('reference_number', str(invoice_id))
                    invoice_id_item = QTableWidgetItem(ref_number)
                    invoice_id_item.setTextAlignment(Qt.AlignCenter)
                    invoice_id_item.setBackground(row_background_color)
                    self.sales_table.setItem(row, 5, invoice_id_item)

                    # العميل
                    customer_name = invoice.get('customer_name', '')
                    # التحقق من وجود اسم العميل، إذا كان فارغاً نعرض "عميل غير مسجل"
                    if not customer_name or customer_name.strip() == '':
                        customer_name = "عميل غير مسجل"
                    customer_item = QTableWidgetItem(customer_name)
                    customer_item.setBackground(row_background_color)
                    self.sales_table.setItem(row, 6, customer_item)

                    # تحديث الإحصائيات
                    total_items += 1
                    total_amount += total_price
                    total_quantity += quantity

        # تحديث إحصائيات التقرير
        self.update_sales_stats(total_items, total_amount, total_quantity, total_paid, total_debt)

    def generate_inventory_report(self):
        """توليد تقرير المخزون"""
        # إعادة ضبط الجدول
        self.inventory_table.clearContents()
        self.inventory_table.setRowCount(0)

        # الحصول على قيم التصفية
        product_filter = self.inventory_product_input.text().lower()

        # تغيير عنوان التقرير
        self.inventory_section_title.setText("تقرير المخزون")

        # مسح الجدول أولاً
        self.inventory_table.setRowCount(0)

        # استدعاء دالة استخراج تقرير المخزون من النموذج
        from models.reports import ReportModel
        # Pasamos None como categoría para mostrar todos los productos
        inventory_report = ReportModel.get_inventory_report(None, product_filter if product_filter else None)

        # استخراج البيانات من النتيجة
        products_data = inventory_report.get('products', [])

        # متغيرات للإحصائيات
        total_items = len(products_data)
        total_amount = inventory_report['stats']['total_value']
        total_quantity = inventory_report['stats']['total_quantity']

        # إضافة المنتجات إلى الجدول
        for row, product in enumerate(products_data):
            # إضافة المنتج إلى الجدول
            self.inventory_table.insertRow(row)

            # كود المنتج
            id_item = QTableWidgetItem(str(product["id"]))
            id_item.setTextAlignment(Qt.AlignCenter)
            self.inventory_table.setItem(row, 0, id_item)

            # اسم المنتج
            name_item = QTableWidgetItem(product["name"])
            name_item.setTextAlignment(Qt.AlignCenter)
            self.inventory_table.setItem(row, 1, name_item)

            # الفئة (التصنيف)
            category_item = QTableWidgetItem(product["category"])
            category_item.setTextAlignment(Qt.AlignCenter)
            self.inventory_table.setItem(row, 2, category_item)

            # الكمية
            stock_item = QTableWidgetItem(str(product["quantity"]))
            stock_item.setTextAlignment(Qt.AlignCenter)
            self.inventory_table.setItem(row, 3, stock_item)

            # سعر الشراء للمنتج
            cost_item = QTableWidgetItem(f"{product['cost']:.2f}")
            cost_item.setTextAlignment(Qt.AlignCenter)
            self.inventory_table.setItem(row, 4, cost_item)

            # قيمة المخزون (السعر × الكمية)
            stock_value = product["total_value"]
            value_item = QTableWidgetItem(f"{stock_value:.2f}")
            value_item.setTextAlignment(Qt.AlignCenter)
            self.inventory_table.setItem(row, 5, value_item)

            # حالة المخزون (منخفض، متوسط، مرتفع) بناءً على كمية المخزون
            stock_status = self.get_stock_status(product["quantity"], product["category"])
            status_item = QTableWidgetItem(stock_status["text"])
            status_item.setTextAlignment(Qt.AlignCenter)

            # تلوين حالة المخزون
            status_item.setForeground(QColor(stock_status["color"]))
            self.inventory_table.setItem(row, 6, status_item)

        # تحديث إحصائيات التقرير
        self.update_inventory_stats(total_items, total_amount, total_quantity)

    def generate_profits_report(self):
        """توليد تقرير الأرباح"""
        # إعادة ضبط الجدول
        self.profits_table.setRowCount(0)

        # الحصول على قيم التصفية من الكومبوبوكسات الجديدة
        selected_day_text = self.profits_day_combo.currentText()
        selected_month_idx = self.profits_month_combo.currentIndex() + 1  # تحويل الفهرس إلى رقم الشهر (1-12)
        selected_year = int(self.profits_year_combo.currentText())

        min_profit = self.profits_min_profit_input.text()
        product_name = self.profits_product_input.text()

        # إنشاء كائن QDate للتاريخ المحدد
        if selected_day_text == "الكل":
            # إذا كان الاختيار "الكل"، استخدم أول يوم من الشهر
            from_date = QDate(selected_year, selected_month_idx, 1)
            # حساب آخر يوم في الشهر
            if selected_month_idx == 12:
                to_date = QDate(selected_year, selected_month_idx, 31)
            else:
                # الشهر التالي اليوم الأول ناقص يوم واحد = آخر يوم في الشهر الحالي
                to_date = QDate(selected_year, selected_month_idx + 1, 1).addDays(-1)

            # تعديل عنوان التقرير ليظهر الشهر كاملا
            title_date = f"{self.profits_month_combo.currentText()} {selected_year}"
        else:
            # إذا كان اختيار يوم محدد
            selected_day = int(selected_day_text)
            selected_date = QDate(selected_year, selected_month_idx, selected_day)

            # التحقق من صحة التاريخ (مثل 30 فبراير غير صالح)
            if not selected_date.isValid():
                QMessageBox.warning(
                    self,
                    "تاريخ غير صالح",
                    "التاريخ المحدد غير صالح. سيتم استخدام التاريخ الحالي بدلاً منه.",
                    QMessageBox.Ok
                )
                selected_date = QDate.currentDate()

            # تعيين نطاق التاريخ (يوم واحد)
            from_date = selected_date
            to_date = selected_date

            # تعيين تاريخ العنوان
            title_date = from_date.toString("yyyy-MM-dd")

        # تحويل التواريخ إلى سلاسل نصية
        from_date_str = from_date.toString("yyyy-MM-dd")
        to_date_str = to_date.toString("yyyy-MM-dd")

        # عرض تاريخ التقرير في العنوان
        self.profits_section_title.setText(f"تقرير الأرباح - {title_date}")

        try:
            # استدعاء دالة استخراج تقرير الأرباح من النموذج
            from models.reports import ReportModel
            # Pasamos None como categoría para mostrar todos los productos
            profits_report = ReportModel.get_profits_report(from_date_str, to_date_str, None)

            # استخراج البيانات من النتيجة
            sales_data = profits_report.get('sales_data', [])

            # تحضير البيانات للعرض وتطبيق الفلترة
            filtered_data = []
            for product in sales_data:
                # تطبيق فلتر المنتج إذا تم تحديده
                product_name_val = product.get('product_name', '') or ''
                product_code_val = product.get('product_code', '') or ''

                # التحقق من تطابق المنتج مع فلتر البحث (إذا كان موجوداً)
                if product_name and product_name.lower() not in product_name_val.lower() and product_name.lower() not in product_code_val.lower():
                    continue

                # تطبيق فلتر الحد الأدنى للربح
                profit = product.get('profit', 0) or 0
                if min_profit and float(min_profit) > 0 and profit < float(min_profit):
                    continue

                # إضافة المنتج إلى البيانات المفلترة
                filtered_data.append(product)

            # متغيرات لحساب الإجماليات
            total_items = 0
            total_sales = 0
            total_profit = 0

            # إضافة البيانات إلى الجدول
            for row_idx, item in enumerate(filtered_data):
                self.profits_table.insertRow(row_idx)

                # كود المنتج
                product_code = item.get('product_code', '')
                code_item = QTableWidgetItem(str(product_code))
                code_item.setTextAlignment(Qt.AlignCenter)
                self.profits_table.setItem(row_idx, 0, code_item)

                # اسم المنتج
                product_name = item.get('product_name', '')
                name_item = QTableWidgetItem(product_name)
                name_item.setTextAlignment(Qt.AlignCenter)
                self.profits_table.setItem(row_idx, 1, name_item)

                # الفئة
                category = item.get('category', '')
                category_item = QTableWidgetItem(category)
                category_item.setTextAlignment(Qt.AlignCenter)
                self.profits_table.setItem(row_idx, 2, category_item)

                # الكمية المباعة (تم نقلها لتصبح العمود الثالث)
                quantity = item.get('total_quantity', 0) or 0
                qty_item = QTableWidgetItem(str(quantity))
                qty_item.setTextAlignment(Qt.AlignCenter)
                self.profits_table.setItem(row_idx, 3, qty_item)

                # إجمالي الربح (تم نقله لتصبح العمود الرابع)
                profit = item.get('profit', 0) or 0
                profit_item = QTableWidgetItem(self.format_price(profit))
                profit_item.setTextAlignment(Qt.AlignCenter)

                # تلوين خلية الربح (أخضر للربح الإيجابي، أحمر للسالب)
                if profit > 0:
                    profit_item.setForeground(QColor(16, 185, 129))  # لون أخضر
                elif profit < 0:
                    profit_item.setForeground(QColor(239, 68, 68))   # لون أحمر

                self.profits_table.setItem(row_idx, 4, profit_item)

                # تحديث الإجماليات
                total_items += quantity
                total_sales += item.get('total_sales', 0) or 0
                total_profit += profit

            # تحديث ملصقات الإحصائيات
            self.update_profits_stats(total_items, total_sales, total_profit)

            # إظهار رسالة إذا لم تكن هناك نتائج
            # Comentado por solicitud del usuario: eliminar notificación cuando no hay datos de ganancias
            # if self.profits_table.rowCount() == 0:
            #     QMessageBox.information(
            #         self,
            #         "لا توجد بيانات",
            #         "لا توجد بيانات أرباح مطابقة لمعايير البحث المحددة.",
            #         QMessageBox.Ok
            #     )

        except Exception as e:
            # التعامل مع الأخطاء العامة
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء توليد تقرير الأرباح: {str(e)}",
                QMessageBox.Ok
            )
            # عرض الخطأ في وحدة التحكم للتشخيص
            print(f"Error in profits report: {str(e)}")

    def create_inventory_tab(self):
        """إنشاء تبويب تقرير المخزون"""
        tab = QWidget()
        tab.setLayoutDirection(Qt.RightToLeft)
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(15, 10, 15, 10)  # توحيد الهوامش العلوية والسفلية

        # عنوان قسم التصفية - نقله للأعلى خارج الفريم
        filter_title = QLabel("خيارات التصفية")
        filter_title.setObjectName("filter_title")
        filter_title.setFont(QFont("Arial", 12, QFont.Bold))
        tab_layout.addWidget(filter_title)

        # إضافة مسافة بين العنوان والفريم
        tab_layout.addSpacing(5)

        # إنشاء إطار الفلتر لتبويب تقرير المخزون
        filter_frame = QFrame()
        filter_frame.setObjectName("filter_frame")
        filter_frame.setFrameShape(QFrame.StyledPanel)
        filter_frame.setFrameShadow(QFrame.Raised)

        filter_layout = QVBoxLayout(filter_frame)
        filter_layout.setContentsMargins(10, 5, 10, 8)  # تقليل الهامش العلوي من 8 إلى 5
        filter_layout.setSpacing(4)  # تقليل المسافة العمودية بين عناصر التخطيط العمودي

        # حذف عنوان قسم التصفية من داخل الفريم

        # استخدام Grid Layout لتنظيم حقول التصفية
        filter_grid = QGridLayout()
        filter_grid.setHorizontalSpacing(2)  # تقليل المسافة الأفقية من 8 إلى 2
        filter_grid.setVerticalSpacing(4)  # تقليل المسافة العمودية بين صفوف الحقول

        # تصفية حسب الفئة - إزالة العنوان

        # Eliminado: self.inventory_category_combo

        # تصفية حسب حالة المخزون - إزالة العنوان

        self.inventory_status_combo = RTLComboBox()
        self.inventory_status_combo.setObjectName("combo_box")
        self.inventory_status_combo.setFixedHeight(28)
        self.inventory_status_combo.setFixedWidth(110)  # تحديد عرض ثابت لكومبو حالة المخزون
        self.inventory_status_combo.addItems(["الحالة: الكل", "متوفر", "غير متوفر"])  # إضافة "الحالة:" للعنصر الأول

        # تصفية حسب البحث عن المنتج - إزالة العنوان

        self.inventory_product_input = QLineEdit()
        self.inventory_product_input.setObjectName("search_input")
        self.inventory_product_input.setPlaceholderText("المنتج...")  # تعديل النص التوضيحي
        self.inventory_product_input.setFixedHeight(30)
        self.inventory_product_input.setFixedWidth(150)  # تضييق عرض حقل البحث عن المنتج

        # تصفية حسب الحد الأدنى للسعر - إزالة العنوان

        self.inventory_min_price_input = QLineEdit()
        self.inventory_min_price_input.setObjectName("search_input")
        self.inventory_min_price_input.setPlaceholderText("السعر الأدنى...")  # تعديل النص التوضيحي
        self.inventory_min_price_input.setFixedHeight(30)
        self.inventory_min_price_input.setFixedWidth(100)  # تضييق عرض حقل الحد الأدنى للسعر

        # إنشاء تخطيط أفقي للفئات وحالة المخزون
        categories_layout = QHBoxLayout()
        categories_layout.setSpacing(2)  # تقليل المسافة بين الكومبوبوكسات
        # Eliminado: categories_layout.addWidget(self.inventory_category_combo)
        categories_layout.addWidget(self.inventory_status_combo)
        categories_layout.addStretch(1)

        # إنشاء تخطيط عمودي للمنتج والسعر مع هوامش متساوية
        product_price_layout = QVBoxLayout()
        product_price_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش الداخلية
        product_price_layout.setSpacing(8)  # توحيد المسافة بين الحقلين
        product_price_layout.addItem(QSpacerItem(0, 1, QSizePolicy.Minimum, QSizePolicy.Expanding))  # إضافة مساحة مرنة في الأعلى
        product_price_layout.addWidget(self.inventory_product_input, 0, Qt.AlignCenter)  # محاذاة المنتج في المنتصف
        product_price_layout.addWidget(self.inventory_min_price_input, 0, Qt.AlignCenter)  # محاذاة السعر في المنتصف
        product_price_layout.addItem(QSpacerItem(0, 1, QSizePolicy.Minimum, QSizePolicy.Expanding))  # إضافة مساحة مرنة في الأسفل

        # إضافة عناصر التصفية إلى التخطيط الشبكي
        filter_grid.addLayout(categories_layout, 0, 0, 1, 1)  # الفئات وحالة المخزون يأخذان عمود واحد
        filter_grid.addLayout(product_price_layout, 0, 1, 2, 1)  # المنتج والسعر يأخذان عمود واحد ويمتدان لصفين

        # إضافة التخطيط الشبكي إلى تخطيط الفلتر
        filter_layout.addLayout(filter_grid)

        # إنشاء زر توليد التقرير مباشرة في تخطيط الفئات
        self.generate_inventory_btn = QPushButton("🔍   عرض")
        self.generate_inventory_btn.setFixedSize(92, 36)
        self.generate_inventory_btn.setObjectName("action_button")
        font = QFont("Arial", 9)  # تقليل حجم الخط من 10 إلى 9
        font.setBold(False)
        self.generate_inventory_btn.setFont(font)
        self.generate_inventory_btn.setCursor(Qt.PointingHandCursor)
        self.generate_inventory_btn.clicked.connect(self.generate_inventory_report)

        categories_layout.removeItem(categories_layout.itemAt(categories_layout.count()-1))  # Eliminar el stretch existente
        categories_layout.addSpacing(15)  # إضافة مسافة بين الكومبوبوكسات والزر
        categories_layout.addWidget(self.generate_inventory_btn)
        categories_layout.addStretch(1)

        tab_layout.addWidget(filter_frame)
        tab_layout.addSpacing(5)

        # حفظ مرجع لعنوان التقرير (سيتم استخدامه لتحديث التاريخ ولكن لن يتم عرضه)
        self.inventory_section_title = QLabel("تقرير المخزون")
        self.inventory_section_title.setVisible(False)
        self.inventory_date_label = QLabel("")
        self.inventory_date_label.setVisible(False)

        # إنشاء جدول لعرض عناصر المخزون
        self.inventory_table = QTableWidget()
        self.inventory_table.setObjectName("items_table")
        self.inventory_table.setColumnCount(7)
        self.inventory_table.setHorizontalHeaderLabels([
            "كود المنتج", "اسم المنتج", "الفئة", "الكمية", "سعر الشراء (ج.م)",
            "إجمالي القيمة (ج.م)", "حالة المخزون"
        ])

        # تعديل عرض الأعمدة - جعل عمود اسم المنتج أكبر
        header = self.inventory_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)

        # تعيين عرض ثابت للأعمدة الأخرى
        self.inventory_table.setColumnWidth(0, 100)  # كود المنتج
        self.inventory_table.setColumnWidth(2, 100)  # الفئة
        self.inventory_table.setColumnWidth(3, 80)   # الكمية
        self.inventory_table.setColumnWidth(4, 120)  # سعر الشراء
        self.inventory_table.setColumnWidth(5, 130)  # إجمالي القيمة
        self.inventory_table.setColumnWidth(6, 120)  # حالة المخزون

        # جعل عمود اسم المنتج يأخذ المساحة المتبقية
        header.setSectionResizeMode(1, QHeaderView.Stretch)

        self.inventory_table.verticalHeader().setVisible(False)
        self.inventory_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.inventory_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.inventory_table.setAlternatingRowColors(False)

        tab_layout.addWidget(self.inventory_table)

        # إضافة إحصائيات التقرير
        stats_layout = QHBoxLayout()

        self.inventory_total_items_label = QLabel("إجمالي المنتجات: 0")
        self.inventory_total_items_label.setObjectName("stats_label")
        stats_layout.addWidget(self.inventory_total_items_label)

        stats_layout.addStretch()

        self.inventory_total_amount_label = QLabel("إجمالي قيم المخزون: 0.00 ج.م")
        self.inventory_total_amount_label.setObjectName("stats_label")
        stats_layout.addWidget(self.inventory_total_amount_label)

        stats_layout.addSpacing(20)

        self.inventory_total_quantity_label = QLabel("إجمالي وحدات المخزون: 0")
        self.inventory_total_quantity_label.setObjectName("stats_label")
        stats_layout.addWidget(self.inventory_total_quantity_label)

        tab_layout.addLayout(stats_layout)

        return tab

    def create_profits_tab(self):
        """إنشاء تبويب تقرير الأرباح"""
        tab = QWidget()
        tab.setLayoutDirection(Qt.RightToLeft)
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(15, 10, 15, 10)  # توحيد الهوامش العلوية والسفلية

        # عنوان قسم التصفية - نقله للأعلى خارج الفريم
        filter_title = QLabel("خيارات التصفية")
        filter_title.setObjectName("filter_title")
        filter_title.setFont(QFont("Arial", 12, QFont.Bold))
        tab_layout.addWidget(filter_title)

        # إضافة مسافة بين العنوان والفريم
        tab_layout.addSpacing(5)

        # إنشاء إطار الفلتر لتبويب تقرير الأرباح
        filter_frame = QFrame()
        filter_frame.setObjectName("filter_frame")
        filter_frame.setFrameShape(QFrame.StyledPanel)
        filter_frame.setFrameShadow(QFrame.Raised)

        filter_layout = QVBoxLayout(filter_frame)
        filter_layout.setContentsMargins(10, 5, 10, 5)  # توحيد الهوامش العلوية والسفلية
        filter_layout.setSpacing(4)  # تقليل المسافة العمودية بين عناصر التخطيط العمودي

        # حذف عنوان قسم التصفية من داخل الفريم

        # استخدام Grid Layout لتنظيم حقول التصفية
        filter_grid = QGridLayout()
        filter_grid.setHorizontalSpacing(2)  # تقليل المسافة الأفقية من 8 إلى 2
        filter_grid.setVerticalSpacing(4)  # تقليل المسافة العمودية بين صفوف الحقول

        # تصفية حسب التاريخ - إزالة العنوان

        # 1. إضافة كومبوبوكس لليوم
        self.profits_day_combo = RTLComboBox()
        self.profits_day_combo.setObjectName("combo_box")
        self.profits_day_combo.setFixedHeight(28)
        self.profits_day_combo.setFixedWidth(50)  # عرض مناسب لكومبو اليوم

        # إضافة الأيام من 1 إلى 31
        # إضافة خيار الكل أولاً
        self.profits_day_combo.addItem("الكل")
        for day in range(1, 32):
            self.profits_day_combo.addItem(str(day))

        # تعيين اليوم الحالي
        current_day = QDate.currentDate().day()
        self.profits_day_combo.setCurrentIndex(current_day)  # زيادة الفهرس بمقدار 1 بسبب إضافة عنصر "الكل"

        # 2. إضافة كومبوبوكس للشهر
        self.profits_month_combo = RTLComboBox()
        self.profits_month_combo.setObjectName("combo_box")
        self.profits_month_combo.setFixedHeight(28)
        self.profits_month_combo.setFixedWidth(90)  # عرض مناسب لكومبو الشهر

        # إضافة الأشهر العربية
        arabic_months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]
        self.profits_month_combo.addItems(arabic_months)

        # تعيين الشهر الحالي
        current_month = QDate.currentDate().month()
        self.profits_month_combo.setCurrentIndex(current_month - 1)

        # 3. إضافة كومبوبوكس للسنة
        self.profits_year_combo = RTLComboBox()
        self.profits_year_combo.setObjectName("combo_box")
        self.profits_year_combo.setFixedHeight(28)
        self.profits_year_combo.setFixedWidth(70)  # عرض مناسب لكومبو السنة

        # إضافة السنوات (من السنة الحالية إلى خمس سنوات قبلها)
        current_year = QDate.currentDate().year()
        for year in range(current_year, current_year - 6, -1):
            self.profits_year_combo.addItem(str(year))

        # تصفية حسب الفئة - إزالة العنوان

        # Eliminado: self.profits_category_combo

        # تصفية حسب الربح الأدنى - إزالة العنوان

        self.profits_min_profit_input = QLineEdit()
        self.profits_min_profit_input.setObjectName("search_input")
        self.profits_min_profit_input.setPlaceholderText("الربح الأدنى...")  # تعديل النص التوضيحي
        self.profits_min_profit_input.setFixedHeight(30)
        self.profits_min_profit_input.setFixedWidth(100)  # تضييق عرض حقل الربح الأدنى

        # تصفية حسب البحث عن منتج - إزالة العنوان

        self.profits_product_input = QLineEdit()
        self.profits_product_input.setObjectName("search_input")
        self.profits_product_input.setPlaceholderText("المنتج...")  # تعديل النص التوضيحي
        self.profits_product_input.setFixedHeight(30)
        self.profits_product_input.setFixedWidth(150)  # تضييق عرض حقل البحث عن المنتج

        # إنشاء تخطيط أفقي للفترة والفئة
        period_category_layout_horizontal = QHBoxLayout()
        period_category_layout_horizontal.setSpacing(2)  # تقليل المسافة بين الكومبوبوكسات
        period_category_layout_horizontal.addWidget(self.profits_day_combo)
        period_category_layout_horizontal.addWidget(self.profits_month_combo)
        period_category_layout_horizontal.addWidget(self.profits_year_combo)
        period_category_layout_horizontal.addSpacing(15)  # إضافة مسافة بين التاريخ والزر

        # إنشاء زر توليد التقرير في تخطيط التاريخ
        self.generate_profits_btn = QPushButton("🔍   عرض")
        self.generate_profits_btn.setFixedSize(92, 36)
        self.generate_profits_btn.setObjectName("action_button")
        font = QFont("Arial", 9)  # تقليل حجم الخط من 10 إلى 9
        font.setBold(False)
        self.generate_profits_btn.setFont(font)
        self.generate_profits_btn.setCursor(Qt.PointingHandCursor)
        self.generate_profits_btn.clicked.connect(self.generate_profits_report)

        period_category_layout_horizontal.addWidget(self.generate_profits_btn)
        period_category_layout_horizontal.addStretch(1)

        # إنشاء تخطيط عمودي لفلتر التاريخ مع هوامش صغيرة لتوسيطه
        period_category_layout = QVBoxLayout()
        period_category_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش الداخلية
        period_category_layout.addSpacing(8)  # إضافة مسافة ثابتة صغيرة في الأعلى
        period_category_layout.addLayout(period_category_layout_horizontal)  # إضافة التخطيط الأفقي للتاريخ والزر
        period_category_layout.addSpacing(8)  # إضافة مسافة ثابتة صغيرة في الأسفل

        # إنشاء تخطيط عمودي للمنتج والربح الأدنى مع هوامش متساوية
        product_profit_layout = QVBoxLayout()
        product_profit_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش الداخلية
        product_profit_layout.setSpacing(8)  # توحيد المسافة بين الحقلين
        product_profit_layout.addItem(QSpacerItem(0, 1, QSizePolicy.Minimum, QSizePolicy.Expanding))  # إضافة مساحة مرنة في الأعلى
        product_profit_layout.addWidget(self.profits_product_input, 0, Qt.AlignCenter)  # محاذاة المنتج في المنتصف
        product_profit_layout.addWidget(self.profits_min_profit_input, 0, Qt.AlignCenter)  # محاذاة الربح في المنتصف
        product_profit_layout.addItem(QSpacerItem(0, 1, QSizePolicy.Minimum, QSizePolicy.Expanding))  # إضافة مساحة مرنة في الأسفل

        # إضافة عناصر التصفية إلى التخطيط الشبكي
        filter_grid.addLayout(period_category_layout, 0, 0, 1, 1)  # الفترة والفئة يأخذان عمود واحد
        filter_grid.addLayout(product_profit_layout, 0, 1, 2, 1)  # المنتج والربح الأدنى يأخذان عمود واحد ويمتدان لصفين

        # إضافة التخطيط الشبكي إلى تخطيط الفلتر
        filter_layout.addLayout(filter_grid)



        tab_layout.addWidget(filter_frame)
        tab_layout.addSpacing(5)

        # حفظ مرجع لعنوان التقرير (سيتم استخدامه لتحديث التاريخ ولكن لن يتم عرضه)
        self.profits_section_title = QLabel("تقرير الأرباح")
        self.profits_section_title.setVisible(False)
        self.profits_date_label = QLabel("")
        self.profits_date_label.setVisible(False)

        # إنشاء جدول لعرض عناصر تقرير الأرباح
        self.profits_table = QTableWidget()
        self.profits_table.setObjectName("items_table")
        self.profits_table.setColumnCount(5)
        self.profits_table.setHorizontalHeaderLabels([
            "كود المنتج", "اسم المنتج", "الفئة",
            "الكمية المباعة", "إجمالي الربح (ج.م)"
        ])

        # تعديل عرض الأعمدة - جعل عمود اسم المنتج أكبر
        header = self.profits_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)

        # تعيين عرض ثابت للأعمدة الأخرى
        self.profits_table.setColumnWidth(0, 100)  # كود المنتج
        self.profits_table.setColumnWidth(2, 100)  # الفئة
        self.profits_table.setColumnWidth(3, 120)  # الكمية المباعة
        self.profits_table.setColumnWidth(4, 140)  # إجمالي الربح

        # جعل عمود اسم المنتج يأخذ المساحة المتبقية
        header.setSectionResizeMode(1, QHeaderView.Stretch)

        self.profits_table.verticalHeader().setVisible(False)
        self.profits_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.profits_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.profits_table.setAlternatingRowColors(False)

        tab_layout.addWidget(self.profits_table)

        # إضافة إحصائيات التقرير
        stats_layout = QHBoxLayout()

        self.profits_total_items_label = QLabel("إجمالي العناصر المباعة: 0")
        self.profits_total_items_label.setObjectName("stats_label")
        stats_layout.addWidget(self.profits_total_items_label)

        stats_layout.addStretch()

        self.profits_total_amount_label = QLabel("إجمالي المبيعات: 0.00 ج.م")
        self.profits_total_amount_label.setObjectName("stats_label")
        stats_layout.addWidget(self.profits_total_amount_label)

        stats_layout.addSpacing(20)

        self.profits_total_profit_label = QLabel("إجمالي الربح: 0.00 ج.م")
        self.profits_total_profit_label.setObjectName("stats_label")
        stats_layout.addWidget(self.profits_total_profit_label)

        tab_layout.addLayout(stats_layout)

        return tab

    def update_inventory_stats(self, items_count, total_amount, total_quantity):
        """تحديث إحصائيات تقرير المخزون"""
        self.inventory_total_items_label.setText(f"إجمالي المنتجات: {items_count}")
        # تأكد من أن total_amount ليس None قبل التنسيق
        if total_amount is None:
            total_amount = 0
        self.inventory_total_amount_label.setText(f"إجمالي قيم المخزون (بسعر الشراء): {total_amount:.2f} ج.م")
        self.inventory_total_quantity_label.setText(f"إجمالي وحدات المخزون: {total_quantity}")

    def update_profits_stats(self, total_items, total_amount, total_profit):
        """تحديث إحصائيات تقرير الأرباح"""
        self.profits_total_items_label.setText(f"إجمالي العناصر المباعة: {total_items}")
        self.profits_total_amount_label.setText(f"إجمالي المبيعات: {total_amount:.2f} ج.م")
        self.profits_total_profit_label.setText(f"إجمالي الربح: {total_profit:.2f} ج.م")

        # إضافة ألوان لإبراز الأرباح
        if total_profit > 0:
            self.profits_total_profit_label.setStyleSheet("color: #10b981;")  # أخضر للربح
        else:
            self.profits_total_profit_label.setStyleSheet("color: #ef4444;")  # أحمر للخسارة

    def update_sales_stats(self, items_count, total_amount, total_quantity, total_paid=0, total_debt=0):
        """تحديث إحصائيات تقرير المبيعات اليومية"""
        self.sales_total_items_label.setText(f"إجمالي العناصر: {items_count}")
        self.sales_total_amount_label.setText(f"إجمالي المبالغ: {total_amount:.2f} ج.م")
        self.sales_total_paid_label.setText(f"إجمالي المدفوع: {total_paid:.2f} ج.م")
        self.sales_total_debt_label.setText(f"إجمالي الدين: {total_debt:.2f} ج.م")
        self.sales_total_quantity_label.setText(f"إجمالي الكميات: {total_quantity}")

    def update_monthly_stats(self, items_count, total_amount, total_quantity, total_paid=0, total_debt=0):
        """تحديث إحصائيات تقرير المبيعات الشهرية"""
        self.monthly_total_items_label.setText(f"إجمالي العناصر: {items_count}")
        self.monthly_total_amount_label.setText(f"إجمالي المبالغ: {total_amount:.2f} ج.م")
        self.monthly_total_paid_label.setText(f"إجمالي المدفوع: {total_paid:.2f} ج.م")
        self.monthly_total_debt_label.setText(f"إجمالي الدين: {total_debt:.2f} ج.م")
        self.monthly_total_quantity_label.setText(f"إجمالي الكميات: {total_quantity}")

    def get_stock_status(self, quantity, category):
        """تحديد حالة المخزون بناءً على الكمية والتصنيف"""
        # تحديد العتبات وفقًا للتصنيف (يمكن تخصيص العتبات حسب نوع المنتج)
        thresholds = {
            "مشروبات": {"low": 20, "high": 70},
            "حلويات": {"low": 15, "high": 50},
            "مأكولات": {"low": 10, "high": 30},
        }

        # استخدام العتبات الافتراضية إذا لم يتم العثور على التصنيف
        default_thresholds = {"low": 15, "high": 50}
        category_thresholds = thresholds.get(category, default_thresholds)

        # تحديد حالة المخزون
        if quantity <= 0:
            return {"status": "out", "text": "نفذت الكمية", "color": "#ef4444"}  # أحمر
        elif quantity < category_thresholds["low"]:
            return {"status": "low", "text": "منخفض", "color": "#f97316"}  # برتقالي
        elif quantity > category_thresholds["high"]:
            return {"status": "high", "text": "مرتفع", "color": "#0ea5e9"}  # أزرق
        else:
            return {"status": "medium", "text": "متوسط", "color": "#10b981"}  # أخضر

    def update_report_stats(self, items_count, total_amount, total_quantity):
        """تحديث إحصائيات التقرير"""
        self.total_items_label.setText(f"إجمالي العناصر: {items_count}")
        self.total_amount_label.setText(f"إجمالي المبالغ: {total_amount:.2f} ج.م")
        self.total_quantity_label.setText(f"إجمالي الكميات: {total_quantity}")

    def apply_styles(self):
        """تطبيق الأنماط على العناصر"""
        # استخدام التنسيقات من ملف الستايلات
        self.setStyleSheet(AppStyles.get_all_view_styles())

        # إضافة تنسيقات خاصة بصفحة التقارير
        additional_styles = """
            #filter_group {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 10px;
                margin-bottom: 15px;
            }

            #filter_frame {
                background-color: #f1f5f9;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                margin: 0px;  /* إزالة أي هوامش إضافية */
                padding: 0px; /* إزالة أي تباعد إضافي */
            }

            #filter_title {
                color: #334155;
                font-weight: bold;
            }

            #section_title {
                color: #0f172a;
                margin-bottom: 10px;
            }

            #stats_label {
                color: #475569;
                font-size: 13px;
                padding: 10px 0;
            }

            /* تنسيق الجداول وخلاياها */
            QTableWidget {
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 0px;
            }

            QTableWidget::item {
                background-color: #f8fafc;
                padding: 8px;
                border-bottom: 1px solid #e2e8f0;
            }

            QTableWidget::item:selected {
                background-color: rgba(59, 130, 246, 0.3);
                color: #0f172a;
            }

            /* تنسيق التبويبات */
            QTabWidget::pane {
                border: 1px solid #e2e8f0;
                border-radius: 0px 0px 8px 8px;
                top: -1px;
            }

            QTabBar::tab {
                background-color: #f1f5f9;
                color: #64748b;
                border: 1px solid #e2e8f0;
                border-bottom: none;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                padding: 8px 16px;
                margin-right: 2px;
            }

            QTabBar::tab:selected {
                background-color: white;
                color: #3b82f6;
                border-bottom: 2px solid #3b82f6;
            }

            QTabBar::tab:hover:!selected {
                background-color: #e2e8f0;
                color: #334155;
            }

            QTabBar::tab:disabled {
                background-color: #e5e7eb;
                color: #9ca3af;
                border: 1px solid #d1d5db;
                border-bottom: none;
            }

            /* تخصيص تنسيق عناوين الحقول لإزالة الهامش بينها وبين عناصر التحكم */
            #field_label {
                margin: 0px;
                padding-right: 2px;
                color: #1e293b;
                font-weight: bold;
            }

            /* تنسيق عناصر الاختيار والإدخال للتأكد من عدم وجود هوامش */
            QComboBox, QLineEdit {
                margin-left: 0px;
                padding-left: 5px;
            }

            /* تنسيق إضافي لعناصر البحث */
            #search_input {
                padding-left: 5px;
                margin-left: 0px;
                border: 1px solid #d1d5db;
            }

            QGroupBox {
                font-weight: bold;
                color: #334155;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 5px 10px;
                background-color: #f8fafc;
            }

            /* تنسيق زر توليد التقرير لضمان وضوح النص */
            #action_button {
                color: #000000;
                background-color: #f5f5f5;
                border: 1px solid #999999;
                font-weight: normal;
                padding: 10px 8px;
                text-align: center;
                margin: 0px;
                white-space: nowrap;
                border-radius: 6px;
            }

            #action_button:hover {
                background-color: #3498db;
                color: #ffffff;
                border: 1px solid #2980b9;
            }

            /* تقليل المسافة العمودية بين التابات والمحتوى */
            #reports_tabs {
                margin-top: 0px;
            }

            #reports_tabs QTabWidget::pane {
                margin-top: 0px; /* إزالة الهامش العلوي للتابات الفرعية */
                padding-top: 0px; /* إزالة التباعد الداخلي العلوي للتابات الفرعية */
            }

            /* إزالة الهوامش والتباعد الداخلي من عناصر التبويب نفسها */
            QTabBar::tab {
                margin-bottom: 0px;
                padding-bottom: 5px;
            }

            /* ضمان عدم وجود مسافة بين شريط التبويب والمحتوى */
            QTabWidget::tab-bar {
                alignment: left;
                left: 0px;
            }

            /* تنسيق خاص لضمان التوسيط في فريم الفلتر */
            #filter_frame QVBoxLayout {
                margin: 0px;
                padding: 0px;
            }

            #filter_frame QHBoxLayout {
                margin: 0px;
                padding: 0px;
            }
        """

        current_style = self.styleSheet()
        self.setStyleSheet(current_style + additional_styles)

    def generate_monthly_sales_report(self):
        """توليد تقرير المبيعات الشهرية"""
        # تحديث عنوان التقرير
        month_name = self.monthly_month_combo.currentText()
        year = self.monthly_year_combo.currentText()
        self.monthly_section_title.setText(f"المبيعات الشهرية - {month_name} {year}")

        # تحديث تاريخ التقرير
        current_date = QDate.currentDate().toString("yyyy/MM/dd")
        self.monthly_date_label.setText(f"تاريخ التقرير: {current_date}")

        # الحصول على بيانات التصفية
        selected_month_idx = self.monthly_month_combo.currentIndex() + 1  # تحويل الفهرس إلى رقم الشهر (1-12)
        selected_year = int(self.monthly_year_combo.currentText())
        # تم إزالة فلتر حالة الفاتورة
        # invoice_status = self.monthly_status_combo.currentText()
        client_filter = self.monthly_client_input.text().strip().lower()
        product_filter = self.monthly_product_input.text().strip().lower()
        unpaid_only_filter = self.monthly_unpaid_only_checkbox.isChecked()

        try:
            # استدعاء دالة استخراج تقرير المبيعات الشهرية من النموذج
            from models.reports import ReportModel
            monthly_data_result = ReportModel.get_monthly_sales_report(selected_year, selected_month_idx)

            # استخراج البيانات من النتيجة
            sales_data = monthly_data_result.get('sales', [])

            # تحضير البيانات للعرض وتطبيق الفلترة
            filtered_data = []

            # الحصول على الأصناف المباعة لكل فاتورة
            from models.invoices import InvoiceModel
            for invoice in sales_data:
                # تم إزالة الفلترة حسب حالة الفاتورة
                # تجاهل الفواتير التي لا تطابق حالة الفلترة
                # status_match = invoice_status == "الكل" or invoice.get('status') == invoice_status
                # if not status_match:
                #     continue

                # تجاهل الفواتير التي لا تطابق فلتر العميل
                client_name = invoice.get('customer_name', '') or ''
                client_name = client_name.lower()
                if client_filter and client_filter not in client_name:
                    continue

                # تجاهل الفواتير التي لا تطابق فلتر "غير المدفوعة فقط"
                if unpaid_only_filter and invoice.get('status') != 'غير مدفوعة':
                    continue

                # الحصول على عناصر الفاتورة
                invoice_items = InvoiceModel.get_invoice_items(invoice.get('id'))

                for item in invoice_items:
                    # تطبيق فلتر المنتج
                    product_name = item.get('product_name', '') or ''
                    product_code = item.get('product_code', '') or ''
                    product_name = product_name.lower()
                    product_code = product_code.lower()
                    if product_filter and product_filter not in product_name and product_filter not in product_code:
                        continue

                    # إضافة العنصر إلى البيانات المفلترة
                    customer_name = invoice.get('customer_name', '')
                    if not customer_name or customer_name.strip() == '':
                        customer_name = "عميل غير مسجل"

                    filtered_data.append([
                        item.get('product_code', ''),
                        item.get('product_name', ''),
                        item.get('unit_price', 0),
                        item.get('quantity', 0),
                        item.get('total_price', 0),
                        invoice.get('reference_number', ''),
                        customer_name
                        # تم حذف عرض حالة الفاتورة
                        # invoice.get('status', '')
                    ])

            # تجهيز الجدول
            self.monthly_table.setRowCount(0)  # مسح الصفوف الموجودة

            # متغيرات لحساب الإجماليات
            total_items = len(filtered_data)
            total_quantity = sum(row[3] for row in filtered_data)
            total_amount = sum(row[4] for row in filtered_data)

            # الحصول على إحصائيات المدفوع والدين من التقرير
            summary = monthly_data_result.get('summary', {})
            total_paid = summary.get('paid_amount', 0) or 0
            total_debt = summary.get('unpaid_amount', 0) or 0

            # إضافة بيانات الجدول
            for row_idx, row_data in enumerate(filtered_data):
                self.monthly_table.insertRow(row_idx)

                # إضافة الأعمدة الـ 7 فقط (بدون عمود حالة الفاتورة)
                for col_idx in range(7):
                    col_data = row_data[col_idx]

                    # إذا كان العمود هو عمود العميل وكانت البيانات فارغة، نعرض "عميل غير مسجل"
                    if col_idx == 6 and (not col_data or col_data.strip() == ''):
                        col_data = "عميل غير مسجل"

                    item = QTableWidgetItem(str(col_data))
                    # محاذاة عناصر الجدول
                    if col_idx in (2, 4):  # أعمدة السعر والإجمالي
                        item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                        # تنسيق السعر بشكل أفضل (إضافة فواصل الآلاف)
                        try:
                            formatted_price = self.format_price(float(col_data))
                            item = QTableWidgetItem(formatted_price)
                            item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                        except:
                            pass
                    else:
                        item.setTextAlignment(Qt.AlignCenter)

                    self.monthly_table.setItem(row_idx, col_idx, item)

            # Mantener los colores no alternos
            self.monthly_table.setAlternatingRowColors(False)

            # تحديث ملصقات الإحصائيات
            self.update_monthly_stats(total_items, total_amount, total_quantity, total_paid, total_debt)

            # إظهار رسالة إذا لم تكن هناك نتائج
            # Comentado por solicitud del usuario: eliminar notificación cuando no hay ventas
            # if total_items == 0:
            #     QMessageBox.information(
            #         self,
            #         "لا توجد بيانات",
            #         f"لا توجد بيانات مبيعات لشهر {month_name} {year} بناءً على معايير البحث المحددة.",
            #         QMessageBox.Ok
            #     )

        except Exception as e:
            # التعامل مع الأخطاء العامة
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء توليد تقرير المبيعات الشهرية: {str(e)}",
                QMessageBox.Ok
            )
            # عرض الخطأ في وحدة التحكم للتشخيص
            print(f"Error in monthly sales report: {str(e)}")

    def format_price(self, price):
        """تنسيق السعر بإضافة فواصل الآلاف وتقريب الأرقام العشرية"""
        if price == 0:
            return "0.00"

        # التأكد من أن السعر هو رقم
        try:
            price_float = float(price)
        except (ValueError, TypeError):
            return str(price)

        # تقريب الرقم إلى رقمين عشريين وتحويله إلى نص
        price_str = f"{price_float:.2f}"

        # تقسيم الرقم إلى جزء صحيح وجزء عشري
        parts = price_str.split('.')

        # إضافة فواصل الآلاف للجزء الصحيح
        integer_part = parts[0]
        decimal_part = parts[1] if len(parts) > 1 else "00"

        # إضافة فواصل الآلاف كل 3 أرقام
        formatted_integer = ""
        for i, digit in enumerate(reversed(integer_part)):
            if i > 0 and i % 3 == 0:
                formatted_integer = ',' + formatted_integer
            formatted_integer = digit + formatted_integer

        # إعادة تجميع الرقم المنسق
        formatted_price = f"{formatted_integer}.{decimal_part}"

        return formatted_price

    def refresh_page(self):
        """تحديث بيانات الصفحة عند الانتقال إليها أو عند فتح التطبيق"""
        # تحديث حالة تفعيل التبويبات بناءً على صلاحيات المستخدم
        self.update_tab_permissions()

        # الحصول على مؤشر التاب الحالي
        current_index = self.reports_tabs.currentIndex()

        # استدعاء دالة تغيير التاب مع المؤشر الحالي لتحديث محتواه
        self.on_tab_changed(current_index)

        print("تم تحديث صفحة التقارير")

    def update_tab_permissions(self):
        """تحديث حالة تفعيل التبويبات بناءً على صلاحيات المستخدم"""
        # الحصول على معلومات المستخدم الحالي
        main_window = None
        parent = self.parent()
        user_id = None

        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        if not main_window or not main_window.current_user:
            return

        user_id = main_window.current_user.get('id')
        username = main_window.current_user.get('username')

        # إذا كان المستخدم هو admin، فجميع التبويبات مفعلة
        if username == 'admin':
            for i in range(self.reports_tabs.count()):
                self.reports_tabs.setTabEnabled(i, True)
            return

        # استيراد وحدة التحكم بالمستخدمين
        from controllers.user_controller import UserController

        # قائمة بالتبويبات والصلاحيات المطلوبة لكل منها
        tab_permissions = {
            "المبيعات اليومية": "عرض تقارير المبيعات",
            "المبيعات الشهرية": "عرض تقارير المبيعات",
            "المنتجات الاكثر مبيعاً": "عرض تقرير المنتجات الأكثر مبيعاً",
            "تقرير الأرباح": "عرض تقارير الأرباح",
            "تقرير المخزون": "عرض تقارير المخزون"
        }

        # تحديث حالة تفعيل كل تبويب
        for tab_name, permission in tab_permissions.items():
            if tab_name in self.tab_indices:
                tab_index = self.tab_indices[tab_name]
                has_permission = UserController.check_permission(user_id, permission)
                self.reports_tabs.setTabEnabled(tab_index, has_permission)

                # تطبيق نمط CSS للتبويبات المعطلة
                # لا نحتاج لتغيير اللون يدوياً لأننا أضفنا نمط CSS للتبويبات المعطلة

    def create_top_selling_products_tab(self):
        """إنشاء تبويب تقرير المنتجات الأكثر مبيعًا"""
        tab = QWidget()
        tab.setLayoutDirection(Qt.RightToLeft)
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(15, 10, 15, 10)  # توحيد الهوامش العلوية والسفلية

        # عنوان قسم التصفية - نقله للأعلى خارج الفريم
        filter_title = QLabel("خيارات التصفية")
        filter_title.setObjectName("filter_title")
        filter_title.setFont(QFont("Arial", 12, QFont.Bold))
        tab_layout.addWidget(filter_title)

        # إضافة مسافة بين العنوان والفريم
        tab_layout.addSpacing(5)

        # إنشاء إطار الفلتر لتبويب تقرير المنتجات الأكثر مبيعًا
        filter_frame = QFrame()
        filter_frame.setObjectName("filter_frame")
        filter_frame.setFrameShape(QFrame.StyledPanel)
        filter_frame.setFrameShadow(QFrame.Raised)

        filter_layout = QVBoxLayout(filter_frame)
        filter_layout.setContentsMargins(10, 5, 10, 5)  # توحيد الهوامش العلوية والسفلية
        filter_layout.setSpacing(4)  # تقليل المسافة العمودية بين عناصر التخطيط العمودي

        # استخدام Grid Layout لتنظيم حقول التصفية
        filter_grid = QGridLayout()
        filter_grid.setHorizontalSpacing(2)  # تقليل المسافة الأفقية من 8 إلى 2
        filter_grid.setVerticalSpacing(4)  # تقليل المسافة العمودية بين صفوف الحقول

        # ================== تكوين الفترة الزمنية ==================
        # تكوين تاريخ البداية - شهر وسنة فقط، مثل المبيعات الشهرية
        self.top_selling_month_combo = RTLComboBox()
        self.top_selling_month_combo.setObjectName("combo_box")
        self.top_selling_month_combo.setFixedHeight(28)
        self.top_selling_month_combo.setFixedWidth(90)

        # إضافة الأشهر العربية
        arabic_months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]
        self.top_selling_month_combo.addItems(arabic_months)

        # تعيين الشهر الحالي
        current_month = QDate.currentDate().month()
        self.top_selling_month_combo.setCurrentIndex(current_month - 1)

        self.top_selling_year_combo = RTLComboBox()
        self.top_selling_year_combo.setObjectName("combo_box")
        self.top_selling_year_combo.setFixedHeight(28)
        self.top_selling_year_combo.setFixedWidth(70)

        # إضافة السنوات (من السنة الحالية إلى خمس سنوات قبلها)
        current_year = QDate.currentDate().year()
        for year in range(current_year, current_year - 6, -1):
            self.top_selling_year_combo.addItem(str(year))

        # تخطيط أفقي للتاريخ (الشهر والسنة)
        date_layout_horizontal = QHBoxLayout()
        date_layout_horizontal.setSpacing(2)  # تقليل المسافة بين الكومبوبوكسات
        date_layout_horizontal.addWidget(self.top_selling_month_combo)
        date_layout_horizontal.addWidget(self.top_selling_year_combo)
        date_layout_horizontal.addSpacing(15)  # إضافة مسافة بين التاريخ والزر

        # ================== تكوين فلاتر إضافية ==================
        # تحويل فلاتر المنتج إلى تخطيط عمودي مشابه للمبيعات الشهرية

        # فلتر المنتج
        self.top_selling_product_input = QLineEdit()
        self.top_selling_product_input.setObjectName("search_input")
        self.top_selling_product_input.setPlaceholderText("المنتج...")
        self.top_selling_product_input.setFixedHeight(30)
        self.top_selling_product_input.setFixedWidth(150)

        # تخطيط عمودي للفلاتر الإضافية
        filters_layout = QVBoxLayout()
        filters_layout.setContentsMargins(0, 0, 0, 0)
        filters_layout.setSpacing(8)
        filters_layout.addItem(QSpacerItem(0, 1, QSizePolicy.Minimum, QSizePolicy.Expanding))
        filters_layout.addWidget(self.top_selling_product_input, 0, Qt.AlignCenter)
        filters_layout.addItem(QSpacerItem(0, 1, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # ================== تكوين عدد النتائج وزر التوليد ==================
        # زر توليد التقرير
        self.generate_top_selling_btn = QPushButton("🔍   عرض")
        self.generate_top_selling_btn.setFixedSize(92, 36)
        self.generate_top_selling_btn.setObjectName("action_button")
        font = QFont("Arial", 9)
        font.setBold(False)
        self.generate_top_selling_btn.setFont(font)
        self.generate_top_selling_btn.setCursor(Qt.PointingHandCursor)
        self.generate_top_selling_btn.clicked.connect(self.generate_top_selling_products_report)

        # إضافة زر التوليد إلى تخطيط التاريخ
        date_layout_horizontal.addWidget(self.generate_top_selling_btn)
        date_layout_horizontal.addStretch(1)

        # إنشاء تخطيط عمودي لفلتر التاريخ مع هوامش صغيرة لتوسيطه
        date_layout = QVBoxLayout()
        date_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش الداخلية
        date_layout.addSpacing(8)  # إضافة مسافة ثابتة صغيرة في الأعلى
        date_layout.addLayout(date_layout_horizontal)  # إضافة التخطيط الأفقي للتاريخ والزر
        date_layout.addSpacing(8)  # إضافة مسافة ثابتة صغيرة في الأسفل

        # ================== تجميع التخطيط ==================
        # إضافة التخطيطات إلى التخطيط الشبكي
        filter_grid.addLayout(date_layout, 0, 0, 1, 1)  # التاريخ وزر التوليد في العمود 0
        filter_grid.addLayout(filters_layout, 0, 1, 2, 1)  # الفلاتر الإضافية في العمود 1

        filter_layout.addLayout(filter_grid)
        tab_layout.addWidget(filter_frame)
        tab_layout.addSpacing(5)

        # حفظ مرجع لعنوان التقرير
        self.top_selling_section_title = QLabel("المنتجات الأكثر مبيعاً")
        self.top_selling_section_title.setObjectName("section_title")
        self.top_selling_section_title.setVisible(False)
        self.top_selling_date_label = QLabel("")
        self.top_selling_date_label.setVisible(False)

        # إنشاء جدول لعرض المنتجات الأكثر مبيعاً
        self.top_selling_table = QTableWidget()
        self.top_selling_table.setObjectName("items_table")
        self.top_selling_table.setColumnCount(6)
        self.top_selling_table.setHorizontalHeaderLabels([
            "كود المنتج", "اسم المنتج", "الفئة",
            "الكمية المباعة", "إجمالي المبيعات (ج.م)", "إجمالي الأرباح (ج.م)"
        ])

        # تعديل عرض الأعمدة - جعل عمود اسم المنتج أكبر
        header = self.top_selling_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)

        # تعيين عرض ثابت للأعمدة الأخرى
        self.top_selling_table.setColumnWidth(0, 100)  # كود المنتج
        self.top_selling_table.setColumnWidth(2, 100)  # الفئة
        self.top_selling_table.setColumnWidth(3, 100)  # الكمية المباعة
        self.top_selling_table.setColumnWidth(4, 130)  # إجمالي المبيعات
        self.top_selling_table.setColumnWidth(5, 130)  # إجمالي الأرباح

        # جعل عمود اسم المنتج يأخذ المساحة المتبقية
        header.setSectionResizeMode(1, QHeaderView.Stretch)

        self.top_selling_table.verticalHeader().setVisible(False)
        self.top_selling_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.top_selling_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.top_selling_table.setAlternatingRowColors(False)

        tab_layout.addWidget(self.top_selling_table)

        # إضافة إحصائيات التقرير
        stats_layout = QHBoxLayout()

        self.top_selling_total_items_label = QLabel("إجمالي المنتجات: 0")
        self.top_selling_total_items_label.setObjectName("stats_label")
        stats_layout.addWidget(self.top_selling_total_items_label)

        stats_layout.addStretch()

        self.top_selling_total_sales_label = QLabel("إجمالي المبيعات: 0.00 ج.م")
        self.top_selling_total_sales_label.setObjectName("stats_label")
        stats_layout.addWidget(self.top_selling_total_sales_label)

        stats_layout.addSpacing(20)

        self.top_selling_total_profit_label = QLabel("إجمالي الأرباح: 0.00 ج.م")
        self.top_selling_total_profit_label.setObjectName("stats_label")
        stats_layout.addWidget(self.top_selling_total_profit_label)

        tab_layout.addLayout(stats_layout)

        return tab

    def generate_top_selling_products_report(self):
        """توليد تقرير المنتجات الأكثر مبيعاً"""
        # إعادة ضبط الجدول
        self.top_selling_table.setRowCount(0)

        # الحصول على قيم التصفية من الواجهة
        selected_month_idx = self.top_selling_month_combo.currentIndex() + 1  # تحويل الفهرس إلى رقم الشهر (1-12)
        selected_year = int(self.top_selling_year_combo.currentText())

        # تم إزالة فلتر التصنيف (الفئة)
        category = None

        product_filter = self.top_selling_product_input.text().lower()

        # استخدام قيمة ثابتة للحد (النتائج) - عرض جميع النتائج
        limit = None

        # حساب أول وآخر يوم في الشهر المختار
        first_day = QDate(selected_year, selected_month_idx, 1)

        # حساب آخر يوم في الشهر (الشهر التالي اليوم الأول ناقص يوم واحد)
        if selected_month_idx == 12:
            last_day = QDate(selected_year, 12, 31)
        else:
            last_day = QDate(selected_year, selected_month_idx + 1, 1).addDays(-1)

        # تحويل التواريخ إلى النسق المطلوب YYYY-MM-DD
        from_date_str = first_day.toString("yyyy-MM-dd")
        to_date_str = last_day.toString("yyyy-MM-dd")

        # بناء عنوان التقرير مع نطاق التاريخ
        month_name = self.top_selling_month_combo.currentText()
        title_date = f"{month_name} {selected_year}"

        # عرض تاريخ التقرير في العنوان
        self.top_selling_section_title.setText(f"المنتجات الأكثر مبيعاً - {title_date}")

        try:
            # استدعاء دالة استخراج تقرير المنتجات الأكثر مبيعاً من النموذج
            from models.reports import ReportModel
            products_report = ReportModel.get_top_selling_products_report(
                from_date_str, to_date_str, category, limit
            )

            # استخراج البيانات من النتيجة
            products_data = products_report.get('products_data', [])

            # تحضير البيانات للعرض وتطبيق الفلترة
            filtered_data = []
            for product in products_data:
                # تطبيق فلتر المنتج إذا تم تحديده
                product_name_val = product.get('product_name', '') or ''
                product_code_val = product.get('product_code', '') or ''

                # التحقق من تطابق المنتج مع فلتر البحث (إذا كان موجوداً)
                if product_filter and product_filter not in product_name_val.lower() and product_filter not in product_code_val.lower():
                    continue

                # إضافة المنتج إلى البيانات المفلترة
                filtered_data.append(product)

            # متغيرات لحساب الإجماليات
            total_items = len(filtered_data)
            total_sales = sum(item.get('total_sales', 0) or 0 for item in filtered_data)
            total_profit = sum(item.get('profit', 0) or 0 for item in filtered_data)

            # إضافة البيانات إلى الجدول
            for row_idx, item in enumerate(filtered_data):
                self.top_selling_table.insertRow(row_idx)

                # كود المنتج
                product_code = item.get('product_code', '')
                code_item = QTableWidgetItem(str(product_code))
                code_item.setTextAlignment(Qt.AlignCenter)
                self.top_selling_table.setItem(row_idx, 0, code_item)

                # اسم المنتج
                product_name = item.get('product_name', '')
                name_item = QTableWidgetItem(product_name)
                name_item.setTextAlignment(Qt.AlignCenter)
                self.top_selling_table.setItem(row_idx, 1, name_item)

                # الفئة
                category = item.get('category', '')
                category_item = QTableWidgetItem(category)
                category_item.setTextAlignment(Qt.AlignCenter)
                self.top_selling_table.setItem(row_idx, 2, category_item)

                # الكمية المباعة
                quantity = item.get('total_quantity', 0) or 0
                quantity_item = QTableWidgetItem(str(quantity))
                quantity_item.setTextAlignment(Qt.AlignCenter)
                self.top_selling_table.setItem(row_idx, 3, quantity_item)

                # إجمالي المبيعات
                total_sales_item = item.get('total_sales', 0) or 0
                sales_item = QTableWidgetItem(self.format_price(total_sales_item))
                sales_item.setTextAlignment(Qt.AlignCenter)
                self.top_selling_table.setItem(row_idx, 4, sales_item)

                # إجمالي الأرباح
                profit_val = item.get('profit', 0) or 0
                profit_item = QTableWidgetItem(self.format_price(profit_val))
                profit_item.setTextAlignment(Qt.AlignCenter)

                # تحديد لون خلية الربح (أخضر للأرباح، أحمر للخسائر)
                if profit_val > 0:
                    profit_item.setForeground(QColor("#27ae60"))  # لون أخضر للأرباح
                elif profit_val < 0:
                    profit_item.setForeground(QColor("#e74c3c"))  # لون أحمر للخسائر

                self.top_selling_table.setItem(row_idx, 5, profit_item)

            # تحديث إحصائيات التقرير
            self.update_top_selling_stats(total_items, total_sales, total_profit)

        except Exception as e:
            # عرض رسالة خطأ إذا فشلت عملية توليد التقرير
            error_msg = QMessageBox()
            error_msg.setIcon(QMessageBox.Critical)
            error_msg.setWindowTitle("خطأ في إنشاء التقرير")
            error_msg.setText("حدث خطأ أثناء إنشاء المنتجات الأكثر مبيعاً")
            error_msg.setDetailedText(str(e))
            error_msg.setStandardButtons(QMessageBox.Ok)
            error_msg.exec_()
            print(f"خطأ في إنشاء المنتجات الأكثر مبيعاً: {str(e)}")

    def update_top_selling_stats(self, total_items, total_sales, total_profit):
        """تحديث إحصائيات المنتجات الأكثر مبيعاً"""
        self.top_selling_total_items_label.setText(f"إجمالي المنتجات: {total_items}")
        self.top_selling_total_sales_label.setText(f"إجمالي المبيعات: {self.format_price(total_sales)} ج.م")
        self.top_selling_total_profit_label.setText(f"إجمالي الأرباح: {self.format_price(total_profit)} ج.م")
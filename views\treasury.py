# -*- coding: utf-8 -*-
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, 
                          QFrame, QGridLayout, QSizePolicy, QTableWidget, QTableWidgetItem, QHeaderView,
                          QComboBox, QLineEdit, QDialog, QMessageBox, QDateEdit, QFormLayout, QTabWidget,
                          QDoubleSpinBox, QTextEdit, QAbstractItemView, QSpacerItem, QGroupBox,
                          QDialogButtonBox)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QFont, QIcon, QColor, QBrush, QDoubleValidator

from styles import AppStyles
import datetime
from models.database import db
from models.treasury import TreasuryModel

# تعريف ثوابت التطبيق
CURRENCY = "جنيه"  # العملة المستخدمة في التطبيق
CURRENCY_SYMBOL = "ج.م"  # رمز العملة المصرية

class TreasuryView(QWidget):
    """واجهة المستخدم لصفحة الخزينة"""
    
    def __init__(self):
        super().__init__()
        
        # التأكد من وجود جداول الخزينة في قاعدة البيانات
        TreasuryModel.ensure_tables_exist()
        
        # إعداد التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 15, 20, 20)
        main_layout.setSpacing(15)
        
        # إضافة شريط العنوان
        header_layout = self.create_header_section()
        main_layout.addLayout(header_layout)
        
        # إنشاء قسم الأدراج
        drawers_section = self.create_drawers_section()
        main_layout.addLayout(drawers_section)
        
        # إنشاء قسم جدول حركة الخزينة
        transactions_section = self.create_transactions_section()
        main_layout.addWidget(transactions_section)
        
        # تعيين نسب الحجم بين الأقسام
        main_layout.setStretch(1, 1)  # قسم الأدراج
        main_layout.setStretch(2, 2)  # قسم جدول الحركات
        
        # تحديث البيانات
        self.refresh_page()
        
        # تطبيق التنسيقات
        self.apply_styles()
    
    def create_header_section(self):
        """إنشاء قسم العنوان والأزرار العلوية"""
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(10)
        
        # إضافة عنوان الصفحة
        page_title = QLabel("الخزينة")
        page_title.setObjectName("page_title")
        page_title.setFont(QFont("Arial", 16, QFont.Bold))
        header_layout.addWidget(page_title)
        
        header_layout.addStretch()
        
        # إضافة زر تقرير الخزينة
        report_btn = QPushButton("تقرير الخزينة")
        report_btn.setObjectName("secondary_button")
        report_btn.setIcon(QIcon.fromTheme("x-office-spreadsheet"))
        report_btn.clicked.connect(self.generate_treasury_report)
        header_layout.addWidget(report_btn)
        
        # إضافة زر إنشاء يوم جديد
        new_day_btn = QPushButton("بدء يوم جديد")
        new_day_btn.setObjectName("action_button")
        new_day_btn.setIcon(QIcon.fromTheme("appointment-new"))
        new_day_btn.clicked.connect(self.start_new_day)
        header_layout.addWidget(new_day_btn)
        
        # إضافة زر إغلاق اليوم
        close_day_btn = QPushButton("إغلاق اليوم")
        close_day_btn.setObjectName("action_button")
        close_day_btn.setIcon(QIcon.fromTheme("window-close"))
        close_day_btn.clicked.connect(self.close_day)
        header_layout.addWidget(close_day_btn)
        
        return header_layout
    
    def create_drawers_section(self):
        """إنشاء قسم أدراج الخزينة"""
        drawers_layout = QHBoxLayout()
        drawers_layout.setContentsMargins(0, 0, 0, 0)
        drawers_layout.setSpacing(20)
        
        # إنشاء درج رأس المال
        self.capital_frame = self.create_drawer_frame("درج رأس المال", "capital_drawer")
        drawers_layout.addWidget(self.capital_frame)
        
        # إنشاء درج اليومية
        self.daily_frame = self.create_drawer_frame("درج اليومية", "daily_drawer")
        drawers_layout.addWidget(self.daily_frame)
        
        return drawers_layout
    
    def create_drawer_frame(self, title, object_name):
        """إنشاء إطار عرض معلومات الدرج"""
        frame = QFrame()
        frame.setObjectName(object_name)
        frame.setFrameShape(QFrame.StyledPanel)
        frame.setFrameShadow(QFrame.Raised)
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # عنوان الدرج
        title_label = QLabel(title)
        title_label.setObjectName("drawer_title")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # رصيد الدرج
        balance_layout = QVBoxLayout()
        balance_layout.setContentsMargins(0, 10, 0, 10)
        balance_layout.setSpacing(5)
        
        balance_title = QLabel("الرصيد الحالي:")
        balance_title.setAlignment(Qt.AlignCenter)
        balance_layout.addWidget(balance_title)
        
        balance_value = QLabel("0.00 " + CURRENCY_SYMBOL)
        balance_value.setObjectName("balance_value")
        balance_value.setFont(QFont("Arial", 18, QFont.Bold))
        balance_value.setAlignment(Qt.AlignCenter)
        balance_layout.addWidget(balance_value)
        
        # حفظ مرجع لعنصر الرصيد
        if object_name == "capital_drawer":
            self.capital_balance = balance_value
        else:
            self.daily_balance = balance_value
            
            # إضافة الحالة لدرج اليومية
            status_layout = QHBoxLayout()
            status_label = QLabel("الحالة:")
            status_value = QLabel("مغلق")
            status_value.setObjectName("status_value")
            self.daily_status = status_value
            status_layout.addWidget(status_label)
            status_layout.addWidget(status_value)
            status_layout.addStretch()
            balance_layout.addLayout(status_layout)
            
            # إضافة إجمالي المبيعات اليومية
            sales_layout = QHBoxLayout()
            sales_label = QLabel("إجمالي المبيعات اليوم:")
            sales_value = QLabel("0.00 " + CURRENCY_SYMBOL)
            sales_value.setObjectName("sales_value")
            sales_value.setFont(QFont("Arial", 14, QFont.Bold))
            sales_value.setStyleSheet("color: #2563eb;")
            self.daily_sales = sales_value
            sales_layout.addWidget(sales_label)
            sales_layout.addWidget(sales_value)
            sales_layout.addStretch()
            balance_layout.addLayout(sales_layout)
        
        layout.addLayout(balance_layout)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(10)
        
        # أزرار مختلفة حسب نوع الدرج
        if object_name == "capital_drawer":
            # أزرار درج رأس المال
            withdraw_btn = QPushButton("سحب من رأس المال")
            withdraw_btn.setObjectName("drawer_button")
            withdraw_btn.clicked.connect(self.withdraw_from_capital)
            
            deposit_btn = QPushButton("إيداع في رأس المال")
            deposit_btn.setObjectName("drawer_button")
            deposit_btn.clicked.connect(self.deposit_to_capital)
            
            buttons_layout.addWidget(withdraw_btn)
            buttons_layout.addWidget(deposit_btn)
        else:
            # أزرار درج اليومية
            withdraw_btn = QPushButton("سحب من درج اليومية")
            withdraw_btn.setObjectName("drawer_button")
            withdraw_btn.clicked.connect(self.withdraw_from_daily)
            
            transfer_btn = QPushButton("تحويل لرأس المال")
            transfer_btn.setObjectName("drawer_button")
            transfer_btn.clicked.connect(self.transfer_to_capital)
            
            buttons_layout.addWidget(withdraw_btn)
            buttons_layout.addWidget(transfer_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        return frame
    
    def create_transactions_section(self):
        """إنشاء قسم جدول حركة الخزينة"""
        # إنشاء إطار للجدول
        frame = QFrame()
        frame.setObjectName("transactions_frame")
        frame.setFrameShape(QFrame.StyledPanel)
        frame.setFrameShadow(QFrame.Raised)
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # عنوان القسم
        title_layout = QHBoxLayout()
        title_label = QLabel("حركة الخزينة")
        title_label.setObjectName("section_title")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # إضافة عناصر الفلترة
        filter_label = QLabel("تصفية:")
        filter_label.setObjectName("filter_label")
        title_layout.addWidget(filter_label)
        
        # اختيار الفترة
        self.period_combo = QComboBox()
        self.period_combo.setObjectName("filter_combo")
        self.period_combo.setMinimumWidth(120)
        self.period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "السنة الحالية", "الكل"])
        self.period_combo.setCurrentIndex(0)  # افتراضياً: اليوم
        self.period_combo.currentIndexChanged.connect(self.filter_transactions)
        title_layout.addWidget(self.period_combo)
        
        # اختيار نوع الحركة
        self.type_combo = QComboBox()
        self.type_combo.setObjectName("filter_combo")
        self.type_combo.setMinimumWidth(100)
        self.type_combo.addItems(["الكل", "إيداع", "سحب", "تحويل"])
        self.type_combo.currentIndexChanged.connect(self.filter_transactions)
        title_layout.addWidget(self.type_combo)
        
        # زر تحديث
        refresh_btn = QPushButton("تحديث")
        refresh_btn.setObjectName("refresh_button")
        refresh_btn.setIcon(QIcon.fromTheme("view-refresh"))
        refresh_btn.clicked.connect(self.refresh_transactions)
        title_layout.addWidget(refresh_btn)
        
        layout.addLayout(title_layout)
        
        # إنشاء جدول الحركات
        self.transactions_table = QTableWidget()
        self.transactions_table.setObjectName("transactions_table")
        self.transactions_table.setColumnCount(7)
        self.transactions_table.setHorizontalHeaderLabels(["التاريخ", "الدرج", "النوع", "المبلغ", "الرصيد بعد", "الوصف", ""])
        self.transactions_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.transactions_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.transactions_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.transactions_table.verticalHeader().setVisible(False)
        self.transactions_table.setAlternatingRowColors(True)
        layout.addWidget(self.transactions_table)
        
        # إنشاء صف ملخص
        summary_frame = QFrame()
        summary_frame.setObjectName("summary_frame")
        summary_layout = QHBoxLayout(summary_frame)
        summary_layout.setContentsMargins(10, 10, 10, 10)
        
        self.count_label = QLabel("عدد الحركات: 0")
        self.count_label.setObjectName("summary_label")
        
        self.total_deposits_label = QLabel(f"إجمالي الإيداعات: 0.00 {CURRENCY_SYMBOL}")
        self.total_deposits_label.setObjectName("summary_deposits")
        
        self.total_withdrawals_label = QLabel(f"إجمالي المسحوبات: 0.00 {CURRENCY_SYMBOL}")
        self.total_withdrawals_label.setObjectName("summary_withdrawals")
        
        self.net_label = QLabel(f"الصافي: 0.00 {CURRENCY_SYMBOL}")
        self.net_label.setObjectName("summary_net")
        
        summary_layout.addWidget(self.count_label)
        summary_layout.addStretch()
        summary_layout.addWidget(self.total_deposits_label)
        summary_layout.addSpacing(15)
        summary_layout.addWidget(self.total_withdrawals_label)
        summary_layout.addSpacing(15)
        summary_layout.addWidget(self.net_label)
            
        layout.addWidget(summary_frame)
            
        return frame
    
    # وظائف معالجة الأحداث
    
    def start_new_day(self):
        """بدء يوم عمل جديد وفتح درج اليومية"""
        # التحقق من وجود يوم مفتوح بالفعل
        query = """
            SELECT id FROM daily_cash_drawers 
            WHERE status = 'open'
        """
        open_drawer = db.fetch_one(query)
        
        if open_drawer:
            QMessageBox.warning(self, "تنبيه", "يوجد درج مفتوح بالفعل. يرجى إغلاق الدرج الحالي أولاً.")
            return
        
        # الحصول على الخزينة الرئيسية
        treasuries = TreasuryModel.get_all_treasuries()
        if not treasuries:
            QMessageBox.critical(self, "خطأ", "لا توجد خزينة متاحة. يرجى إنشاء خزينة أولاً.")
            return
        
        # إنشاء نافذة حوار لإدخال المبلغ الافتتاحي
        dialog = QDialog(self)
        dialog.setWindowTitle("فتح درج اليومية")
        dialog.setMinimumWidth(300)
        
        layout = QVBoxLayout(dialog)
        
        # إضافة حقل المبلغ الافتتاحي
        form_layout = QFormLayout()
        amount_input = QLineEdit()
        amount_input.setValidator(QDoubleValidator(0, 1000000, 2))
        amount_input.setText("0.00")
        form_layout.addRow("المبلغ الافتتاحي:", amount_input)
        
        layout.addLayout(form_layout)
        
        # أزرار الموافقة والإلغاء
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)
        
        # عرض النافذة وانتظار الرد
        if dialog.exec_() == QDialog.Accepted:
            try:
                # تحضير بيانات الدرج
                open_amount = float(amount_input.text() or 0)
                
                drawer_data = {
                    'treasury_id': treasuries[0]['id'],  # الخزينة الرئيسية
                    'open_amount': open_amount,
                    'opened_by': 'المستخدم'  # يمكن تغييره لاستخدام اسم المستخدم الحالي
                }
                
                # فتح الدرج باستخدام النموذج
                result = TreasuryModel.open_daily_drawer(drawer_data)
                
                if result['success']:
                    QMessageBox.information(self, "بدء يوم جديد", "تم بدء يوم عمل جديد وفتح درج اليومية بنجاح.")
                    self.refresh_page()
                else:
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح الدرج: {result.get('error', '')}")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")

            
    def close_day(self):
        """إغلاق يوم العمل الحالي ونقل الرصيد إلى رأس المال"""
        # التحقق من وجود درج مفتوح
        query = """
            SELECT id, treasury_id, open_amount, status, opened_at 
            FROM daily_cash_drawers 
            WHERE status = 'open'
            ORDER BY opened_at DESC LIMIT 1
        """
        daily_drawer = db.fetch_one(query)
        
        if not daily_drawer:
            QMessageBox.warning(self, "تنبيه", "لا يوجد درج مفتوح حالياً.")
            return
        
        # حساب الرصيد الحالي للدرج
        query = """
            SELECT 
                SUM(CASE WHEN transaction_type = 'deposit' THEN amount ELSE 0 END) as total_deposits,
                SUM(CASE WHEN transaction_type = 'withdraw' THEN amount ELSE 0 END) as total_withdrawals
            FROM treasury_transactions
            WHERE reference_type = 'daily_drawer' AND reference_id = ?
        """
        summary = db.fetch_one(query, (daily_drawer['id'],))
        
        open_amount = float(daily_drawer['open_amount'])
        total_deposits = float(summary['total_deposits']) if summary and summary['total_deposits'] else 0
        total_withdrawals = float(summary['total_withdrawals']) if summary and summary['total_withdrawals'] else 0
        current_balance = open_amount + total_deposits - total_withdrawals
        
        # إنشاء نافذة حوار لتأكيد الإغلاق وإدخال المبلغ الفعلي
        dialog = QDialog(self)
        dialog.setWindowTitle("إغلاق درج اليومية")
        dialog.setMinimumWidth(400)
        
        layout = QVBoxLayout(dialog)
        
        # عرض معلومات الدرج
        info_layout = QFormLayout()
        info_layout.addRow("المبلغ الافتتاحي:", QLabel(f"{open_amount:,.2f} {CURRENCY_SYMBOL}"))
        info_layout.addRow("إجمالي الإيداعات:", QLabel(f"{total_deposits:,.2f} {CURRENCY_SYMBOL}"))
        info_layout.addRow("إجمالي المسحوبات:", QLabel(f"{total_withdrawals:,.2f} {CURRENCY_SYMBOL}"))
        info_layout.addRow("الرصيد المتوقع:", QLabel(f"{current_balance:,.2f} {CURRENCY_SYMBOL}"))
        layout.addLayout(info_layout)
        
        # إضافة خط فاصل
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line)
        
        # إضافة حقل المبلغ الفعلي
        form_layout = QFormLayout()
        actual_amount_input = QLineEdit()
        actual_amount_input.setValidator(QDoubleValidator(0, 1000000, 2))
        actual_amount_input.setText(f"{current_balance:.2f}")
        form_layout.addRow("المبلغ الفعلي في الدرج:", actual_amount_input)
        
        # إضافة حقل ملاحظات
        notes_input = QTextEdit()
        notes_input.setMaximumHeight(80)
        form_layout.addRow("ملاحظات:", notes_input)
        
        layout.addLayout(form_layout)
        
        # أزرار الموافقة والإلغاء
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)
        
        # عرض النافذة وانتظار الرد
        if dialog.exec_() == QDialog.Accepted:
            try:
                # تحضير بيانات إغلاق الدرج
                actual_amount = float(actual_amount_input.text() or 0)
                difference = actual_amount - current_balance
                notes = notes_input.toPlainText()
                
                # تحديث حالة الدرج
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                query = """
                    UPDATE daily_cash_drawers
                    SET status = 'closed', close_amount = ?, expected_amount = ?,
                        difference_amount = ?, notes = ?, closed_by = ?, closed_at = ?, updated_at = ?
                    WHERE id = ?
                """
                params = (
                    actual_amount, current_balance, difference, notes,
                    'المستخدم', current_time, current_time, daily_drawer['id']
                )
                db.execute(query, params)
                
                # إذا كان هناك رصيد متبقي، نقوم بتحويله إلى رأس المال
                if actual_amount > 0:
                    # الحصول على الخزينة الرئيسية
                    treasuries = TreasuryModel.get_all_treasuries()
                    if treasuries:
                        # إضافة حركة تحويل من الدرج اليومي إلى رأس المال
                        transaction_data = {
                            'treasury_id': treasuries[0]['id'],
                            'transaction_type': 'deposit',
                            'amount': actual_amount,
                            'reference_type': 'transfer_from_daily',
                            'reference_id': daily_drawer['id'],
                            'description': 'تحويل رصيد من درج اليومية عند الإغلاق',
                            'created_by': 'المستخدم',
                            'transaction_date': current_time
                        }
                        TreasuryModel.add_transaction(transaction_data)
                
                db.commit()
                QMessageBox.information(self, "إغلاق اليوم", "تم إغلاق اليوم ونقل الرصيد إلى رأس المال بنجاح.")
                self.refresh_page()
            except Exception as e:
                db.rollback()
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إغلاق الدرج: {str(e)}")

    
    def withdraw_from_capital(self):
        """سحب مبلغ من درج رأس المال"""
        # الحصول على الخزينة الرئيسية
        treasuries = TreasuryModel.get_all_treasuries()
        if not treasuries:
            QMessageBox.critical(self, "خطأ", "لا توجد خزينة متاحة.")
            return
        
        # إنشاء نافذة حوار لإدخال المبلغ والوصف
        dialog = QDialog(self)
        dialog.setWindowTitle("سحب من رأس المال")
        dialog.setMinimumWidth(350)
        
        layout = QVBoxLayout(dialog)
        
        # إضافة حقول الإدخال
        form_layout = QFormLayout()
        
        amount_input = QLineEdit()
        amount_input.setValidator(QDoubleValidator(0.01, 1000000, 2))
        form_layout.addRow("المبلغ:", amount_input)
        
        description_input = QLineEdit()
        description_input.setText("سحب من رأس المال")
        form_layout.addRow("الوصف:", description_input)
        
        layout.addLayout(form_layout)
        
        # أزرار الموافقة والإلغاء
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)
        
        # عرض النافذة وانتظار الرد
        if dialog.exec_() == QDialog.Accepted:
            try:
                # التحقق من المبلغ
                amount_text = amount_input.text()
                if not amount_text:
                    QMessageBox.warning(self, "تنبيه", "يرجى إدخال المبلغ.")
                    return
                
                amount = float(amount_text)
                if amount <= 0:
                    QMessageBox.warning(self, "تنبيه", "يجب أن يكون المبلغ أكبر من صفر.")
                    return
                
                # التحقق من كفاية الرصيد
                current_balance = float(treasuries[0]['current_balance'])
                if amount > current_balance:
                    QMessageBox.warning(self, "تنبيه", "المبلغ المطلوب أكبر من الرصيد المتاح.")
                    return
                
                # إضافة حركة سحب
                transaction_data = {
                    'treasury_id': treasuries[0]['id'],
                    'transaction_type': 'withdraw',
                    'amount': amount,
                    'reference_type': 'capital_withdraw',
                    'description': description_input.text() or "سحب من رأس المال",
                    'created_by': 'المستخدم',
                    'transaction_date': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
                transaction_id = TreasuryModel.add_transaction(transaction_data)
                
                if transaction_id:
                    QMessageBox.information(self, "نجاح العملية", f"تم سحب {amount:,.2f} {CURRENCY_SYMBOL} من رأس المال بنجاح.")
                    self.refresh_page()
                else:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء تنفيذ العملية. يرجى المحاولة مرة أخرى.")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")
    
    def deposit_to_capital(self):
        """إيداع مبلغ في درج رأس المال"""
        # الحصول على الخزينة الرئيسية
        treasuries = TreasuryModel.get_all_treasuries()
        if not treasuries:
            QMessageBox.critical(self, "خطأ", "لا توجد خزينة متاحة.")
            return
        
        # إنشاء نافذة حوار لإدخال المبلغ والوصف
        dialog = QDialog(self)
        dialog.setWindowTitle("إيداع في رأس المال")
        dialog.setMinimumWidth(350)
        
        layout = QVBoxLayout(dialog)
        
        # إضافة حقول الإدخال
        form_layout = QFormLayout()
        
        amount_input = QLineEdit()
        amount_input.setValidator(QDoubleValidator(0.01, 1000000, 2))
        form_layout.addRow("المبلغ:", amount_input)
        
        description_input = QLineEdit()
        description_input.setText("إيداع في رأس المال")
        form_layout.addRow("الوصف:", description_input)
        
        layout.addLayout(form_layout)
        
        # أزرار الموافقة والإلغاء
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)
        
        # عرض النافذة وانتظار الرد
        if dialog.exec_() == QDialog.Accepted:
            try:
                # التحقق من المبلغ
                amount_text = amount_input.text()
                if not amount_text:
                    QMessageBox.warning(self, "تنبيه", "يرجى إدخال المبلغ.")
                    return
                
                amount = float(amount_text)
                if amount <= 0:
                    QMessageBox.warning(self, "تنبيه", "يجب أن يكون المبلغ أكبر من صفر.")
                    return
                
                # إضافة حركة إيداع
                transaction_data = {
                    'treasury_id': treasuries[0]['id'],
                    'transaction_type': 'deposit',
                    'amount': amount,
                    'reference_type': 'capital_deposit',
                    'description': description_input.text() or "إيداع في رأس المال",
                    'created_by': 'المستخدم',
                    'transaction_date': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
                transaction_id = TreasuryModel.add_transaction(transaction_data)
                
                if transaction_id:
                    QMessageBox.information(self, "نجاح العملية", f"تم إيداع {amount:,.2f} {CURRENCY_SYMBOL} في رأس المال بنجاح.")
                    self.refresh_page()
                else:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء تنفيذ العملية. يرجى المحاولة مرة أخرى.")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")
    
    def withdraw_from_daily(self):
        """سحب مبلغ من درج اليومية"""
        # التحقق من وجود درج مفتوح
        query = """
            SELECT id, treasury_id, open_amount, status, opened_at 
            FROM daily_cash_drawers 
            WHERE status = 'open'
            ORDER BY opened_at DESC LIMIT 1
        """
        daily_drawer = db.fetch_one(query)
        
        if not daily_drawer:
            QMessageBox.warning(self, "تنبيه", "لا يوجد درج مفتوح حالياً.")
            return
        
        # حساب الرصيد الحالي للدرج
        query = """
            SELECT 
                SUM(CASE WHEN transaction_type = 'deposit' THEN amount ELSE 0 END) as total_deposits,
                SUM(CASE WHEN transaction_type = 'withdraw' THEN amount ELSE 0 END) as total_withdrawals
            FROM treasury_transactions
            WHERE reference_type = 'daily_drawer' AND reference_id = ?
        """
        summary = db.fetch_one(query, (daily_drawer['id'],))
        
        open_amount = float(daily_drawer['open_amount'])
        total_deposits = float(summary['total_deposits']) if summary and summary['total_deposits'] else 0
        total_withdrawals = float(summary['total_withdrawals']) if summary and summary['total_withdrawals'] else 0
        current_balance = open_amount + total_deposits - total_withdrawals
        
        # إنشاء نافذة حوار لإدخال المبلغ والوصف
        dialog = QDialog(self)
        dialog.setWindowTitle("سحب من درج اليومية")
        dialog.setMinimumWidth(350)
        
        layout = QVBoxLayout(dialog)
        
        # إضافة حقول الإدخال
        form_layout = QFormLayout()
        
        amount_input = QLineEdit()
        amount_input.setValidator(QDoubleValidator(0.01, 1000000, 2))
        form_layout.addRow("المبلغ:", amount_input)
        
        description_input = QLineEdit()
        description_input.setText("سحب من درج اليومية")
        form_layout.addRow("الوصف:", description_input)
        
        layout.addLayout(form_layout)
        
        # أزرار الموافقة والإلغاء
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)
        
        # عرض النافذة وانتظار الرد
        if dialog.exec_() == QDialog.Accepted:
            try:
                # التحقق من المبلغ
                amount_text = amount_input.text()
                if not amount_text:
                    QMessageBox.warning(self, "تنبيه", "يرجى إدخال المبلغ.")
                    return
                
                amount = float(amount_text)
                if amount <= 0:
                    QMessageBox.warning(self, "تنبيه", "يجب أن يكون المبلغ أكبر من صفر.")
                    return
                
                # التحقق من كفاية الرصيد
                if amount > current_balance:
                    QMessageBox.warning(self, "تنبيه", "المبلغ المطلوب أكبر من الرصيد المتاح.")
                    return
                
                # إضافة حركة سحب
                transaction_data = {
                    'treasury_id': daily_drawer['treasury_id'],
                    'transaction_type': 'withdraw',
                    'amount': amount,
                    'reference_type': 'daily_drawer',
                    'reference_id': daily_drawer['id'],
                    'description': description_input.text() or "سحب من درج اليومية",
                    'created_by': 'المستخدم',
                    'transaction_date': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
                transaction_id = TreasuryModel.add_transaction(transaction_data)
                
                if transaction_id:
                    QMessageBox.information(self, "نجاح العملية", f"تم سحب {amount:,.2f} {CURRENCY_SYMBOL} من درج اليومية بنجاح.")
                    self.refresh_page()
                else:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء تنفيذ العملية. يرجى المحاولة مرة أخرى.")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")
    
    def transfer_to_capital(self):
        """تحويل مبلغ من درج اليومية إلى رأس المال"""
        # التحقق من وجود درج مفتوح
        query = """
            SELECT id, treasury_id, open_amount, status, opened_at 
            FROM daily_cash_drawers 
            WHERE status = 'open'
            ORDER BY opened_at DESC LIMIT 1
        """
        daily_drawer = db.fetch_one(query)
        
        if not daily_drawer:
            QMessageBox.warning(self, "تنبيه", "لا يوجد درج مفتوح حالياً.")
            return
        
        # حساب الرصيد الحالي للدرج
        query = """
            SELECT 
                SUM(CASE WHEN transaction_type = 'deposit' THEN amount ELSE 0 END) as total_deposits,
                SUM(CASE WHEN transaction_type = 'withdraw' THEN amount ELSE 0 END) as total_withdrawals
            FROM treasury_transactions
            WHERE reference_type = 'daily_drawer' AND reference_id = ?
        """
        summary = db.fetch_one(query, (daily_drawer['id'],))
        
        open_amount = float(daily_drawer['open_amount'])
        total_deposits = float(summary['total_deposits']) if summary and summary['total_deposits'] else 0
        total_withdrawals = float(summary['total_withdrawals']) if summary and summary['total_withdrawals'] else 0
        current_balance = open_amount + total_deposits - total_withdrawals
        
        # الحصول على الخزينة الرئيسية
        treasuries = TreasuryModel.get_all_treasuries()
        if not treasuries:
            QMessageBox.critical(self, "خطأ", "لا توجد خزينة رئيسية متاحة.")
            return
        
        # إنشاء نافذة حوار لإدخال المبلغ والوصف
        dialog = QDialog(self)
        dialog.setWindowTitle("تحويل إلى رأس المال")
        dialog.setMinimumWidth(350)
        
        layout = QVBoxLayout(dialog)
        
        # إضافة حقول الإدخال
        form_layout = QFormLayout()
        
        amount_input = QLineEdit()
        amount_input.setValidator(QDoubleValidator(0.01, 1000000, 2))
        form_layout.addRow("المبلغ:", amount_input)
        
        description_input = QLineEdit()
        description_input.setText("تحويل من درج اليومية إلى رأس المال")
        form_layout.addRow("الوصف:", description_input)
        
        layout.addLayout(form_layout)
        
        # أزرار الموافقة والإلغاء
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)
        
        # عرض النافذة وانتظار الرد
        if dialog.exec_() == QDialog.Accepted:
            try:
                # التحقق من المبلغ
                amount_text = amount_input.text()
                if not amount_text:
                    QMessageBox.warning(self, "تنبيه", "يرجى إدخال المبلغ.")
                    return
                
                amount = float(amount_text)
                if amount <= 0:
                    QMessageBox.warning(self, "تنبيه", "يجب أن يكون المبلغ أكبر من صفر.")
                    return
                
                # التحقق من كفاية الرصيد
                if amount > current_balance:
                    QMessageBox.warning(self, "تنبيه", "المبلغ المطلوب أكبر من الرصيد المتاح.")
                    return
                
                # بدء المعاملة
                db.begin_transaction()
                
                try:
                    # 1. إضافة حركة سحب من درج اليومية
                    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    description = description_input.text() or "تحويل من درج اليومية إلى رأس المال"
                    
                    withdraw_data = {
                        'treasury_id': daily_drawer['treasury_id'],
                        'transaction_type': 'withdraw',
                        'amount': amount,
                        'reference_type': 'transfer_to_capital',
                        'reference_id': daily_drawer['id'],
                        'description': description,
                        'created_by': 'المستخدم',
                        'transaction_date': current_time
                    }
                    
                    withdraw_id = TreasuryModel.add_transaction(withdraw_data)
                    
                    if not withdraw_id:
                        raise Exception("فشل في تسجيل حركة السحب من درج اليومية")
                    
                    # 2. إضافة حركة إيداع في رأس المال
                    deposit_data = {
                        'treasury_id': treasuries[0]['id'],
                        'transaction_type': 'deposit',
                        'amount': amount,
                        'reference_type': 'transfer_from_daily',
                        'reference_id': daily_drawer['id'],
                        'description': description,
                        'created_by': 'المستخدم',
                        'transaction_date': current_time
                    }
                    
                    deposit_id = TreasuryModel.add_transaction(deposit_data)
                    
                    if not deposit_id:
                        raise Exception("فشل في تسجيل حركة الإيداع في رأس المال")
                    
                    # إتمام المعاملة
                    db.commit()
                    QMessageBox.information(self, "نجاح العملية", f"تم تحويل {amount:,.2f} {CURRENCY_SYMBOL} من درج اليومية إلى رأس المال بنجاح.")
                    self.refresh_page()
                    
                except Exception as e:
                    db.rollback()
                    QMessageBox.critical(self, "خطأ", f"فشلت عملية التحويل: {str(e)}")
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")
    
    def generate_treasury_report(self):
        """إنشاء تقرير للخزينة"""
        # هنا تأتي عملية إنشاء التقرير
        pass
    
    def filter_transactions(self):
        """تصفية حركات الخزينة حسب المعايير المحددة"""
        # تحديث الجدول بناءً على معايير التصفية المحددة
        self.refresh_transactions()
        
    def delete_transaction(self, transaction_id):
        """حذف حركة خزينة محددة"""
        # التأكد من رغبة المستخدم في الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف", 
            "هل أنت متأكد من حذف هذه الحركة؟ لا يمكن التراجع عن هذه العملية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
            
        if reply == QMessageBox.Yes:
            # حذف الحركة باستخدام النموذج
            result = TreasuryModel.delete_transaction(transaction_id)
            
            if result:
                QMessageBox.information(self, "نجاح العملية", "تم حذف الحركة بنجاح.")
                # تحديث الجدول والأرصدة
                self.refresh_page()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حذف الحركة. يرجى المحاولة مرة أخرى.")

    
    def refresh_transactions(self):
        """تحديث جدول حركات الخزينة"""
        # تحديد معايير الفلترة
        period = self.period_combo.currentText()
        transaction_type = self.type_combo.currentText()
        
        # تحديد نطاق التاريخ بناءً على الفترة المحددة
        today = datetime.datetime.now().date()
        start_date = None
        end_date = None
        
        if period == "اليوم":
            start_date = today.strftime("%Y-%m-%d 00:00:00")
            end_date = today.strftime("%Y-%m-%d 23:59:59")
        elif period == "الأسبوع الحالي":
            # الحصول على بداية الأسبوع (الأحد)
            start_of_week = today - datetime.timedelta(days=today.weekday() + 1)
            if start_of_week.weekday() > 5:  # إذا كان اليوم السبت
                start_of_week = today
            start_date = start_of_week.strftime("%Y-%m-%d 00:00:00")
            end_date = today.strftime("%Y-%m-%d 23:59:59")
        elif period == "الشهر الحالي":
            start_date = today.replace(day=1).strftime("%Y-%m-%d 00:00:00")
            end_date = today.strftime("%Y-%m-%d 23:59:59")
        elif period == "السنة الحالية":
            start_date = today.replace(month=1, day=1).strftime("%Y-%m-%d 00:00:00")
            end_date = today.strftime("%Y-%m-%d 23:59:59")
        
        # تحديد نوع الحركة للفلترة
        filter_type = None
        if transaction_type == "إيداع":
            filter_type = "deposit"
        elif transaction_type == "سحب":
            filter_type = "withdraw"
        elif transaction_type == "تحويل":
            filter_type = "transfer"
        
        # الحصول على حركات الخزينة
        transactions = TreasuryModel.get_transactions(
            start_date=start_date,
            end_date=end_date,
            transaction_type=filter_type
        )
        
        # تحديث الجدول
        self.transactions_table.setRowCount(0)  # مسح الجدول
        
        # متغيرات لحساب الملخص
        total_deposits = 0
        total_withdrawals = 0
        
        # إضافة الحركات للجدول
        for row, transaction in enumerate(transactions):
            self.transactions_table.insertRow(row)
            
            # تنسيق التاريخ
            date_str = transaction['transaction_date']
            if date_str:
                try:
                    date_obj = datetime.datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
                    formatted_date = date_obj.strftime("%Y-%m-%d %H:%M")
                except:
                    formatted_date = date_str
            else:
                formatted_date = ""
            
            # تحديد نوع الحركة بالعربية
            type_str = ""
            if transaction['transaction_type'] == "deposit":
                type_str = "إيداع"
                total_deposits += float(transaction['amount'])
            elif transaction['transaction_type'] == "withdraw":
                type_str = "سحب"
                total_withdrawals += float(transaction['amount'])
            elif transaction['transaction_type'] == "transfer":
                type_str = "تحويل"
                if transaction.get('reference_type') == 'transfer_to_capital':
                    total_withdrawals += float(transaction['amount'])
                else:
                    total_deposits += float(transaction['amount'])
            
            # تنسيق المبلغ والرصيد
            amount = float(transaction['amount'])
            balance = float(transaction['balance_after'])
            
            # إنشاء عناصر الجدول
            self.transactions_table.setItem(row, 0, QTableWidgetItem(formatted_date))
            self.transactions_table.setItem(row, 1, QTableWidgetItem(transaction['treasury_name']))
            self.transactions_table.setItem(row, 2, QTableWidgetItem(type_str))
            
            # عنصر المبلغ مع تنسيق اللون
            amount_item = QTableWidgetItem(f"{amount:,.2f}")
            if transaction['transaction_type'] == "deposit":
                amount_item.setForeground(QBrush(QColor("green")))
            else:
                amount_item.setForeground(QBrush(QColor("red")))
            self.transactions_table.setItem(row, 3, amount_item)
            
            # عنصر الرصيد
            self.transactions_table.setItem(row, 4, QTableWidgetItem(f"{balance:,.2f}"))
            self.transactions_table.setItem(row, 5, QTableWidgetItem(transaction['description']))
            
            # إضافة زر الحذف
            delete_btn = QPushButton("حذف")
            delete_btn.setProperty("transaction_id", transaction['id'])
            delete_btn.clicked.connect(lambda checked, tid=transaction['id']: self.delete_transaction(tid))
            self.transactions_table.setCellWidget(row, 6, delete_btn)
        
        # تحديث ملخص الحركات
        self.count_label.setText(f"عدد الحركات: {len(transactions)}")
        self.total_deposits_label.setText(f"إجمالي الإيداعات: {total_deposits:,.2f} {CURRENCY_SYMBOL}")
        self.total_withdrawals_label.setText(f"إجمالي المسحوبات: {total_withdrawals:,.2f} {CURRENCY_SYMBOL}")
        
        # حساب الصافي
        net_amount = total_deposits - total_withdrawals
        self.net_label.setText(f"الصافي: {net_amount:,.2f} {CURRENCY_SYMBOL}")
        
        # تلوين الصافي حسب القيمة
        if net_amount > 0:
            self.net_label.setStyleSheet("color: green; font-weight: bold;")
        elif net_amount < 0:
            self.net_label.setStyleSheet("color: red; font-weight: bold;")
        else:
            self.net_label.setStyleSheet("color: #000088; font-weight: bold;")
    
    def refresh_page(self):
        """تحديث بيانات الصفحة بالكامل"""
        # تحديث الأرصدة
        self.update_drawer_balances()
        
        # تحديث إجمالي المبيعات اليومية
        self.update_daily_sales()
        
        # تحديث حركات الخزينة
        self.refresh_transactions()
    
    def update_drawer_balances(self):
        """تحديث أرصدة الأدراج"""
        # الحصول على قائمة الخزائن
        treasuries = TreasuryModel.get_all_treasuries()
        
        if not treasuries:
            return
        
        # الخزينة الرئيسية (رأس المال)
        main_treasury = treasuries[0] if treasuries else None
        if main_treasury:
            capital_balance = float(main_treasury['current_balance'])
            self.capital_balance.setText(f"{capital_balance:,.2f} {CURRENCY_SYMBOL}")
        
        # التحقق من حالة درج اليومية
        # البحث عن درج يومي مفتوح
        query = """
            SELECT id, treasury_id, open_amount, status, opened_at 
            FROM daily_cash_drawers 
            WHERE status = 'open'
            ORDER BY opened_at DESC LIMIT 1
        """
        daily_drawer = db.fetch_one(query)
        
        if daily_drawer:
            # الحصول على حركات الدرج اليومي
            query = """
                SELECT 
                    SUM(CASE WHEN transaction_type = 'deposit' THEN amount ELSE 0 END) as total_deposits,
                    SUM(CASE WHEN transaction_type = 'withdraw' THEN amount ELSE 0 END) as total_withdrawals
                FROM treasury_transactions
                WHERE reference_type = 'daily_drawer' AND reference_id = ?
            """
            summary = db.fetch_one(query, (daily_drawer['id'],))
            
            # حساب الرصيد الحالي للدرج اليومي
            open_amount = float(daily_drawer['open_amount'])
            total_deposits = float(summary['total_deposits']) if summary and summary['total_deposits'] else 0
            total_withdrawals = float(summary['total_withdrawals']) if summary and summary['total_withdrawals'] else 0
            daily_balance = open_amount + total_deposits - total_withdrawals
            
            # تحديث واجهة المستخدم
            self.daily_balance.setText(f"{daily_balance:,.2f} {CURRENCY_SYMBOL}")
            self.daily_status.setText("مفتوح")
            self.daily_status.setStyleSheet("color: green; font-weight: bold;")
        else:
            # لا يوجد درج مفتوح
            self.daily_balance.setText(f"0.00 {CURRENCY_SYMBOL}")
            self.daily_status.setText("مغلق")
            self.daily_status.setStyleSheet("color: red; font-weight: bold;")

    
    def apply_styles(self):
        """تطبيق التنسيقات على مكونات النافذة"""
        # تنسيقات الأدراج
        drawer_style = """
            QFrame#capital_drawer, QFrame#daily_drawer {
                border: 1px solid #CCCCCC;
                border-radius: 8px;
                background-color: #F8F8F8;
            }
            QLabel#drawer_title {
                color: #444444;
                    padding: 5px;
                border-bottom: 1px solid #CCCCCC;
            }
            QLabel#balance_value {
                color: #095B00;
                font-weight: bold;
                padding: 10px;
            }
            QPushButton#drawer_button {
                padding: 8px;
                border-radius: 4px;
            }
            QLabel#status_value {
                font-weight: bold;
            }
            QLabel#sales_value {
                font-weight: bold;
                padding: 5px;
            }
        """
        
        # تنسيقات جدول الحركات
        table_style = """
            QFrame#transactions_frame {
                border: 1px solid #CCCCCC;
                border-radius: 8px;
                background-color: #FFFFFF;
            }
            QTableWidget#transactions_table {
                border: 1px solid #DDDDDD;
                border-radius: 4px;
            }
            QTableWidget#transactions_table::item {
                padding: 6px;
            }
            QLabel#summary_deposits {
                color: green;
                font-weight: bold;
            }
            QLabel#summary_withdrawals {
                color: #CC0000;
                font-weight: bold;
            }
            QLabel#summary_net {
                color: #000088;
                font-weight: bold;
            }
        """
        
        self.setStyleSheet(drawer_style + table_style)
        
        # بدء تحديث تلقائي للبيانات
        self.start_auto_refresh()
        
    def update_daily_sales(self):
        """تحديث إجمالي المبيعات اليومية"""
        try:
            # الحصول على التاريخ الحالي بتنسيق YYYY/MM/DD
            today = datetime.datetime.now().strftime("%Y/%m/%d")
            
            # استعلام لجلب إجمالي المبيعات لليوم الحالي
            query = """
                SELECT SUM(total) as daily_sales
                FROM invoices
                WHERE date LIKE ? AND status != 'ملغية'
            """
            
            result = db.fetch_one(query, (f"{today}%",))
            
            # تحديث عرض إجمالي المبيعات
            daily_sales = float(result['daily_sales']) if result and result['daily_sales'] else 0
            self.daily_sales.setText(f"{daily_sales:,.2f} {CURRENCY_SYMBOL}")
            
            # تغيير لون النص بناءً على قيمة المبيعات
            if daily_sales > 0:
                self.daily_sales.setStyleSheet("color: #16a34a;")  # لون أخضر للمبيعات الإيجابية
            else:
                self.daily_sales.setStyleSheet("color: #2563eb;")  # لون أزرق للمبيعات الصفرية
                
        except Exception as e:
            print(f"خطأ في تحديث إجمالي المبيعات اليومية: {str(e)}")
            self.daily_sales.setText(f"0.00 {CURRENCY_SYMBOL}")
            self.daily_sales.setStyleSheet("color: #2563eb;")
            
    def start_auto_refresh(self):
        """بدء تحديث تلقائي للبيانات"""
        # إنشاء مؤقت لتحديث البيانات كل 5 دقائق
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.update_daily_sales)
        self.refresh_timer.start(300000)  # 300000 مللي ثانية = 5 دقائق
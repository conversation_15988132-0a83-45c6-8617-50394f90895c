# متطلبات Smart Manager
# Smart Manager Requirements

# واجهة المستخدم الرسومية
PyQt5==5.15.9
PyQt5-Qt5==5.15.2
PyQt5-sip==12.11.0

# قاعدة البيانات (مدمجة مع Python)
# sqlite3 (built-in with Python)

# معالجة التاريخ والوقت (مدمجة مع Python)
# datetime (built-in with Python)

# التشفير (مدمج مع Python)
# hashlib (built-in with Python)

# معالجة JSON (مدمج مع Python)
# json (built-in with Python)

# نسخ الملفات (مدمج مع Python)
# shutil (built-in with Python)

# معالجة المسارات (مدمج مع Python)
# os (built-in with Python)

# تشغيل العمليات الخارجية (مدمج مع Python)
# subprocess (built-in with Python)

# معالجة الأخطاء (مدمج مع Python)
# traceback (built-in with Python)

# ملاحظة: يُنصح بتثبيت المكتبات التالية لتحسين الأداء والأمان
# Note: The following libraries are recommended for better performance and security

# تشفير كلمات المرور (موصى به)
# bcrypt==4.0.1

# معالجة ملفات Excel (للتصدير المستقبلي)
# openpyxl==3.1.2

# إنشاء ملفات PDF (للتقارير المستقبلية)
# reportlab==4.0.4

# معالجة الباركود (للميزات المستقبلية)
# python-barcode==0.14.0
# qrcode==7.4.2

# تحسينات الأداء
# numpy==1.24.3
# pandas==2.0.3
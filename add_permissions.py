"""
<PERSON><PERSON><PERSON> to add new permissions to the system
"""

import os
import re

def add_permission_to_category(file_path, category, permission):
    """Add a permission to a specific category in the file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Find the category pattern
        pattern = f'"{category}":\\s*\\[(.*?)\\]'
        match = re.search(pattern, content, re.DOTALL)
        
        if match:
            # Check if permission already exists
            if f'"{permission}"' not in match.group(1):
                # Add the new permission
                new_content = content.replace(
                    match.group(0),
                    match.group(0).replace(
                        ']',
                        f',\n                    "{permission}"\n                ]'
                    )
                )
                
                # Write the updated content back to the file
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(new_content)
                
                print(f"Added permission '{permission}' to category '{category}' in {file_path}")
                return True
            else:
                print(f"Permission '{permission}' already exists in category '{category}' in {file_path}")
                return False
        else:
            # If category doesn't exist, add it with the new permission
            # Find the last category
            last_category_pattern = r'(.*?"[^"]+?":\s*\[.*?\]\s*})'
            last_match = re.search(last_category_pattern, content, re.DOTALL)
            
            if last_match:
                new_category = f'}},\n                "{category}": [\n                    "{permission}"\n                ]'
                new_content = content.replace(
                    last_match.group(1),
                    last_match.group(1).replace('}', new_category)
                )
                
                # Write the updated content back to the file
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(new_content)
                
                print(f"Added new category '{category}' with permission '{permission}' in {file_path}")
                return True
            else:
                print(f"Could not find a place to add the new category in {file_path}")
                return False
    except Exception as e:
        print(f"Error updating {file_path}: {str(e)}")
        return False

def add_permissions():
    """Add all the new permissions to the system"""
    files_to_update = [
        'views/settings.py',
        'models/users.py'
    ]
    
    # New permissions to add
    permissions = [
        ("العملاء", "تسجيل دفعة من عميل"),
        ("الموردين", "تسجيل دفعة لمورد"),
        ("الفواتير", "تعديل تصميم الفاتورة"),
        ("المدفوعات", "عرض سجل المدفوعات")
    ]
    
    for file_path in files_to_update:
        if os.path.exists(file_path):
            for category, permission in permissions:
                add_permission_to_category(file_path, category, permission)
        else:
            print(f"File {file_path} does not exist")

if __name__ == "__main__":
    add_permissions()

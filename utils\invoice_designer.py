# -*- coding: utf-8 -*-
"""
وحدة تصميم الفواتير المشتركة
توفر دالة موحدة لإنشاء تصميم الفاتورة يتم استخدامها في جميع أنحاء التطبيق
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QTableWidget,
                            QTableWidgetItem, QHeaderView, QFrame)
from PyQt5.QtCore import Qt, QSettings
from PyQt5.QtGui import QFont
import datetime


class InvoiceDesigner:
    """فئة تصميم الفواتير المشتركة"""

    @staticmethod
    def create_invoice_widget(invoice_data=None, items_data=None, is_preview=False):
        """
        إنشاء widget الفاتورة بتصميم موحد

        Args:
            invoice_data (dict): بيانات الفاتورة (اختياري للمعاينة)
            items_data (list): بيانات عناصر الفاتورة (اختياري للمعاينة)
            is_preview (bool): هل هذه معاينة في الإعدادات أم فاتورة حقيقية

        Returns:
            QWidget: widget الفاتورة المصممة
        """
        # إنشاء widget الفاتورة
        invoice_widget = QWidget()
        invoice_layout = QVBoxLayout(invoice_widget)
        invoice_layout.setContentsMargins(5, 5, 5, 5)  # تقليل الهوامش أكثر لطابعات XPrinter
        invoice_layout.setSpacing(3)  # تقليل المسافة بين العناصر أكثر

        # جلب معلومات الشركة من الإعدادات
        settings = QSettings("MyCompany", "SmartManager")
        company_name_text = settings.value("company_name", "اسم الشركة")
        company_address_text = settings.value("company_address", "عنوان الشركة")
        company_phone_text = settings.value("company_phone", "01234567890")

        invoice_notes_text = settings.value("invoice_notes", "شكراً لتعاملكم معنا")

        # ترويسة الفاتورة - تنسيق مركزي لطابعات XPrinter الحرارية
        # معلومات الشركة
        company_name_label = QLabel(company_name_text)
        company_name_label.setFont(QFont("Courier New", 12, QFont.Bold))  # خط واضح للطباعة الحرارية
        company_name_label.setAlignment(Qt.AlignCenter)
        company_name_label.setStyleSheet("color: #000000; font-weight: bold;")
        invoice_layout.addWidget(company_name_label)



        # معلومات الاتصال بالشركة - مختصرة
        contact_info = QLabel(f"{company_address_text}\n"
                             f"هاتف: {company_phone_text}")
        contact_info.setAlignment(Qt.AlignCenter)
        contact_info.setFont(QFont("Courier New", 8))  # خط واضح للمعلومات
        contact_info.setStyleSheet("color: #000000;")
        contact_info.setWordWrap(True)  # تمكين التفاف النص
        invoice_layout.addWidget(contact_info)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        invoice_layout.addWidget(separator)

        # معلومات الفاتورة - تنسيق مبسط لطابعات XPrinter الحرارية
        if is_preview:
            # للمعاينة في الإعدادات - استخدم بيانات تجريبية
            invoice_info_label = QLabel(
                f"رقم: INV-12345 | "
                f"التاريخ: {datetime.datetime.now().strftime('%Y/%m/%d')}"
            )
            customer_info = QLabel("العميل: عميل تجريبي | طريقة الدفع: نقدي")
        else:
            # للفواتير الحقيقية - استخدم البيانات الفعلية
            if invoice_data:
                invoice_info_label = QLabel(
                    f"رقم: {invoice_data['reference_number']} | "
                    f"التاريخ: {invoice_data['date'][:10]}"
                )
                # الحصول على اسم العميل من بيانات الفاتورة
                customer_name = invoice_data.get('customer_name', 'عميل غير محدد')

                customer_info = QLabel(f"العميل: {customer_name} | طريقة الدفع: {invoice_data['payment_method']}")
            else:
                invoice_info_label = QLabel("رقم: --- | التاريخ: ---")
                customer_info = QLabel("العميل: --- | طريقة الدفع: ---")

        invoice_info_label.setFont(QFont("Courier New", 9, QFont.Bold))
        invoice_info_label.setStyleSheet("color: #000000; font-weight: bold;")
        invoice_info_label.setAlignment(Qt.AlignCenter)
        invoice_layout.addWidget(invoice_info_label)

        customer_info.setFont(QFont("Courier New", 8))
        customer_info.setStyleSheet("color: #000000;")
        customer_info.setAlignment(Qt.AlignCenter)
        customer_info.setWordWrap(True)
        invoice_layout.addWidget(customer_info)

        # إضافة فاصل
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.HLine)
        separator2.setFrameShadow(QFrame.Sunken)
        invoice_layout.addWidget(separator2)

        # جدول عناصر الفاتورة - بسيط وبدون ألوان لطابعات XPrinter الحرارية
        items_table = QTableWidget()
        items_table.setColumnCount(4)  # تقليل عدد الأعمدة
        items_table.setHorizontalHeaderLabels(["المنتج", "الكمية", "السعر", "الإجمالي"])

        # تعيين عرض الأعمدة بشكل مخصص لإعطاء مساحة أكبر لاسم المنتج
        header = items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # عمود المنتج يأخذ المساحة المتبقية
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # عمود الكمية عرض ثابت
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # عمود السعر عرض ثابت
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # عمود الإجمالي عرض ثابت

        # تعيين عرض الأعمدة الثابتة
        items_table.setColumnWidth(1, 50)   # عمود الكمية - 50 بكسل
        items_table.setColumnWidth(2, 60)   # عمود السعر - 60 بكسل
        items_table.setColumnWidth(3, 70)   # عمود الإجمالي - 70 بكسل

        items_table.setEditTriggers(QTableWidget.NoEditTriggers)
        items_table.setAlternatingRowColors(False)  # إلغاء الألوان المتناوبة
        items_table.setFont(QFont("Courier New", 8))  # خط واضح للجدول

        # تكبير حجم الخط للعناوين
        header_font = QFont("Courier New", 10, QFont.Bold)
        items_table.horizontalHeader().setFont(header_font)

        # تكبير ارتفاع صف العناوين
        items_table.horizontalHeader().setFixedHeight(30)

        # تعيين نمط بسيط جداً للجدول مناسب للطباعة الحرارية
        items_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #000000;
                gridline-color: #000000;
                background-color: white;
                font-family: "Courier New", monospace;
            }
            QTableWidget::item {
                border-bottom: 1px solid #000000;
                border-right: 1px solid #000000;
                padding: 4px;
                color: #000000;
                word-wrap: break-word;
                vertical-align: top;
            }
            QHeaderView::section {
                background-color: white;
                border: 1px solid #000000;
                padding: 5px;
                font-weight: bold;
                color: #000000;
                text-align: center;
            }
        """)

        # تحديد البيانات المراد عرضها
        if is_preview:
            # بيانات تجريبية للمعاينة مع أسماء منتجات طويلة لاختبار العرض
            display_items = [
                {"name": "منتج تجريبي بأسم طويل جداً لاختبار العرض", "quantity": "2", "price": "50.00", "total": "100.00"},
                {"name": "منتج آخر", "quantity": "1", "price": "75.00", "total": "75.00"},
                {"name": "منتج تجريبي ثالث بأسم طويل أيضاً", "quantity": "3", "price": "30.00", "total": "90.00"}
            ]
        else:
            # البيانات الفعلية أو بيانات تجريبية إذا لم تكن متوفرة
            display_items = items_data if items_data else [
                {"product_name": "منتج افتراضي", "quantity": 1, "unit_price": 0.00, "total_price": 0.00}
            ]

        items_table.setRowCount(len(display_items))

        # تعبئة بيانات الجدول
        for row, item in enumerate(display_items):
            # اسم المنتج
            if 'product_name' in item:
                product_name = str(item['product_name'])
            else:
                product_name = item.get('name', 'منتج')

            product_item = QTableWidgetItem(product_name)
            product_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            # تمكين التفاف النص لأسماء المنتجات الطويلة
            product_item.setFlags(product_item.flags() | Qt.TextWordWrap)
            items_table.setItem(row, 0, product_item)

            # الكمية
            if 'quantity' in item:
                quantity = str(item['quantity'])
            else:
                quantity = item.get('quantity', '1')

            quantity_item = QTableWidgetItem(quantity)
            quantity_item.setTextAlignment(Qt.AlignCenter)
            items_table.setItem(row, 1, quantity_item)

            # السعر
            if 'unit_price' in item:
                price = f"{item['unit_price']:.2f}"
            else:
                price = item.get('price', '0.00')

            price_item = QTableWidgetItem(price)
            price_item.setTextAlignment(Qt.AlignCenter)
            items_table.setItem(row, 2, price_item)

            # الإجمالي
            if 'total_price' in item:
                total = f"{item['total_price']:.2f}"
            else:
                total = item.get('total', '0.00')

            total_item = QTableWidgetItem(total)
            total_item.setTextAlignment(Qt.AlignCenter)
            items_table.setItem(row, 3, total_item)

        # تعيين ارتفاع الصفوف ليكون مناسباً مع إمكانية التوسع للنصوص الطويلة
        for row in range(items_table.rowCount()):
            # حساب ارتفاع مناسب بناءً على طول النص
            product_text = items_table.item(row, 0).text()
            # تقدير عدد الأسطر المطلوبة (تقريباً 25 حرف لكل سطر في الطابعة الحرارية)
            estimated_lines = max(1, len(product_text) // 25 + 1)
            row_height = max(25, estimated_lines * 20)  # حد أدنى 25 بكسل
            items_table.setRowHeight(row, row_height)

        # تعيين ارتفاع مناسب للجدول بدون شريط تمرير
        header_height = 30  # ارتفاع العناوين
        border_margin = 4

        # حساب الارتفاع الإجمالي بناءً على ارتفاع كل صف
        total_rows_height = sum(items_table.rowHeight(row) for row in range(items_table.rowCount()))
        total_height = header_height + total_rows_height + border_margin

        items_table.setFixedHeight(total_height)
        items_table.setMinimumWidth(350)  # زيادة العرض الأدنى لاستيعاب النصوص الطويلة
        items_table.setMaximumWidth(450)  # زيادة العرض الأقصى

        # إزالة أشرطة التمرير
        items_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        items_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        invoice_layout.addWidget(items_table)

        # إجمالي الفاتورة فقط - تنسيق بسيط لطابعات XPrinter الحرارية
        total_layout = QHBoxLayout()
        total_layout.addStretch()

        # تحديد المبلغ الإجمالي
        if is_preview:
            total_amount = 265.00  # مبلغ تجريبي للمعاينة
        else:
            total_amount = invoice_data.get('total', 0.00) if invoice_data else 0.00

        # إنشاء label لإجمالي الفاتورة
        total_label = QLabel(f"الإجمالي: {total_amount:.2f} ج.م")
        total_label.setFont(QFont("Courier New", 14, QFont.Bold))  # خط واضح للطباعة الحرارية
        total_label.setAlignment(Qt.AlignCenter)
        total_label.setStyleSheet("""
            QLabel {
                border: 2px solid #000000;
                border-radius: 0px;
                padding: 8px 16px;
                background-color: white;
                color: #000000;
                font-weight: bold;
            }
        """)

        total_layout.addWidget(total_label)
        total_layout.addStretch()

        invoice_layout.addLayout(total_layout)

        # إضافة مساحة صغيرة
        invoice_layout.addSpacing(10)

        # تذييل الفاتورة - تنسيق بسيط لطابعات XPrinter الحرارية
        footer_layout = QVBoxLayout()
        footer_layout.setSpacing(3)
        footer_layout.setAlignment(Qt.AlignCenter)

        # ملاحظات الفاتورة
        notes_label = QLabel(invoice_notes_text)
        notes_label.setFont(QFont("Courier New", 9))
        notes_label.setStyleSheet("color: #000000;")
        notes_label.setAlignment(Qt.AlignCenter)
        notes_label.setWordWrap(True)
        footer_layout.addWidget(notes_label)

        # إضافة رقم الباركود بتنسيق بسيط
        barcode_label = QLabel("*123456789*")
        barcode_label.setFont(QFont("Courier New", 10, QFont.Bold))
        barcode_label.setStyleSheet("color: #000000; font-weight: bold;")
        barcode_label.setAlignment(Qt.AlignCenter)
        footer_layout.addWidget(barcode_label)

        # إضافة تاريخ ووقت الطباعة
        datetime_label = QLabel(datetime.datetime.now().strftime('%Y/%m/%d %H:%M'))
        datetime_label.setFont(QFont("Courier New", 7))
        datetime_label.setStyleSheet("color: #000000;")
        datetime_label.setAlignment(Qt.AlignCenter)
        footer_layout.addWidget(datetime_label)

        # إضافة تخطيط التذييل إلى تخطيط الفاتورة
        invoice_layout.addLayout(footer_layout)

        return invoice_widget

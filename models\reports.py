"""
وحدة التقارير والإحصائيات - توفر وظائف لاستخراج واستعراض تقارير وإحصائيات المبيعات والمخزون والأرباح
"""

from models.database import db
import datetime
import calendar
from models.products import ProductModel
from models.invoices import InvoiceModel
from models.purchases import PurchaseModel
from models.customers import CustomerModel
from models.suppliers import SupplierModel

class ReportModel:
    """نموذج التعامل مع التقارير والإحصائيات"""
    
    @staticmethod
    def get_daily_sales_report(start_date=None, end_date=None):
        """استخراج تقرير المبيعات اليومية"""
        # Use YYYY-MM-DD format internally for parameter consistency
        if not start_date:
            start_date = datetime.datetime.now().strftime("%Y-%m-%d")
        
        if not end_date:
            end_date = start_date

        # Convert YYYY-MM-DD parameters to the format needed for DB comparison (YYYY/MM/DD HH:MM:SS)
        try:
            start_dt_param = start_date.replace('-', '/') + " 00:00:00"
            
            end_date_obj = datetime.datetime.strptime(end_date, '%Y-%m-%d')
            next_day_obj = end_date_obj + datetime.timedelta(days=1)
            end_dt_exclusive_param = next_day_obj.strftime('%Y/%m/%d 00:00:00')
        except ValueError:
             # Handle potential format errors if needed, fallback or raise error
             print(f"Error converting date parameters: {start_date}, {end_date}")
             # Fallback to prevent crash, might return empty
             start_dt_param = datetime.datetime.now().strftime("%Y/%m/%d 00:00:00")
             end_dt_exclusive_param = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y/%m/%d 00:00:00')


        # استعلام لجلب الفواتير مع معلومات العميل (Compare full timestamp)
        query = """
            SELECT i.id, i.reference_number, i.date, i.total, i.status, 
                   i.payment_method, c.name as customer_name
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE i.date >= ? AND i.date < ?
            ORDER BY i.id DESC
        """
        sales_data = db.fetch_all(query, (start_dt_param, end_dt_exclusive_param))
        
        # استعلام لحساب إجماليات الفترة (Compare full timestamp)
        summary_query = """
            SELECT COUNT(*) as total_invoices, 
                   SUM(total) as total_amount,
                   SUM(CASE WHEN status = 'مدفوعة' THEN total ELSE 0 END) as paid_amount,
                   SUM(CASE WHEN status = 'غير مدفوعة' THEN total ELSE 0 END) as unpaid_amount
            FROM invoices
            WHERE date >= ? AND date < ?
        """
        summary = db.fetch_one(summary_query, (start_dt_param, end_dt_exclusive_param))
        
        # استخراج إجمالي عدد المنتجات المباعة (Compare full timestamp)
        items_query = """
            SELECT SUM(ii.quantity) as total_items
            FROM invoice_items ii
            JOIN invoices i ON ii.invoice_id = i.id
            WHERE i.date >= ? AND i.date < ?
        """
        items_result = db.fetch_one(items_query, (start_dt_param, end_dt_exclusive_param))
        total_items = items_result.get('total_items', 0) if items_result else 0
        
        # حساب المتوسط إذا كان لدينا فواتير
        avg_invoice = 0
        if summary and summary.get('total_invoices', 0) > 0:
            # Ensure total_amount is not None before division
            total_amount_val = summary.get('total_amount', 0) or 0
            avg_invoice = total_amount_val / summary.get('total_invoices')

        # تجميع البيانات في قاموس واحد
        return {
            "start_date": start_date, # Keep original YYYY-MM-DD for display consistency if needed
            "end_date": end_date,     # Keep original YYYY-MM-DD
            "sales": sales_data,
            "summary": {
                "total_invoices": summary.get('total_invoices', 0) if summary else 0,
                "total_amount": summary.get('total_amount', 0) or 0 if summary else 0, # Handle None
                "paid_amount": summary.get('paid_amount', 0) or 0 if summary else 0,    # Handle None
                "unpaid_amount": summary.get('unpaid_amount', 0) or 0 if summary else 0, # Handle None
                "total_items": total_items,
                "avg_invoice": avg_invoice
            }
        }
    
    @staticmethod
    def get_monthly_sales_report(year, month):
        """استخراج تقرير المبيعات الشهرية"""
        # تنسيق تاريخ البداية والنهاية للشهر المحدد
        if month < 10:
            month_str = f"0{month}"
        else:
            month_str = str(month)
            
        start_date = f"{year}/{month_str}/01"
        
        # تحديد تاريخ نهاية الشهر
        if month == 12:
            end_date = f"{year+1}/01/01"
        else:
            next_month = month + 1
            if next_month < 10:
                next_month_str = f"0{next_month}"
            else:
                next_month_str = str(next_month)
            end_date = f"{year}/{next_month_str}/01"
        
        # استعلام لجلب الفواتير مع معلومات العميل
        query = """
            SELECT i.id, i.reference_number, i.date, i.total, i.status, 
                   i.payment_method, c.name as customer_name
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE i.date >= ? AND i.date < ?
            ORDER BY i.date DESC
        """
        sales_data = db.fetch_all(query, (start_date, end_date))
        
        # استعلام لحساب إجماليات الشهر
        summary_query = """
            SELECT COUNT(*) as total_invoices, 
                   SUM(total) as total_amount,
                   SUM(CASE WHEN status = 'مدفوعة' THEN total ELSE 0 END) as paid_amount,
                   SUM(CASE WHEN status = 'غير مدفوعة' THEN total ELSE 0 END) as unpaid_amount
            FROM invoices
            WHERE date >= ? AND date < ?
        """
        summary = db.fetch_one(summary_query, (start_date, end_date))
        
        # استخراج إجمالي عدد المنتجات المباعة
        items_query = """
            SELECT SUM(ii.quantity) as total_items
            FROM invoice_items ii
            JOIN invoices i ON ii.invoice_id = i.id
            WHERE i.date >= ? AND i.date < ?
        """
        items_result = db.fetch_one(items_query, (start_date, end_date))
        total_items = items_result.get('total_items', 0) if items_result else 0
        
        # استخراج المبيعات اليومية للشهر
        days_in_month = calendar.monthrange(year, month)[1]
        daily_sales = []
        
        for day in range(1, days_in_month + 1):
            if day < 10:
                day_str = f"0{day}"
            else:
                day_str = str(day)
                
            day_date = f"{year}/{month_str}/{day_str}"
            
            day_query = """
                SELECT SUM(total) as day_total
                FROM invoices
                WHERE date = ?
            """
            day_result = db.fetch_one(day_query, (day_date,))
            
            daily_sales.append({
                "day": day,
                "date": day_date,
                "total": day_result.get('day_total', 0) if day_result else 0
            })
        
        # تجميع البيانات في قاموس واحد
        return {
            "year": year,
            "month": month,
            "month_name": calendar.month_name[month],
            "start_date": start_date,
            "end_date": end_date,
            "sales": sales_data,
            "daily_sales": daily_sales,
            "summary": {
                "total_invoices": summary.get('total_invoices', 0) if summary else 0,
                "total_amount": summary.get('total_amount', 0) if summary else 0,
                "paid_amount": summary.get('paid_amount', 0) if summary else 0,
                "unpaid_amount": summary.get('unpaid_amount', 0) if summary else 0,
                "total_items": total_items
            }
        }
    
    @staticmethod
    def get_inventory_report(category=None, search_text=None):
        """استخراج تقرير المخزون"""
        base_query = """
            SELECT id, code, name, category, quantity, min_quantity, 
                   price, cost, cost * quantity as total_value
            FROM products
            WHERE 1=1 AND product_type != 'service' AND product_type != 'category_placeholder'
        """
        params = []
        
        # إضافة فلتر الفئة
        if category and category != "الكل":
            base_query += " AND category = ?"
            params.append(category)
        
        # إضافة فلتر البحث
        if search_text:
            base_query += " AND (name LIKE ? OR code LIKE ?)"
            search_pattern = f"%{search_text}%"
            params.extend([search_pattern, search_pattern])
        
        # ترتيب النتائج حسب الكمية (الأقل أولاً)
        base_query += " ORDER BY quantity"
        
        products = db.fetch_all(base_query, params)
        
        # استخراج إحصائيات المخزون
        stats_query = """
            SELECT COUNT(*) as total_products,
                   SUM(cost * quantity) as total_value,
                   SUM(quantity) as total_quantity,
                   COUNT(CASE WHEN quantity <= min_quantity THEN 1 END) as low_stock_count
            FROM products
            WHERE product_type != 'service' AND product_type != 'category_placeholder'
        """
        
        if category and category != "الكل":
            stats_query += " AND category = ?"
            stats_params = [category]
        else:
            stats_params = []
            
        stats = db.fetch_one(stats_query, stats_params)
        
        # تجميع البيانات في قاموس واحد
        return {
            "products": products,
            "stats": {
                "total_products": stats.get('total_products', 0) if stats else 0,
                "total_value": stats.get('total_value', 0) if stats else 0,
                "total_quantity": stats.get('total_quantity', 0) if stats else 0,
                "low_stock_count": stats.get('low_stock_count', 0) if stats else 0
            }
        }
    
    @staticmethod
    def get_profits_report(start_date=None, end_date=None, category=None):
        """استخراج تقرير الأرباح"""
        # Use YYYY-MM-DD format internally for parameter consistency
        if not start_date:
            now = datetime.datetime.now()
            start_date = f"{now.year}-{now.month:02d}-01"
            
        if not end_date:
            now = datetime.datetime.now()
            end_date = now.strftime("%Y-%m-%d")

        # Convert YYYY-MM-DD parameters to the format needed for DB comparison (YYYY/MM/DD HH:MM:SS)
        try:
            start_dt_param = start_date.replace('-', '/') + " 00:00:00"
            
            end_date_obj = datetime.datetime.strptime(end_date, '%Y-%m-%d')
            next_day_obj = end_date_obj + datetime.timedelta(days=1)
            end_dt_exclusive_param = next_day_obj.strftime('%Y/%m/%d 00:00:00')
        except ValueError:
             print(f"Error converting date parameters for profit report: {start_date}, {end_date}")
             # Fallback
             start_dt_param = datetime.datetime.now().strftime("%Y/%m/01 00:00:00")
             end_dt_exclusive_param = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y/%m/%d 00:00:00')

        # الاستعلام الأساسي لاستخراج بيانات المبيعات مع الأرباح (Compare full timestamp)
        base_query = """
            SELECT ii.product_id, p.name as product_name, p.code as product_code, p.category,
                   SUM(ii.quantity) as total_quantity,
                   SUM(ii.total_price) as total_sales,
                   SUM(ii.quantity * p.cost) as total_cost,
                   SUM(ii.total_price) - SUM(ii.quantity * p.cost) as profit
            FROM invoice_items ii
            JOIN invoices i ON ii.invoice_id = i.id
            JOIN products p ON ii.product_id = p.id
            WHERE i.date >= ? AND i.date < ?
            AND p.product_type != 'category_placeholder'
        """
        
        params = [start_dt_param, end_dt_exclusive_param]
        
        # إضافة فلتر الفئة
        if category and category != "الكل":
            base_query += " AND p.category = ?"
            params.append(category)
        
        # تجميع البيانات حسب المنتج
        base_query += " GROUP BY ii.product_id ORDER BY profit DESC"
        
        sales_data = db.fetch_all(base_query, params)
        
        # استخراج إجماليات الفترة (Compare full timestamp)
        summary_query = """
            SELECT SUM(ii.total_price) as total_sales,
                   SUM(ii.quantity * p.cost) as total_cost,
                   SUM(ii.quantity) as total_quantity
            FROM invoice_items ii
            JOIN invoices i ON ii.invoice_id = i.id
            JOIN products p ON ii.product_id = p.id
            WHERE i.date >= ? AND i.date < ?
            AND p.product_type != 'category_placeholder'
        """
        
        summary_params = [start_dt_param, end_dt_exclusive_param]
        
        if category and category != "الكل":
            summary_query += " AND p.category = ?"
            summary_params.append(category)
        
        summary = db.fetch_one(summary_query, summary_params)
        
        # حساب إجمالي الربح
        total_profit = 0
        if summary:
            total_sales = summary.get('total_sales', 0) or 0
            total_cost = summary.get('total_cost', 0) or 0
            total_profit = total_sales - total_cost
        
        # تجميع البيانات في قاموس واحد
        return {
            "start_date": start_date, # Original YYYY-MM-DD
            "end_date": end_date,     # Original YYYY-MM-DD
            "category": category,
            "sales_data": sales_data,
            "summary": {
                "total_sales": summary.get('total_sales', 0) or 0 if summary else 0,
                "total_cost": summary.get('total_cost', 0) or 0 if summary else 0,
                "total_quantity": summary.get('total_quantity', 0) or 0 if summary else 0,
                "total_profit": total_profit
            }
        }
    
    @staticmethod
    def get_dashboard_stats():
        """استخراج إحصائيات للوحة التحكم الرئيسية"""
        # تاريخ اليوم
        today = datetime.datetime.now().strftime("%Y/%m/%d")
        
        # بداية الشهر الحالي
        now = datetime.datetime.now()
        current_month_start = f"{now.year}/{now.month:02d}/01"
        
        # بداية العام الحالي
        current_year_start = f"{now.year}/01/01"
        
        # إحصائيات المبيعات اليومية
        daily_sales_query = """
            SELECT COUNT(*) as invoices_count, 
                   SUM(total) as total_amount
            FROM invoices
            WHERE date = ?
        """
        daily_sales = db.fetch_one(daily_sales_query, (today,))
        
        # إحصائيات المبيعات الشهرية
        monthly_sales_query = """
            SELECT COUNT(*) as invoices_count, 
                   SUM(total) as total_amount
            FROM invoices
            WHERE date >= ?
        """
        monthly_sales = db.fetch_one(monthly_sales_query, (current_month_start,))
        
        # إحصائيات المبيعات السنوية
        yearly_sales_query = """
            SELECT COUNT(*) as invoices_count, 
                   SUM(total) as total_amount
            FROM invoices
            WHERE date >= ?
        """
        yearly_sales = db.fetch_one(yearly_sales_query, (current_year_start,))
        
        # إحصائيات المخزون
        inventory_query = """
            SELECT COUNT(*) as total_products,
                   SUM(price * quantity) as inventory_value,
                   COUNT(CASE WHEN quantity <= min_quantity THEN 1 END) as low_stock_count
            FROM products
            WHERE product_type != 'service'
        """
        inventory_stats = db.fetch_one(inventory_query)
        
        # إحصائيات العملاء
        customers_query = "SELECT COUNT(*) as total_customers FROM customers"
        customers_stats = db.fetch_one(customers_query)
        
        # أفضل 5 منتجات مبيعًا هذا الشهر
        top_products_query = """
            SELECT p.name, SUM(ii.quantity) as total_sold, SUM(ii.total_price) as total_sales
            FROM invoice_items ii
            JOIN invoices i ON ii.invoice_id = i.id
            JOIN products p ON ii.product_id = p.id
            WHERE i.date >= ?
            AND p.product_type != 'category_placeholder'
            GROUP BY p.id
            ORDER BY total_sold DESC
            LIMIT 5
        """
        top_products = db.fetch_all(top_products_query, (current_month_start,))
        
        # أفضل 5 عملاء هذا الشهر
        top_customers_query = """
            SELECT c.name, COUNT(i.id) as invoice_count, SUM(i.total) as total_purchases
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            WHERE i.date >= ?
            GROUP BY c.id
            ORDER BY total_purchases DESC
            LIMIT 5
        """
        top_customers = db.fetch_all(top_customers_query, (current_month_start,))
        
        # تجميع جميع الإحصائيات
        return {
            "date": today,
            "sales": {
                "daily": {
                    "invoices_count": daily_sales.get('invoices_count', 0) if daily_sales else 0,
                    "total_amount": daily_sales.get('total_amount', 0) if daily_sales else 0
                },
                "monthly": {
                    "invoices_count": monthly_sales.get('invoices_count', 0) if monthly_sales else 0,
                    "total_amount": monthly_sales.get('total_amount', 0) if monthly_sales else 0
                },
                "yearly": {
                    "invoices_count": yearly_sales.get('invoices_count', 0) if yearly_sales else 0,
                    "total_amount": yearly_sales.get('total_amount', 0) if yearly_sales else 0
                }
            },
            "inventory": {
                "total_products": inventory_stats.get('total_products', 0) if inventory_stats else 0,
                "inventory_value": inventory_stats.get('inventory_value', 0) if inventory_stats else 0,
                "low_stock_count": inventory_stats.get('low_stock_count', 0) if inventory_stats else 0
            },
            "customers": {
                "total_customers": customers_stats.get('total_customers', 0) if customers_stats else 0
            },
            "top_products": top_products,
            "top_customers": top_customers
        }
    
    @staticmethod
    def get_top_selling_products_report(start_date=None, end_date=None, category=None, limit=None):
        """استخراج تقرير المنتجات الأكثر مبيعًا"""
        # استخدام تنسيق YYYY-MM-DD للمعاملات الداخلية
        if not start_date:
            now = datetime.datetime.now()
            start_date = f"{now.year}-{now.month:02d}-01"
            
        if not end_date:
            now = datetime.datetime.now()
            end_date = now.strftime("%Y-%m-%d")

        # تحويل معاملات YYYY-MM-DD إلى التنسيق المطلوب للمقارنة في قاعدة البيانات (YYYY/MM/DD HH:MM:SS)
        try:
            start_dt_param = start_date.replace('-', '/') + " 00:00:00"
            
            end_date_obj = datetime.datetime.strptime(end_date, '%Y-%m-%d')
            next_day_obj = end_date_obj + datetime.timedelta(days=1)
            end_dt_exclusive_param = next_day_obj.strftime('%Y/%m/%d 00:00:00')
        except ValueError:
             print(f"Error converting date parameters for top selling products report: {start_date}, {end_date}")
             # استخدام قيمة افتراضية في حالة حدوث خطأ
             start_dt_param = datetime.datetime.now().strftime("%Y/%m/01 00:00:00")
             end_dt_exclusive_param = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y/%m/%d 00:00:00')

        # الاستعلام الأساسي لاستخراج المنتجات الأكثر مبيعا مع عدد مرات البيع والمبيعات والأرباح
        base_query = """
            SELECT 
                ii.product_id, 
                p.name as product_name, 
                p.code as product_code, 
                p.category,
                COUNT(DISTINCT i.id) as times_sold,
                SUM(ii.quantity) as total_quantity,
                SUM(ii.total_price) as total_sales,
                SUM(ii.quantity * p.cost) as total_cost,
                SUM(ii.total_price) - SUM(ii.quantity * p.cost) as profit
            FROM invoice_items ii
            JOIN invoices i ON ii.invoice_id = i.id
            JOIN products p ON ii.product_id = p.id
            WHERE i.date >= ? AND i.date < ?
            AND p.product_type != 'category_placeholder'
        """
        
        params = [start_dt_param, end_dt_exclusive_param]
        
        # إضافة فلتر الفئة
        if category and category != "الكل":
            base_query += " AND p.category = ?"
            params.append(category)
        
        # تجميع البيانات حسب المنتج وترتيبها حسب عدد مرات البيع
        base_query += " GROUP BY ii.product_id ORDER BY times_sold DESC"
        
        # تحديد عدد النتائج إذا تم تحديد حد أقصى
        if limit and isinstance(limit, int):
            base_query += f" LIMIT {limit}"
        
        products_data = db.fetch_all(base_query, params)
        
        # استخراج إجماليات الفترة
        summary_query = """
            SELECT 
                COUNT(DISTINCT ii.product_id) as total_products,
                SUM(ii.total_price) as total_sales,
                SUM(ii.quantity * p.cost) as total_cost,
                SUM(ii.quantity) as total_quantity
            FROM invoice_items ii
            JOIN invoices i ON ii.invoice_id = i.id
            JOIN products p ON ii.product_id = p.id
            WHERE i.date >= ? AND i.date < ?
            AND p.product_type != 'category_placeholder'
        """
        
        summary_params = [start_dt_param, end_dt_exclusive_param]
        
        if category and category != "الكل":
            summary_query += " AND p.category = ?"
            summary_params.append(category)
        
        summary = db.fetch_one(summary_query, summary_params)
        
        # حساب إجمالي الربح
        total_profit = 0
        if summary:
            total_sales = summary.get('total_sales', 0) or 0
            total_cost = summary.get('total_cost', 0) or 0
            total_profit = total_sales - total_cost
        
        # تجميع البيانات في قاموس واحد
        return {
            "start_date": start_date, # التنسيق الأصلي YYYY-MM-DD
            "end_date": end_date,     # التنسيق الأصلي YYYY-MM-DD
            "category": category,
            "products_data": products_data,
            "summary": {
                "total_products": summary.get('total_products', 0) or 0 if summary else 0,
                "total_sales": summary.get('total_sales', 0) or 0 if summary else 0,
                "total_cost": summary.get('total_cost', 0) or 0 if summary else 0,
                "total_quantity": summary.get('total_quantity', 0) or 0 if summary else 0,
                "total_profit": total_profit
            }
        } 
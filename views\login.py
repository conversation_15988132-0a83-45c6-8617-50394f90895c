import os
from PyQt5.QtWidgets import (
    QW<PERSON>t, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel, QLineEdit,
    QPushButton, QDialog, QCheckBox, QFrame, QApplication, QComboBox
)
from PyQt5.QtCore import Qt, QSettings, QSize, pyqtSignal
from PyQt5.QtGui import QFont
from controllers.user_controller import UserController
from styles import AppStyles

class LoginWindow(QDialog):
    """نافذة تسجيل الدخول للنظام"""

    # إشارة يتم إرسالها عند نجاح تسجيل الدخول
    loginSuccessful = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)

        # خصائص النافذة
        self.setWindowTitle("مرحباً بك")
        # عدم استخدام أيقونة خارجية للنافذة
        self.setMinimumSize(500, 400)
        self.setMaximumSize(600, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)  # إظهار زر الإغلاق فقط

        # متغير لتخزين بيانات المستخدم بعد تسجيل الدخول
        self.user_data = None

        # الإعدادات
        self.settings = QSettings("MyCompany", "SmartManager")

        # إنشاء واجهة المستخدم
        self.setup_ui()

        # تطبيق الأنماط
        self.apply_styles()

        # تحميل إعدادات تذكر المستخدم
        self.load_remembered_user()

        # تحميل قائمة المستخدمين في الكومبوبوكس
        self.load_users()

        # تعديل سلوك الكومبوبوكس للتأكد من فتح القائمة المنسدلة
        self.username_input.mousePressEvent = self.combo_click_event

    def setup_ui(self):
        """إنشاء عناصر واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 40, 30, 30)
        main_layout.setSpacing(20)

        # استبدال الشعار بعنوان نصي كبير
        title_frame = QFrame()
        title_frame.setObjectName("title_frame")
        title_frame.setMinimumHeight(100)
        title_frame.setMaximumHeight(120)

        title_layout = QVBoxLayout(title_frame)
        title_layout.setAlignment(Qt.AlignCenter)

        app_title = QLabel("Smart Manager")
        app_title.setObjectName("app_title")
        app_title.setAlignment(Qt.AlignCenter)
        app_title.setFont(QFont(AppStyles.FONT_FAMILY, 24, QFont.Bold))

        login_title = QLabel("تسجيل الدخول")
        login_title.setObjectName("login_subtitle")
        login_title.setAlignment(Qt.AlignCenter)
        login_title.setFont(QFont(AppStyles.FONT_FAMILY, 16))

        title_layout.addWidget(app_title)
        title_layout.addWidget(login_title)

        # إضافة العنوان
        main_layout.addWidget(title_frame)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("separator")
        main_layout.addWidget(separator)

        # نموذج تسجيل الدخول
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFormAlignment(Qt.AlignHCenter)
        form_layout.setContentsMargins(20, 10, 20, 10)
        form_layout.setSpacing(15)

        # استبدال حقل اسم المستخدم بكومبوبوكس
        self.username_input = QComboBox()
        self.username_input.setObjectName("login_input")
        self.username_input.setMinimumHeight(40)
        self.username_input.setEditable(True)  # السماح بإدخال نص
        self.username_input.setPlaceholderText("اختر اسم المستخدم")
        self.username_input.setFocusPolicy(Qt.StrongFocus)

        # توسيط النص في الكومبوكس
        self.username_input.lineEdit().setAlignment(Qt.AlignCenter)

        # ضمان فتح القائمة المنسدلة عند النقر على أي جزء من الكومبوبوكس
        self.username_input.setMaxVisibleItems(10)  # عرض عدد أكبر من العناصر

        # معالجة الأحداث لفتح القائمة المنسدلة عند النقر على حقل النص
        line_edit = self.username_input.lineEdit()
        line_edit.originalMousePressEvent = line_edit.mousePressEvent
        line_edit.mousePressEvent = lambda event: self.show_dropdown(event)

        user_label = QLabel("اسم المستخدم:")
        user_label.setObjectName("login_label")
        form_layout.addRow(user_label, self.username_input)

        # حقل كلمة المرور
        self.password_input = QLineEdit()
        self.password_input.setObjectName("login_input")
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setMinimumHeight(40)
        self.password_input.setAlignment(Qt.AlignCenter)
        pass_label = QLabel("كلمة المرور:")
        pass_label.setObjectName("login_label")
        form_layout.addRow(pass_label, self.password_input)

        # خيار تذكر المستخدم
        self.remember_me = QCheckBox("تذكرني")
        self.remember_me.setObjectName("remember_checkbox")
        form_layout.addRow("", self.remember_me)

        # ربط الزر Enter بتسجيل الدخول
        # تعطيل ربط الزر Enter بتسجيل الدخول لمنع تكرار استدعاء وظيفة login
        # self.password_input.returnPressed.connect(self.login)

        # إضافة نموذج تسجيل الدخول
        main_layout.addLayout(form_layout)

        # أزرار تسجيل الدخول والخروج
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        self.login_btn = QPushButton("تسجيل الدخول")
        self.login_btn.setObjectName("login_button")
        self.login_btn.setMinimumHeight(45)
        self.login_btn.clicked.connect(self.login)

        exit_btn = QPushButton("خروج")
        exit_btn.setObjectName("exit_button")
        exit_btn.setMinimumHeight(45)
        exit_btn.clicked.connect(self.reject)

        buttons_layout.addWidget(self.login_btn)
        buttons_layout.addWidget(exit_btn)

        main_layout.addLayout(buttons_layout)



        # نص حقوق النشر
        copyright_label = QLabel("برمجة م. محمد شوقي © 2025 جميع الحقوق محفوظة")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setObjectName("copyright_label")
        main_layout.addWidget(copyright_label)

        # ضبط التركيز المبدئي على اسم المستخدم
        self.username_input.setFocus()

    def show_dropdown(self, event):
        """عرض القائمة المنسدلة للكومبوبوكس عند النقر عليه"""
        # استدعاء الحدث الأصلي للماوس
        line_edit = self.username_input.lineEdit()
        line_edit.originalMousePressEvent(event)

        # فتح القائمة المنسدلة
        if not self.username_input.view().isVisible():
            self.username_input.showPopup()

    def load_users(self):
        """تحميل قائمة المستخدمين في الكومبوبوكس"""
        try:
            users = UserController.get_all_users()
            if users:
                # تفريغ الكومبوبوكس
                self.username_input.clear()

                # إضافة قائمة المستخدمين للكومبوبوكس
                for user in users:
                    if user['is_active']:  # إضافة المستخدمين النشطين فقط
                        self.username_input.addItem(user['username'])
        except Exception as e:
            print(f"خطأ في تحميل قائمة المستخدمين: {str(e)}")

    def load_remembered_user(self):
        """تحميل بيانات المستخدم المحفوظة"""
        remember_me = self.settings.value("login/remember_me", False, type=bool)
        if remember_me:
            username = self.settings.value("login/username", "")

            # البحث عن المستخدم في الكومبوبوكس
            index = self.username_input.findText(username)
            if index >= 0:
                self.username_input.setCurrentIndex(index)
            else:
                self.username_input.setCurrentText(username)

            self.remember_me.setChecked(True)
            # التركيز على كلمة المرور إذا كان اسم المستخدم محفوظًا
            if username:
                self.password_input.setFocus()

    def save_remembered_user(self, username):
        """حفظ بيانات المستخدم إذا تم اختيار تذكرني"""
        if self.remember_me.isChecked():
            self.settings.setValue("login/remember_me", True)
            self.settings.setValue("login/username", username)
        else:
            self.settings.setValue("login/remember_me", False)
            self.settings.setValue("login/username", "")

    def login(self):
        """التحقق من بيانات تسجيل الدخول"""
        # تعطيل زر تسجيل الدخول لمنع النقر المتكرر
        self.login_btn.setEnabled(False)

        # الحصول على بيانات المستخدم
        username = self.username_input.currentText().strip()
        password = self.password_input.text()

        # التحقق من إدخال البيانات
        if not username or not password:
            # استخدام QDialog مباشرة
            error_dialog = QDialog(self)
            error_dialog.setWindowTitle("بيانات غير مكتملة")
            error_dialog.setFixedSize(400, 150)
            error_dialog.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)

            # تخطيط النافذة
            layout = QVBoxLayout(error_dialog)

            # رسالة الخطأ
            error_label = QLabel("يرجى إدخال اسم المستخدم وكلمة المرور")
            error_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(error_label)

            # تخطيط الأزرار
            buttons_layout = QHBoxLayout()
            buttons_layout.addStretch(1)  # إضافة مساحة مرنة لدفع الأزرار إلى اليمين

            # زر الإغلاق
            close_button = QPushButton("إغلاق")
            close_button.setDefault(True)  # جعله الزر الافتراضي
            close_button.setMinimumWidth(80)
            close_button.clicked.connect(error_dialog.accept)
            buttons_layout.addWidget(close_button)

            layout.addLayout(buttons_layout)

            # تطبيق تنسيقات الحوار
            try:
                error_dialog.setStyleSheet(AppStyles.get_dialog_style())
            except Exception as e:
                pass

            # إعادة تفعيل زر تسجيل الدخول
            self.login_btn.setEnabled(True)

            # عرض النافذة
            error_dialog.exec_()
            return

        # محاولة تسجيل الدخول
        success, user, message = UserController.authenticate(username, password)

        # إعادة تفعيل زر تسجيل الدخول
        self.login_btn.setEnabled(True)

        if success and user:
            # حفظ بيانات تذكر المستخدم
            self.save_remembered_user(username)

            # تخزين بيانات المستخدم
            self.user_data = user

            # إرسال إشارة بنجاح تسجيل الدخول وبيانات المستخدم
            self.loginSuccessful.emit(user)

            # إغلاق نافذة تسجيل الدخول
            self.accept()
        else:
            # عرض رسالة الخطأ
            error_dialog = QDialog(self)
            error_dialog.setWindowTitle("خطأ في تسجيل الدخول")
            error_dialog.setFixedSize(400, 150)
            error_dialog.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)

            # تخطيط النافذة
            layout = QVBoxLayout(error_dialog)

            # رسالة الخطأ
            error_label = QLabel(message)
            error_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(error_label)

            # تخطيط الأزرار
            buttons_layout = QHBoxLayout()
            buttons_layout.addStretch(1)  # إضافة مساحة مرنة لدفع الأزرار إلى اليمين

            # زر الإغلاق
            close_button = QPushButton("إغلاق")
            close_button.setDefault(True)  # جعله الزر الافتراضي
            close_button.setMinimumWidth(80)
            close_button.clicked.connect(error_dialog.accept)
            buttons_layout.addWidget(close_button)

            layout.addLayout(buttons_layout)

            # تطبيق تنسيقات الحوار
            try:
                error_dialog.setStyleSheet(AppStyles.get_dialog_style())
            except Exception as e:
                print(f"خطأ في تطبيق الأنماط: {str(e)}")

            # عرض النافذة
            error_dialog.exec_()



    def apply_styles(self):
        """تطبيق الأنماط على عناصر النافذة"""
        # استخدام تنسيقات من AppStyles إذا كانت متاحة
        try:
            # تنسيقات إضافية للعناصر الجديدة
            extra_styles = """
                #title_frame {
                    background-color: #e8f0fe;
                    border-radius: 10px;
                    margin-bottom: 15px;
                    border: 1px solid #d0d9e0;
                }
                #app_title {
                    color: #0f172a;
                    font-weight: bold;
                    font-size: 24px;
                }
                #login_subtitle {
                    color: #334155;
                    font-size: 16px;
                    margin-top: 5px;
                }
                #login_input {
                    text-align: center;
                    direction: rtl;
                    font-size: 13px;
                }
                QComboBox#login_input {
                    padding: 5px 10px;
                    background-color: white;
                    border: 1px solid #cbd5e1;
                    border-radius: 6px;
                }
                QComboBox#login_input::drop-down {
                    subcontrol-origin: padding;
                    subcontrol-position: left top;
                    width: 20px; /* إعادة ظهور زر القائمة المنسدلة */
                    background-color: transparent;
                    border: none;
                }
                QComboBox#login_input::down-arrow {
                    width: 14px;
                    height: 14px;
                    background-color: transparent;
                    color: transparent;
                    image: none; /* إزالة صورة السهم */
                }
                QComboBox#login_input QAbstractItemView {
                    border: 1px solid #cbd5e1;
                    border-radius: 6px;
                    background-color: white;
                    selection-background-color: #3b82f6;
                    selection-color: white;
                }
            """
            self.setStyleSheet(AppStyles.get_login_style() + extra_styles)

            # تطبيق الأنماط على جميع الأزرار في النافذة
            for button in self.findChildren(QPushButton):
                # تطبيق الحد الأدنى للعرض والارتفاع على جميع الأزرار
                if button.objectName() not in ["login_button", "exit_button"]:
                    button.setMinimumWidth(80)
                    button.setMinimumHeight(24)
                else:
                    # تطبيق أحجام خاصة لأزرار تسجيل الدخول والخروج
                    button.setMinimumHeight(45)
                    button.setMinimumWidth(120)

        except Exception as e:
            print(f"خطأ في تطبيق الأنماط: {str(e)}")
            # تنسيقات افتراضية إذا كانت AppStyles غير متاحة
            self.setStyleSheet("""
                QDialog {
                    background-color: #f8fafc;
                }
                #title_frame {
                    background-color: #e8f0fe;
                    border-radius: 10px;
                    margin-bottom: 15px;
                    border: 1px solid #d0d9e0;
                }
                #app_title {
                    color: #0f172a;
                    font-weight: bold;
                    font-size: 24px;
                }
                #login_subtitle {
                    color: #334155;
                    font-size: 16px;
                    margin-top: 5px;
                }
                #login_input {
                    text-align: center;
                    direction: rtl;
                    font-size: 13px;
                    border: 1px solid #cbd5e1;
                    border-radius: 6px;
                    padding: 8px 12px;
                    background-color: white;
                }
                QComboBox#login_input {
                    padding: 5px 10px;
                    background-color: white;
                }
                QComboBox#login_input::drop-down {
                    width: 0px; /* إزالة زر القائمة المنسدلة */
                    border: none;
                }
                QComboBox#login_input::down-arrow {
                    width: 0px; /* إزالة السهم */
                    height: 0px;
                }
                QComboBox#login_input QAbstractItemView {
                    border: 1px solid #cbd5e1;
                    border-radius: 6px;
                    background-color: white;
                }

                /* تنسيق الزر الافتراضي للويندوز */
                QPushButton {
                    background-color: #e1e1e1;
                    color: #000000;
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 5px 12px;
                    text-align: center;
                    min-width: 80px;
                    min-height: 24px;
                }

                QPushButton:hover {
                    background-color: #e5f1fb;
                    border: 1px solid #0078d7;
                }

                QPushButton:pressed {
                    background-color: #cce4f7;
                    border: 1px solid #0078d7;
                }

                QPushButton:focus {
                    border: 1px solid #0078d7;
                    outline: none;
                }

                QPushButton:default {
                    background-color: #0078d7;
                    color: white;
                    border: 1px solid #0078d7;
                }

                QPushButton:default:hover {
                    background-color: #1a86d9;
                    border: 1px solid #1a86d9;
                }

                QPushButton:default:pressed {
                    background-color: #006cc1;
                    border: 1px solid #006cc1;
                }

                QPushButton:disabled {
                    background-color: #f0f0f0;
                    color: #666666;
                    border: 1px solid #d0d0d0;
                }

                #login_button {
                    background-color: #0078d7;
                    color: white;
                    border: 1px solid #0078d7;
                    border-radius: 2px;
                    padding: 8px 16px;
                    font-size: 14px;
                    font-weight: bold;
                    min-height: 45px;
                    min-width: 120px;
                }

                #login_button:hover {
                    background-color: #1a86d9;
                    border: 1px solid #1a86d9;
                }

                #login_button:pressed {
                    background-color: #006cc1;
                    border: 1px solid #006cc1;
                }

                #exit_button {
                    background-color: #e1e1e1;
                    color: #000000;
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 8px 16px;
                    font-size: 14px;
                    min-height: 45px;
                    min-width: 120px;
                }

                #exit_button:hover {
                    background-color: #e5f1fb;
                    border: 1px solid #0078d7;
                }

                #exit_button:pressed {
                    background-color: #cce4f7;
                    border: 1px solid #0078d7;
                }

                #login_label {
                    font-size: 14px;
                    font-weight: bold;
                }
                #copyright_label {
                    color: #64748b;
                    font-size: 12px;
                    margin-top: 20px;
                }
                #separator {
                    color: #cbd5e1;
                    margin: 10px 0;
                }
                #remember_checkbox {
                    font-size: 14px;
                }
            """)

    def combo_click_event(self, event):
        """معالجة النقر على الكومبوبوكس"""
        # استدعاء السلوك الأصلي
        QComboBox.mousePressEvent(self.username_input, event)

        # التأكد من فتح القائمة
        if not self.username_input.view().isVisible():
            self.username_input.showPopup()
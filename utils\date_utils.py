"""
وحدة المساعدة للتعامل مع التواريخ والأوقات - توفر وظائف لتنسيق وعرض التواريخ والأوقات
"""

import datetime
import random

class DateTimeUtils:
    """فئة المساعدة للتعامل مع التواريخ والأوقات"""
    
    @staticmethod
    def get_current_date_time():
        """الحصول على التاريخ والوقت الحالي بالتنسيق المناسب للتخزين في قاعدة البيانات"""
        return datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")
    
    @staticmethod
    def get_current_date():
        """الحصول على التاريخ الحالي فقط"""
        return datetime.datetime.now().strftime("%Y/%m/%d")
    
    @staticmethod
    def get_current_time():
        """الحصول على الوقت الحالي فقط"""
        return datetime.datetime.now().strftime("%H:%M:%S")
    
    @staticmethod
    def format_date_for_display(date_str):
        """تنسيق التاريخ للعرض في واجهة المستخدم"""
        if not date_str:
            return "-"
            
        # إذا كان التاريخ يحتوي على الوقت أيضًا
        if " " in date_str:
            date_part, time_part = date_str.split(" ", 1)
            return f"{date_part} {DateTimeUtils.format_time_for_display(time_part)}"
        
        # إذا كان التاريخ فقط
        return date_str
    
    @staticmethod
    def format_time_for_display(time_str):
        """تنسيق الوقت للعرض في واجهة المستخدم بنظام 12 ساعة"""
        if not time_str:
            return ""
            
        try:
            # تحويل نص الوقت إلى كائن time
            time_obj = datetime.datetime.strptime(time_str, "%H:%M:%S").time()
            
            # تنسيق الوقت بنظام 12 ساعة مع إضافة صباحًا/مساءً
            hour = time_obj.hour
            period = "مساءً" if hour >= 12 else "صباحًا"
            
            # تحويل الساعة إلى نظام 12 ساعة
            if hour > 12:
                hour -= 12
            elif hour == 0:
                hour = 12
                
            # تنسيق الوقت بالشكل المناسب
            return f"{hour:02d}:{time_obj.minute:02d} {period}"
        except:
            return time_str
    
    @staticmethod
    def convert_old_date_to_datetime(date_str):
        """تحويل نص التاريخ القديم (YYYY/MM/DD) إلى تنسيق التاريخ والوقت (YYYY/MM/DD HH:MM:SS)"""
        if not date_str:
            return ""
            
        # إذا كان التاريخ يحتوي على الوقت بالفعل
        if " " in date_str:
            return date_str
            
        # إضافة الوقت الحالي إلى التاريخ
        current_time = DateTimeUtils.get_current_time()
        return f"{date_str} {current_time}"
    
    @staticmethod
    def format_date_for_table(date_str):
        """تنسيق التاريخ والوقت للعرض في الجداول"""
        if not date_str or date_str == "-" or date_str == "None":
            return "-"
            
        print(f"تنسيق التاريخ: {date_str}")
        
        try:
            # إذا كان التاريخ يحتوي على وقت
            if " " in date_str:
                date_part, time_part = date_str.split(" ", 1)
                
                # محاولة التعرف على نمط التاريخ (قد يكون YYYY-MM-DD أو YYYY/MM/DD)
                if "-" in date_part:
                    date_parts = date_part.split("-")
                elif "/" in date_part:
                    date_parts = date_part.split("/")
                else:
                    print(f"تنسيق التاريخ غير معروف: {date_str}")
                    return date_str
                    
                # التحقق من أن لدينا ثلاثة أجزاء للتاريخ
                if len(date_parts) != 3:
                    print(f"تنسيق التاريخ غير صحيح: {date_str}")
                    return date_str
                    
                year, month, day = date_parts
                
                # محاولة تنسيق الوقت
                try:
                    # استخراج الساعة والدقيقة
                    if ":" in time_part:
                        time_components = time_part.split(":")
                        hour = int(time_components[0])
                        minute = time_components[1][:2]  # أخذ أول رقمين فقط للدقائق
                    else:
                        print(f"تنسيق الوقت غير صحيح: {time_part}")
                        return f"{year}/{month}/{day}"
                    
                    am_pm = "م" if hour >= 12 else "ص"
                    
                    # تحويل الساعة إلى تنسيق 12 ساعة
                    if hour > 12:
                        hour -= 12
                    elif hour == 0:
                        hour = 12
                        
                    time_formatted = f"{hour:02d}:{minute} {am_pm}"
                    formatted_date = f"{year}/{month}/{day} {time_formatted}"
                    return formatted_date
                except Exception as e:
                    print(f"خطأ في تنسيق الوقت: {e}")
                    # إذا فشل تنسيق الوقت، إرجاع التاريخ فقط
                    return f"{year}/{month}/{day}"
            else:
                # إذا كان التاريخ لا يحتوي على وقت
                if "-" in date_str:
                    date_parts = date_str.split("-")
                elif "/" in date_str:
                    date_parts = date_str.split("/")
                else:
                    print(f"تنسيق التاريخ غير معروف: {date_str}")
                    return date_str
                
                if len(date_parts) != 3:
                    print(f"تنسيق التاريخ غير صحيح: {date_str}")
                    return date_str
                    
                year, month, day = date_parts
                return f"{year}/{month}/{day}"
        except Exception as e:
            print(f"خطأ في تنسيق التاريخ: {e}")
            return date_str
    
    @staticmethod
    def generate_random_time():
        """إنشاء وقت عشوائي بين 8:00 صباحًا و 8:00 مساءً"""
        hour = random.randint(8, 20)  # ساعات بين 8 صباحًا و 8 مساءً
        minute = random.randint(0, 59)
        return f"{hour:02d}:{minute:02d}"
    
    @staticmethod
    def update_database_dates(db):
        """تحديث جميع التواريخ في قاعدة البيانات بإضافة الساعة إليها إذا لم تكن موجودة"""
        try:
            # التحقق من اتصال قاعدة البيانات
            if not db.conn:
                db.connect()
                
            cursor = db.conn.cursor()
            tables_updated = 0
            records_updated = 0
            
            # 1. تحديث جدول الفواتير
            print("تحديث جدول الفواتير...")
            cursor.execute("SELECT id, date FROM invoices")
            invoices = cursor.fetchall()
            invoices_updated = 0
            
            for invoice_id, date_str in invoices:
                # التحقق مما إذا كان التاريخ لا يحتوي على وقت
                if date_str and " " not in date_str:
                    # إضافة وقت عشوائي
                    random_time = DateTimeUtils.generate_random_time()
                    new_date = f"{date_str} {random_time}"
                    
                    # تحديث السجل
                    cursor.execute("UPDATE invoices SET date = ? WHERE id = ?", (new_date, invoice_id))
                    invoices_updated += 1
            
            if invoices_updated > 0:
                tables_updated += 1
                records_updated += invoices_updated
                print(f"تم تحديث {invoices_updated} فاتورة بإضافة الوقت")
            
            # 2. تحديث جدول المشتريات
            print("تحديث جدول المشتريات...")
            cursor.execute("SELECT id, date FROM purchases")
            purchases = cursor.fetchall()
            purchases_updated = 0
            
            for purchase_id, date_str in purchases:
                # التحقق مما إذا كان التاريخ لا يحتوي على وقت
                if date_str and " " not in date_str:
                    # إضافة وقت عشوائي
                    random_time = DateTimeUtils.generate_random_time()
                    new_date = f"{date_str} {random_time}"
                    
                    # تحديث السجل
                    cursor.execute("UPDATE purchases SET date = ? WHERE id = ?", (new_date, purchase_id))
                    purchases_updated += 1
            
            if purchases_updated > 0:
                tables_updated += 1
                records_updated += purchases_updated
                print(f"تم تحديث {purchases_updated} عملية شراء بإضافة الوقت")
            
            # 3. تحديث جدول العملاء
            print("تحديث جدول العملاء...")
            cursor.execute("SELECT id, last_purchase FROM customers WHERE last_purchase IS NOT NULL AND last_purchase != ''")
            customers = cursor.fetchall()
            customers_updated = 0
            
            for customer_id, date_str in customers:
                # التحقق مما إذا كان التاريخ لا يحتوي على وقت
                if date_str and " " not in date_str:
                    # إضافة وقت عشوائي
                    random_time = DateTimeUtils.generate_random_time()
                    new_date = f"{date_str} {random_time}"
                    
                    # تحديث السجل
                    cursor.execute("UPDATE customers SET last_purchase = ? WHERE id = ?", (new_date, customer_id))
                    customers_updated += 1
            
            if customers_updated > 0:
                tables_updated += 1
                records_updated += customers_updated
                print(f"تم تحديث {customers_updated} عميل بإضافة الوقت لآخر شراء")
            
            # 4. تحديث جدول الموردين
            print("تحديث جدول الموردين...")
            cursor.execute("SELECT id, last_purchase FROM suppliers WHERE last_purchase IS NOT NULL AND last_purchase != ''")
            suppliers = cursor.fetchall()
            suppliers_updated = 0
            
            for supplier_id, date_str in suppliers:
                # التحقق مما إذا كان التاريخ لا يحتوي على وقت
                if date_str and " " not in date_str:
                    # إضافة وقت عشوائي
                    random_time = DateTimeUtils.generate_random_time()
                    new_date = f"{date_str} {random_time}"
                    
                    # تحديث السجل
                    cursor.execute("UPDATE suppliers SET last_purchase = ? WHERE id = ?", (new_date, supplier_id))
                    suppliers_updated += 1
            
            if suppliers_updated > 0:
                tables_updated += 1
                records_updated += suppliers_updated
                print(f"تم تحديث {suppliers_updated} مورد بإضافة الوقت لآخر شراء")
            
            # 5. تحديث جدول المدفوعات
            print("تحديث جدول المدفوعات...")
            cursor.execute("SELECT id, date FROM payments")
            payments = cursor.fetchall()
            payments_updated = 0
            
            for payment_id, date_str in payments:
                # التحقق مما إذا كان التاريخ لا يحتوي على وقت
                if date_str and " " not in date_str:
                    # إضافة وقت عشوائي
                    random_time = DateTimeUtils.generate_random_time()
                    new_date = f"{date_str} {random_time}"
                    
                    # تحديث السجل
                    cursor.execute("UPDATE payments SET date = ? WHERE id = ?", (new_date, payment_id))
                    payments_updated += 1
            
            if payments_updated > 0:
                tables_updated += 1
                records_updated += payments_updated
                print(f"تم تحديث {payments_updated} عملية دفع بإضافة الوقت")
            
            # حفظ التغييرات
            db.conn.commit()
            
            success = True
            message = f"تم تحديث {records_updated} سجل في {tables_updated} جدول بنجاح."
            print(message)
            return success, message
            
        except Exception as e:
            # إرجاع الحالة في حالة الخطأ
            db.conn.rollback()
            error_message = f"حدث خطأ أثناء تحديث التواريخ: {str(e)}"
            print(error_message)
            return False, error_message 
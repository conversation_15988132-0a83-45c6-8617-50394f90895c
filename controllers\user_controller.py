from models.users import UserModel
from utils.custom_widgets import show_warning, show_error, show_information

class UserController:
    """تحكم في عمليات المستخدمين والصلاحيات"""

    @staticmethod
    def initialize_user_system():
        """تهيئة نظام المستخدمين"""
        success, message = UserModel.initialize_admin_user()
        return success, message

    @staticmethod
    def get_all_users():
        """الحصول على قائمة جميع المستخدمين"""
        return UserModel.get_all_users()

    @staticmethod
    def create_user(username, full_name, password, role="كاشير", is_active=True):
        """إنشاء مستخدم جديد"""
        # التحقق من البيانات
        if not username or not full_name or not password:
            return False, "جميع الحقول مطلوبة"

        return UserModel.create_user(username, full_name, password, role, is_active)

    @staticmethod
    def update_user(user_id, username, full_name, role=None, is_active=None):
        """تحديث بيانات مستخدم"""
        # التحقق من صحة المعرف
        if not user_id or not full_name:
            return False, "معرف المستخدم والاسم الكامل مطلوبان"

        return UserModel.update_user(user_id, username, full_name, None, role, is_active)

    @staticmethod
    def delete_user(user_id):
        """حذف مستخدم"""
        if not user_id:
            return False, "معرف المستخدم مطلوب"

        # التحقق من عدم كونه المستخدم الرئيسي
        user = UserModel.get_user_by_id(user_id)
        if user and user['username'] == 'admin':
            return False, "لا يمكن حذف المستخدم الرئيسي"

        return UserModel.delete_user(user_id)

    @staticmethod
    def get_user_info(username=None, user_id=None):
        """الحصول على معلومات مستخدم"""
        if username:
            return UserModel.get_user_by_username(username)
        elif user_id:
            return UserModel.get_user_by_id(user_id)
        return None

    @staticmethod
    def authenticate(username, password):
        """تسجيل الدخول"""
        if not username or not password:
            return False, None, "اسم المستخدم وكلمة المرور مطلوبان"

        # الحصول على بيانات المستخدم مباشرة
        user = UserModel.get_user_by_username(username)

        # التحقق من وجود المستخدم
        if not user:
            return False, None, "اسم المستخدم غير موجود"

        # التحقق من كلمة المرور
        hashed_password = UserModel.hash_password(password)
        if user['password'] != hashed_password:
            return False, None, "كلمة المرور غير صحيحة"

        # التحقق من أن المستخدم نشط
        if not user['is_active']:
            return False, None, "هذا الحساب غير نشط"

        # تحديث تاريخ آخر تسجيل دخول
        import datetime
        from models.database import db
        now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        update_query = """
            UPDATE users
            SET last_login = ?, updated_at = ?
            WHERE id = ?
        """
        db.connect()
        db.execute(update_query, (now, now, user['id']))
        db.commit()

        return True, user, "تم تسجيل الدخول بنجاح"

    @staticmethod
    def change_password(user_id, old_password, new_password):
        """تغيير كلمة المرور"""
        if not old_password or not new_password:
            return False, "كلمة المرور القديمة والجديدة مطلوبة"

        # التحقق من الحد الأدنى لطول كلمة المرور
        if len(new_password) < 4:
            return False, "يجب أن تكون كلمة المرور 4 أحرف على الأقل"

        return UserModel.change_password(user_id, old_password, new_password)

    @staticmethod
    def reset_password(user_id, new_password):
        """إعادة تعيين كلمة المرور للمستخدم"""
        if not user_id or not new_password:
            return False, "معرف المستخدم وكلمة المرور الجديدة مطلوبان"

        # التحقق من الحد الأدنى لطول كلمة المرور
        if len(new_password) < 4:
            return False, "يجب أن تكون كلمة المرور 4 أحرف على الأقل"

        return UserModel.reset_password(user_id, new_password)

    @staticmethod
    def get_role_permissions():
        """الحصول على صلاحيات الأدوار"""
        return UserModel.get_role_permissions()

    @staticmethod
    def save_role_permissions(permissions_data):
        """حفظ صلاحيات الأدوار"""
        if not permissions_data:
            return False, "بيانات الصلاحيات غير صالحة"

        return UserModel.save_role_permissions(permissions_data)

    @staticmethod
    def check_permission(user_id, permission_name, show_message=False, parent_widget=None):
        """التحقق من صلاحية المستخدم

        المعلمات:
            user_id: معرف المستخدم
            permission_name: اسم الصلاحية المطلوبة
            show_message: إظهار رسالة تنبيه إذا لم يكن لدى المستخدم الصلاحية
            parent_widget: النافذة الأب لعرض رسالة التنبيه
        """
        has_permission = UserModel.check_permission(user_id, permission_name)

        # إذا لم يكن لدى المستخدم الصلاحية وتم طلب عرض رسالة
        if not has_permission and show_message and parent_widget:
            show_warning(parent_widget, "غير مسموح",
                        f"ليس لديك صلاحية للقيام بهذه العملية: {permission_name}")

        return has_permission

    @staticmethod
    def check_permissions(user_id, permission_names):
        """التحقق من مجموعة من الصلاحيات للمستخدم (يجب أن تتوفر جميع الصلاحيات)"""
        return UserModel.check_permissions(user_id, permission_names)

    @staticmethod
    def check_any_permission(user_id, permission_names):
        """التحقق من مجموعة من الصلاحيات للمستخدم (يكفي توفر صلاحية واحدة)"""
        return UserModel.check_any_permission(user_id, permission_names)

    @staticmethod
    def get_user_permissions(user_id):
        """الحصول على صلاحيات مستخدم محدد"""
        return UserModel.get_user_permissions(user_id)

    @staticmethod
    def save_user_permissions(user_id, permissions_data):
        """حفظ صلاحيات مستخدم محدد"""
        if not permissions_data:
            return False, "بيانات الصلاحيات غير صالحة"

        return UserModel.save_user_permissions(user_id, permissions_data)

    @staticmethod
    def handle_error(parent_widget, error_title, error_message):
        """عرض رسالة خطأ"""
        show_error(parent_widget, error_title, error_message)

    @staticmethod
    def handle_success(parent_widget, success_title, success_message):
        """عرض رسالة نجاح"""
        show_information(parent_widget, success_title, success_message)
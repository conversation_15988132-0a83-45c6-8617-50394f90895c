#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
وحدة تحتوي على عناصر واجهة مستخدم مخصصة للتطبيق
"""

from PyQt5.QtWidgets import (
    QMessageBox, QPushButton, QVBoxLayout, QHBoxLayout, QLabel,
    QDialog, QWidget, QSpacerItem, QSizePolicy
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QFont, QIcon, QPixmap
from styles import AppStyles

class CustomMessageBox(QDialog):
    """
    صندوق رسائل مخصص بتنسيق موحد لجميع أنواع الرسائل في التطبيق
    """

    # أنواع الرسائل
    Information = QMessageBox.Information
    Warning = QMessageBox.Warning
    Critical = QMessageBox.Critical
    Question = QMessageBox.Question

    # أزرار الرسائل
    Ok = QMessageBox.Ok
    Cancel = QMessageBox.Cancel
    Yes = QMessageBox.Yes
    No = QMessageBox.No
    Abort = QMessageBox.Abort
    Retry = QMessageBox.Retry
    Ignore = QMessageBox.Ignore

    def __init__(self, parent=None, title="", text="", message_type=QMessageBox.Information,
                 buttons=QMessageBox.Ok, default_button=QMessageBox.Ok):
        """
        إنشاء صندوق رسائل مخصص

        المعلمات:
            parent: النافذة الأب
            title: عنوان النافذة
            text: نص الرسالة
            message_type: نوع الرسالة (Information, Warning, Critical, Question)
            buttons: الأزرار المعروضة (Ok, Cancel, Yes, No, ...)
            default_button: الزر الافتراضي
        """
        super().__init__(parent)

        # تعيين خصائص النافذة
        self.setWindowTitle(title)
        self.setMinimumWidth(400)
        self.setMinimumHeight(150)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        self.setModal(True)

        # تعيين التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # إنشاء تخطيط أفقي للأيقونة والنص
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)

        # إضافة الأيقونة المناسبة
        icon_label = QLabel()
        icon_label.setFixedSize(48, 48)

        # تحديد الأيقونة حسب نوع الرسالة
        if message_type == QMessageBox.Information:
            icon = QPixmap(":/icons/information.png")
            if icon.isNull():
                icon_label.setText("ℹ️")
                icon_label.setStyleSheet("font-size: 32px;")
        elif message_type == QMessageBox.Warning:
            icon = QPixmap(":/icons/warning.png")
            if icon.isNull():
                icon_label.setText("⚠️")
                icon_label.setStyleSheet("font-size: 32px;")
        elif message_type == QMessageBox.Critical:
            icon = QPixmap(":/icons/critical.png")
            if icon.isNull():
                icon_label.setText("❌")
                icon_label.setStyleSheet("font-size: 32px;")
        elif message_type == QMessageBox.Question:
            icon = QPixmap(":/icons/question.png")
            if icon.isNull():
                icon_label.setText("❓")
                icon_label.setStyleSheet("font-size: 32px;")

        if not icon.isNull():
            icon_label.setPixmap(icon)

        content_layout.addWidget(icon_label)

        # إضافة نص الرسالة
        text_label = QLabel(text)
        text_label.setWordWrap(True)
        text_label.setTextFormat(Qt.RichText)
        text_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        text_label.setStyleSheet("font-size: 14px;")

        content_layout.addWidget(text_label, 1)
        main_layout.addLayout(content_layout)

        # إضافة فاصل
        separator = QWidget()
        separator.setFixedHeight(1)
        separator.setStyleSheet("background-color: #e0e0e0;")
        main_layout.addWidget(separator)

        # إنشاء تخطيط للأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # إضافة مساحة مرنة لدفع الأزرار إلى اليمين
        buttons_layout.addStretch(1)

        # تخزين الأزرار المضافة
        self.buttons = {}

        # إضافة الأزرار المطلوبة
        if buttons & QMessageBox.Ok:
            ok_button = QPushButton("موافق")
            ok_button.setObjectName("action_button")
            ok_button.setMinimumWidth(100)
            ok_button.clicked.connect(lambda: self.done(QMessageBox.Ok))
            buttons_layout.addWidget(ok_button)
            self.buttons[QMessageBox.Ok] = ok_button

            if default_button == QMessageBox.Ok:
                ok_button.setDefault(True)
                ok_button.setFocus()

        if buttons & QMessageBox.Cancel:
            cancel_button = QPushButton("إلغاء")
            cancel_button.setObjectName("secondary_button")
            cancel_button.setMinimumWidth(100)
            cancel_button.clicked.connect(lambda: self.done(QMessageBox.Cancel))
            buttons_layout.addWidget(cancel_button)
            self.buttons[QMessageBox.Cancel] = cancel_button

            if default_button == QMessageBox.Cancel:
                cancel_button.setDefault(True)
                cancel_button.setFocus()

        if buttons & QMessageBox.Yes:
            yes_button = QPushButton("نعم")
            yes_button.setObjectName("action_button")
            yes_button.setMinimumWidth(100)
            yes_button.clicked.connect(lambda: self.done(QMessageBox.Yes))
            buttons_layout.addWidget(yes_button)
            self.buttons[QMessageBox.Yes] = yes_button

            if default_button == QMessageBox.Yes:
                yes_button.setDefault(True)
                yes_button.setFocus()

        if buttons & QMessageBox.No:
            no_button = QPushButton("لا")
            no_button.setObjectName("secondary_button")
            no_button.setMinimumWidth(100)
            no_button.clicked.connect(lambda: self.done(QMessageBox.No))
            buttons_layout.addWidget(no_button)
            self.buttons[QMessageBox.No] = no_button

            if default_button == QMessageBox.No:
                no_button.setDefault(True)
                no_button.setFocus()

        if buttons & QMessageBox.Retry:
            retry_button = QPushButton("إعادة المحاولة")
            retry_button.setObjectName("action_button")
            retry_button.setMinimumWidth(100)
            retry_button.clicked.connect(lambda: self.done(QMessageBox.Retry))
            buttons_layout.addWidget(retry_button)
            self.buttons[QMessageBox.Retry] = retry_button

            if default_button == QMessageBox.Retry:
                retry_button.setDefault(True)
                retry_button.setFocus()

        if buttons & QMessageBox.Abort:
            abort_button = QPushButton("إيقاف")
            abort_button.setObjectName("danger_button")
            abort_button.setMinimumWidth(100)
            abort_button.clicked.connect(lambda: self.done(QMessageBox.Abort))
            buttons_layout.addWidget(abort_button)
            self.buttons[QMessageBox.Abort] = abort_button

            if default_button == QMessageBox.Abort:
                abort_button.setDefault(True)
                abort_button.setFocus()

        if buttons & QMessageBox.Ignore:
            ignore_button = QPushButton("تجاهل")
            ignore_button.setObjectName("secondary_button")
            ignore_button.setMinimumWidth(100)
            ignore_button.clicked.connect(lambda: self.done(QMessageBox.Ignore))
            buttons_layout.addWidget(ignore_button)
            self.buttons[QMessageBox.Ignore] = ignore_button

            if default_button == QMessageBox.Ignore:
                ignore_button.setDefault(True)
                ignore_button.setFocus()

        main_layout.addLayout(buttons_layout)

        # تطبيق الأنماط
        self.apply_styles()

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        try:
            # استخدام تنسيقات من AppStyles
            self.setStyleSheet(AppStyles.get_dialog_style())
        except Exception as e:
            print(f"خطأ في تطبيق الأنماط: {str(e)}")
            # تنسيقات افتراضية
            self.setStyleSheet("""
                QDialog {
                    background-color: white;
                    border: 1px solid #cccccc;
                    border-radius: 8px;
                }

                QLabel {
                    color: #333333;
                    background-color: transparent;
                }

                /* تنسيق الزر الافتراضي للويندوز */
                QPushButton {
                    background-color: #e1e1e1;
                    color: #000000;
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 5px 12px;
                    text-align: center;
                    min-width: 80px;
                    min-height: 24px;
                }

                QPushButton:hover {
                    background-color: #e5f1fb;
                    border: 1px solid #0078d7;
                }

                QPushButton:pressed {
                    background-color: #cce4f7;
                    border: 1px solid #0078d7;
                }

                QPushButton:focus {
                    border: 1px solid #0078d7;
                    outline: none;
                }

                QPushButton:default {
                    background-color: #0078d7;
                    color: white;
                    border: 1px solid #0078d7;
                }

                QPushButton:default:hover {
                    background-color: #1a86d9;
                    border: 1px solid #1a86d9;
                }

                QPushButton:default:pressed {
                    background-color: #006cc1;
                    border: 1px solid #006cc1;
                }

                QPushButton:disabled {
                    background-color: #f0f0f0;
                    color: #666666;
                    border: 1px solid #d0d0d0;
                }

                QPushButton#action_button {
                    background-color: #0078d7;
                    color: white;
                    border: 1px solid #0078d7;
                    border-radius: 2px;
                    padding: 5px 12px;
                    min-width: 80px;
                }

                QPushButton#action_button:hover {
                    background-color: #1a86d9;
                    border: 1px solid #1a86d9;
                }

                QPushButton#action_button:pressed {
                    background-color: #006cc1;
                    border: 1px solid #006cc1;
                }

                QPushButton#secondary_button {
                    background-color: #e1e1e1;
                    color: #000000;
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 5px 12px;
                    min-width: 80px;
                }

                QPushButton#secondary_button:hover {
                    background-color: #e5f1fb;
                    border: 1px solid #0078d7;
                }

                QPushButton#secondary_button:pressed {
                    background-color: #cce4f7;
                    border: 1px solid #0078d7;
                }

                QPushButton#danger_button {
                    background-color: #e81123;
                    color: white;
                    border: 1px solid #e81123;
                    border-radius: 2px;
                    padding: 5px 12px;
                    min-width: 80px;
                }

                QPushButton#danger_button:hover {
                    background-color: #f1707a;
                    border: 1px solid #f1707a;
                }

                QPushButton#danger_button:pressed {
                    background-color: #d0021b;
                    border: 1px solid #d0021b;
                }
            """)

# دوال مساعدة لعرض رسائل بسهولة
def show_information(parent, title, text, buttons=QMessageBox.Ok, default_button=QMessageBox.Ok):
    """عرض رسالة معلومات"""
    dialog = CustomMessageBox(parent, title, text, CustomMessageBox.Information, buttons, default_button)
    return dialog.exec_()

def show_warning(parent, title, text, buttons=QMessageBox.Ok, default_button=QMessageBox.Ok):
    """عرض رسالة تحذير"""
    dialog = CustomMessageBox(parent, title, text, CustomMessageBox.Warning, buttons, default_button)
    return dialog.exec_()

def show_error(parent, title, text, buttons=QMessageBox.Ok, default_button=QMessageBox.Ok):
    """عرض رسالة خطأ"""
    # استخدام CustomMessageBox مع تطبيق التنسيقات المناسبة
    dialog = CustomMessageBox(parent, title, text, CustomMessageBox.Critical, buttons, default_button)
    return dialog.exec_()

def show_question(parent, title, text, buttons=QMessageBox.Yes | QMessageBox.No, default_button=QMessageBox.No):
    """عرض رسالة سؤال"""
    dialog = CustomMessageBox(parent, title, text, CustomMessageBox.Question, buttons, default_button)
    return dialog.exec_()

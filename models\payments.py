"""
وحدة إدارة المدفوعات - توفر وظائف للتعامل مع سجلات المدفوعات في قاعدة البيانات
"""

from models.database import db
from models.invoices import InvoiceModel
from utils.date_utils import DateTimeUtils
import datetime

class PaymentModel:
    """نموذج التعامل مع بيانات المدفوعات"""
    
    @staticmethod
    def get_customer_payments(customer_id):
        """استرجاع جميع مدفوعات العميل من قاعدة البيانات"""
        try:
            # التحقق من بنية الجدول أولاً
            columns = PaymentModel.check_payments_table_schema()
            print("أعمدة جدول المدفوعات (get_customer_payments):", columns)
            
            # بناء الاستعلام بناءً على الأعمدة المتاحة
            select_fields = ["p.id"]
            
            # إضافة حقول التاريخ
            if 'payment_date' in columns:
                select_fields.append("p.payment_date")
            elif 'date' in columns:
                select_fields.append("p.date as payment_date")
            else:
                select_fields.append("p.created_at as payment_date")
                
            # إضافة حقل المبلغ
            select_fields.append("p.amount")
            
            # إضافة حقل طريقة الدفع
            if 'payment_method' in columns:
                select_fields.append("p.payment_method")
            else:
                select_fields.append("'نقداً' as payment_method")
                
            # إضافة حقل الملاحظات
            if 'notes' in columns:
                select_fields.append("p.notes")
            else:
                select_fields.append("'' as notes")
                
            # إضافة حقول الفاتورة
            select_fields.extend(["i.id as invoice_id", "i.reference_number"])
            
            # بناء الاستعلام النهائي
            query = f"""
                SELECT {', '.join(select_fields)}
                FROM payments p
                LEFT JOIN invoices i ON p.invoice_id = i.id
                WHERE p.customer_id = ?
                ORDER BY p.id DESC
            """
            
            return db.fetch_all(query, (customer_id,))
        except Exception as e:
            db.log_error(f"خطأ في استرجاع مدفوعات العميل: {str(e)}")
            print(f"خطأ في استرجاع مدفوعات العميل: {str(e)}")
            return []
    
    @staticmethod
    def get_invoice_payments(invoice_id):
        """استرجاع جميع مدفوعات فاتورة محددة"""
        try:
            # التحقق من بنية الجدول أولاً
            columns = PaymentModel.check_payments_table_schema()
            
            # بناء الاستعلام بناءً على الأعمدة المتاحة
            select_fields = ["id"]
            
            # إضافة حقول التاريخ
            if 'payment_date' in columns:
                select_fields.append("payment_date")
            elif 'date' in columns:
                select_fields.append("date as payment_date")
            else:
                select_fields.append("created_at as payment_date")
                
            # إضافة حقل المبلغ
            select_fields.append("amount")
            
            # إضافة حقل طريقة الدفع
            if 'payment_method' in columns:
                select_fields.append("payment_method")
            else:
                select_fields.append("'نقداً' as payment_method")
                
            # إضافة حقل الملاحظات
            if 'notes' in columns:
                select_fields.append("notes")
            else:
                select_fields.append("'' as notes")
                
            # بناء الاستعلام النهائي
            query = f"""
                SELECT {', '.join(select_fields)}
                FROM payments
                WHERE invoice_id = ?
                ORDER BY
            """
            
            # إضافة ترتيب حسب التاريخ المتاح
            if 'payment_date' in columns:
                query += " payment_date DESC"
            elif 'date' in columns:
                query += " date DESC"
            else:
                query += " created_at DESC"
            
            # تنفيذ الاستعلام
            return db.fetch_all(query, (invoice_id,))
            
        except Exception as e:
            # طباعة تفاصيل الخطأ للتشخيص
            error_message = f"خطأ في استرجاع مدفوعات الفاتورة: {str(e)}"
            print(error_message)
            db.log_error(error_message)
            return []
    
    @staticmethod
    def check_payments_table_schema():
        """التحقق من هيكل جدول المدفوعات الحالي"""
        try:
            columns_info = db.fetch_all("PRAGMA table_info(payments)")
            columns = [column["name"] for column in columns_info]
            return columns
        except Exception as e:
            db.log_error(f"خطأ في فحص هيكل جدول المدفوعات: {str(e)}")
            return []

    @staticmethod
    def add_payment(payment_data):
        """إضافة دفعة جديدة إلى قاعدة البيانات"""
        try:
            # بدء معاملة قاعدة البيانات
            if not db.conn:
                if not db.connect():
                    db.log_error("فشل الاتصال بقاعدة البيانات عند إضافة دفعة")
                    return None
            
            # التأكد من أن التاريخ يحتوي على الوقت
            if 'payment_date' in payment_data:
                payment_data['payment_date'] = DateTimeUtils.convert_old_date_to_datetime(payment_data['payment_date'])
            else:
                payment_data['payment_date'] = DateTimeUtils.get_current_date_time()
            
            # حالة خاصة: إذا كان نوع الدفع هو 'total_debt' (دفع إجمالي للدين)
            if payment_data.get('payment_mode') == 'total_debt':
                customer_id = payment_data.get('customer_id')
                payment_amount = payment_data.get('amount', 0)
                
                if not customer_id or payment_amount <= 0:
                    db.log_error("معرّف العميل أو مبلغ الدفع غير صالح")
                    return None
                
                # الحصول على قائمة الفواتير غير المدفوعة
                unpaid_invoices = PaymentModel.get_customer_unpaid_invoices(customer_id)
                
                if not unpaid_invoices:
                    db.log_error("لا توجد فواتير غير مدفوعة لهذا العميل")
                    return None
                
                # إدراج بيانات الدفعة الرئيسية
                query = """
                    INSERT INTO payments (
                        customer_id, payment_date,
                        amount, payment_method, notes
                    )
                    VALUES (?, ?, ?, ?, ?)
                """
                params = (
                    customer_id,
                    payment_data.get('payment_date'),
                    payment_amount,
                    payment_data.get('payment_method', 'نقداً'),
                    payment_data.get('notes', '')
                )
                
                success = db.execute(query, params)
                if not success:
                    db.log_error("فشل إضافة الدفعة")
                    return None
                
                payment_id = db.get_last_insert_id()
                
                # توزيع المبلغ المدفوع على الفواتير غير المدفوعة
                remaining_amount = payment_amount
                for invoice in unpaid_invoices:
                    invoice_id = invoice.get('id')
                    invoice_remaining = invoice.get('remaining_amount', 0)
                    
                    if remaining_amount <= 0:
                        break  # توقف إذا تم استنفاد المبلغ المدفوع
                    
                    # تحديد المبلغ الذي سيتم تطبيقه على هذه الفاتورة
                    amount_to_apply = min(remaining_amount, invoice_remaining)
                    
                    if amount_to_apply <= 0:
                        continue  # انتقل للفاتورة التالية إذا لم يكن هناك مبلغ للتطبيق
                    
                    # الحصول على معلومات الفاتورة
                    invoice_full = InvoiceModel.get_invoice_by_id(invoice_id)
                    if not invoice_full:
                        continue  # انتقل للفاتورة التالية إذا لم يتم العثور على الفاتورة
                    
                    # حساب المبلغ المدفوع الجديد والمبلغ المتبقي للفاتورة
                    new_paid_amount = invoice_full.get('paid_amount', 0) + amount_to_apply
                    new_remaining_amount = invoice_full.get('total', 0) - new_paid_amount
                    
                    # تحديث حالة الفاتورة
                    invoice_status = "مدفوعة" if new_remaining_amount <= 0 else "غير مدفوعة"
                    
                    # تحديث الفاتورة
                    update_data = {
                        'customer_id': customer_id,
                        'paid_amount': new_paid_amount,
                        'remaining_amount': new_remaining_amount,
                        'status': invoice_status,
                        'total': invoice_full.get('total', 0)
                    }
                    
                    InvoiceModel.update_invoice(invoice_id, update_data)
                    
                    # إنشاء رابط بين الدفعة والفاتورة
                    link_query = """
                        INSERT INTO payment_invoice_link (payment_id, invoice_id, amount)
                        VALUES (?, ?, ?)
                    """
                    link_params = (payment_id, invoice_id, amount_to_apply)
                    db.execute(link_query, link_params)
                    
                    # تحديث المبلغ المتبقي للتوزيع
                    remaining_amount -= amount_to_apply
                
                # حفظ التغييرات
                db.commit()
                return payment_id
            
            # المسار العادي لإضافة الدفعة لفاتورة محددة
            # إدراج بيانات الدفعة
            query = """
                INSERT INTO payments (
                    invoice_id, customer_id, payment_date,
                    amount, payment_method, notes
                )
                VALUES (?, ?, ?, ?, ?, ?)
            """
            params = (
                payment_data.get('invoice_id'),
                payment_data.get('customer_id'),
                payment_data.get('payment_date'),
                payment_data.get('amount', 0),
                payment_data.get('payment_method', 'نقداً'),
                payment_data.get('notes', '')
            )
            
            success = db.execute(query, params)
            if not success:
                db.log_error("فشل إضافة الدفعة")
                return None
            
            payment_id = db.get_last_insert_id()
            
            # تحديث الفاتورة المتصلة بهذه الدفعة إذا وجدت
            invoice_id = payment_data.get('invoice_id')
            if invoice_id:
                # الحصول على معلومات الفاتورة
                invoice = InvoiceModel.get_invoice_by_id(invoice_id)
                if invoice:
                    # حساب المبلغ المدفوع الجديد والمبلغ المتبقي
                    new_paid_amount = invoice.get('paid_amount', 0) + payment_data.get('amount', 0)
                    new_remaining_amount = invoice.get('total', 0) - new_paid_amount
                    
                    # تحديث حالة الفاتورة بناءً على المبلغ المتبقي
                    invoice_status = "مدفوعة" if new_remaining_amount <= 0 else "غير مدفوعة"
                    
                    # تحديث الفاتورة - مع الحفاظ على معرف العميل
                    update_data = {
                        'customer_id': invoice.get('customer_id'),  # الحفاظ على معرف العميل
                        'paid_amount': new_paid_amount,
                        'remaining_amount': new_remaining_amount,
                        'status': invoice_status,
                        'total': invoice.get('total', 0)  # الحفاظ على إجمالي الفاتورة
                    }
                    InvoiceModel.update_invoice(invoice_id, update_data)
            
            # تحديث الرابط بين الدفعة والفاتورة
            if payment_id and invoice_id:
                link_query = """
                    INSERT INTO payment_invoice_link (payment_id, invoice_id, amount)
                    VALUES (?, ?, ?)
                """
                link_params = (payment_id, invoice_id, payment_data.get('amount', 0))
                db.execute(link_query, link_params)
            
            # حفظ التغييرات
            db.commit()
            return payment_id
            
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            if db.conn:
                db.rollback()
            db.log_error(f"خطأ في إضافة دفعة: {str(e)}")
            print(f"خطأ في إضافة دفعة: {str(e)}")
            return None
    
    @staticmethod
    def get_customer_unpaid_invoices(customer_id):
        """استرجاع قائمة الفواتير غير المدفوعة للعميل"""
        query = """
            SELECT id, reference_number, date, total, paid_amount, remaining_amount
            FROM invoices
            WHERE customer_id = ? AND status = 'غير مدفوعة' AND remaining_amount > 0
            ORDER BY date ASC
        """
        return db.fetch_all(query, (customer_id,))
    
    @staticmethod
    def delete_payment(payment_id):
        """حذف دفعة من قاعدة البيانات"""
        try:
            # التأكد من وجود اتصال بقاعدة البيانات
            if not db.conn:
                db.connect()
            
            # الحصول على تفاصيل الدفعة قبل الحذف
            payment_query = """
                SELECT p.invoice_id, p.amount, p.customer_id,
                       i.paid_amount, i.remaining_amount, i.total, i.status
                FROM payments p
                LEFT JOIN invoices i ON p.invoice_id = i.id
                WHERE p.id = ?
            """
            payment = db.fetch_one(payment_query, (payment_id,))
            
            if not payment:
                return False, "الدفعة غير موجودة"
            
            invoice_id = payment.get('invoice_id')
            customer_id = payment.get('customer_id')
            
            if invoice_id:
                # حالة الدفع المرتبط بفاتورة محددة
                    
                # حذف الدفعة
                delete_query = "DELETE FROM payments WHERE id = ?"
                success = db.execute(delete_query, (payment_id,))
                
                if not success:
                    return False, "فشل في حذف الدفعة"
                    
                # تحديث بيانات الفاتورة
                payment_amount = payment.get('amount', 0)
                current_paid = payment.get('paid_amount', 0)
                total = payment.get('total', 0)
                
                new_paid_amount = current_paid - payment_amount
                new_remaining_amount = total - new_paid_amount
                new_status = "غير مدفوعة" if new_remaining_amount > 0 else "مدفوعة"
                
                # الحصول على معلومات الفاتورة الكاملة
                invoice = InvoiceModel.get_invoice_by_id(invoice_id)
                if not invoice:
                    db.rollback()
                    return False, "الفاتورة غير موجودة"
                
                # تحديث الفاتورة مع الحفاظ على معرف العميل
                update_data = {
                    'customer_id': invoice.get('customer_id'),  # الحفاظ على معرف العميل
                    'paid_amount': new_paid_amount,
                    'remaining_amount': new_remaining_amount,
                    'status': new_status,
                    'total': invoice.get('total', 0)  # الحفاظ على إجمالي الفاتورة
                }
                
                success = InvoiceModel.update_invoice(invoice_id, update_data)
                if not success:
                    db.rollback()
                    return False, "فشل في تحديث بيانات الفاتورة"
            
            elif customer_id:
                # حالة الدفع المرتبط بالعميل مباشرة
                
                # التحقق من وجود روابط للدفعة مع الفواتير
                links_query = """
                    SELECT invoice_id, amount
                    FROM payment_invoice_link
                    WHERE payment_id = ?
                """
                links = db.fetch_all(links_query, (payment_id,))
                
                # إلغاء المدفوعات من الفواتير المرتبطة
                for link in links:
                    linked_invoice_id = link.get('invoice_id')
                    linked_amount = link.get('amount', 0)
                    
                    # الحصول على بيانات الفاتورة
                    invoice = InvoiceModel.get_invoice_by_id(linked_invoice_id)
                    if not invoice:
                        continue
                    
                    # تحديث بيانات الفاتورة
                    current_paid = invoice.get('paid_amount', 0)
                    total = invoice.get('total', 0)
                    
                    new_paid_amount = max(0, current_paid - linked_amount)
                    new_remaining_amount = total - new_paid_amount
                    new_status = "غير مدفوعة" if new_remaining_amount > 0 else "مدفوعة"
                    
                    # تحديث الفاتورة مع الحفاظ على معرف العميل
                    update_data = {
                        'customer_id': invoice.get('customer_id'),  # الحفاظ على معرف العميل
                        'paid_amount': new_paid_amount,
                        'remaining_amount': new_remaining_amount,
                        'status': new_status,
                        'total': invoice.get('total', 0)  # الحفاظ على إجمالي الفاتورة
                    }
                    
                    success = InvoiceModel.update_invoice(linked_invoice_id, update_data)
                    if not success:
                        db.log_error(f"فشل تحديث الفاتورة المرتبطة {linked_invoice_id}")
                
                # حذف روابط الدفعة مع الفواتير
                delete_links_query = """
                    DELETE FROM payment_invoice_link WHERE payment_id = ?
                """
                db.execute(delete_links_query, (payment_id,))
                
                # حذف الدفعة
                delete_query = "DELETE FROM payments WHERE id = ?"
                success = db.execute(delete_query, (payment_id,))
                
                if not success:
                    db.rollback()
                    return False, "فشل في حذف الدفعة"
            
            # حفظ التغييرات
            db.commit()
            return True, "تم حذف الدفعة بنجاح"
            
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            if db.conn:
                db.rollback()
            db.log_error(f"خطأ في حذف دفعة: {str(e)}")
            return False, f"حدث خطأ: {str(e)}" 
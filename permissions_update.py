"""
ملف تحديث الصلاحيات - يستخدم لتحديث الصلاحيات في النظام
"""

# قائمة الصلاحيات المحدثة
updated_permissions = {
    "المبيعات": [
        "عرض المبيعات",
        "إضافة عملية بيع",
        "تعديل عملية بيع",
        "حذف عملية بيع",
        "طباعة فاتورة مبيعات"
    ],
    "المنتجات": [
        "عرض المنتجات",
        "إضافة منتج",
        "تعديل منتج",
        "حذف منتج",
        "إدارة فئات المنتجات",
        "تعديل أسعار المنتجات"
    ],
    "المخزون": [
        "عرض المخزون",
        "إضافة للمخزون",
        "تعديل المخزون",
        "جرد المخزون"
    ],
    "الفواتير": [
        "عرض الفواتير",
        "إلغاء فاتورة",
        "طباعة فاتورة",
        "تعديل فاتورة",
        "تعديل تصميم الفاتورة"
    ],
    "التقارير": [
        "عرض تقارير المبيعات",
        "عرض تقارير المخزون",
        "عرض تقارير الأرباح",
        "عرض تقارير العملاء",
        "عرض تقارير الموردين",
        "تصدير التقارير"
    ],
    "العملاء": [
        "عرض العملاء",
        "إضافة عميل",
        "تعديل عميل",
        "حذف عميل",
        "إدارة ديون العملاء",
        "تسجيل دفعة من عميل"
    ],
    "الموردين": [
        "عرض الموردين",
        "إضافة مورد",
        "تعديل مورد",
        "حذف مورد",
        "إدارة مدفوعات الموردين",
        "تسجيل دفعة لمورد"
    ],
    "المصروفات": [
        "عرض المصروفات",
        "إضافة مصروف",
        "تعديل مصروف",
        "حذف مصروف"
    ],
    "المستخدمين": [
        "عرض المستخدمين",
        "إضافة مستخدم",
        "تعديل مستخدم",
        "حذف مستخدم",
        "إدارة صلاحيات المستخدمين"
    ],
    "الإعدادات": [
        "عرض الإعدادات",
        "تعديل إعدادات النظام",
        "إدارة النسخ الاحتياطي",
        "استعادة النسخ الاحتياطي"
    ],
    "المدفوعات": [
        "عرض سجل المدفوعات"
    ]
}

def update_permissions_in_file(file_path, start_line, end_line):
    """تحديث الصلاحيات في ملف محدد"""
    import os
    
    # التحقق من وجود الملف
    if not os.path.exists(file_path):
        print(f"الملف {file_path} غير موجود")
        return False
    
    # قراءة محتوى الملف
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    # التحقق من أن نطاق الأسطر صحيح
    if start_line < 0 or end_line >= len(lines) or start_line > end_line:
        print(f"نطاق الأسطر غير صحيح: {start_line} - {end_line}")
        return False
    
    # إنشاء نص الصلاحيات الجديد
    new_permissions_text = "            available_permissions = {\n"
    for category, permissions in updated_permissions.items():
        new_permissions_text += f'                "{category}": [\n'
        for permission in permissions:
            new_permissions_text += f'                    "{permission}",\n'
        new_permissions_text += "                ],\n"
    new_permissions_text += "            }\n"
    
    # استبدال الأسطر القديمة بالأسطر الجديدة
    new_lines = lines[:start_line] + [new_permissions_text] + lines[end_line+1:]
    
    # كتابة المحتوى الجديد إلى الملف
    with open(file_path, 'w', encoding='utf-8') as file:
        file.writelines(new_lines)
    
    print(f"تم تحديث الصلاحيات في الملف {file_path}")
    return True

# مثال على استخدام الدالة
# update_permissions_in_file('views/settings.py', 442, 512)

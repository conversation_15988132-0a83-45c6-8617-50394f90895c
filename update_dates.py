#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
برنامج لتحديث التواريخ في قاعدة البيانات بإضافة الوقت إليها
هذا البرنامج يجب تشغيله مرة واحدة فقط لتحديث التواريخ القديمة التي لا تحتوي على معلومات الوقت

يقوم البرنامج بإضافة أوقات عشوائية للتواريخ في الجداول التالية:
- جدول الفواتير (invoices)
- جدول المشتريات (purchases)
- جدول العملاء (customers) - حقل آخر شراء
- جدول الموردين (suppliers) - حقل آخر شراء
- جدول المدفوعات (payments)

استخدام البرنامج:
python update_dates.py [--force] [--backup] [--help]

الخيارات:
--force     تنفيذ التحديث بدون فحص الأمان (للتشغيل المتكرر)
--backup    إنشاء نسخة احتياطية من قاعدة البيانات قبل التحديث
--help      عرض معلومات المساعدة
"""

import sys
import os
import argparse
import datetime
from PyQt5.QtWidgets import QApplication, QMessageBox, QFileDialog

# استيراد وحدات النظام
from models.database import Database
from utils.date_utils import DateTimeUtils

def parse_arguments():
    """تحليل معلمات سطر الأوامر"""
    parser = argparse.ArgumentParser(
        description="برنامج تحديث التواريخ في قاعدة البيانات",
        epilog="هذا البرنامج يجب تشغيله مرة واحدة فقط لتحديث التواريخ القديمة التي لا تحتوي على معلومات الوقت"
    )
    parser.add_argument('--force', action='store_true', help='تنفيذ التحديث بدون فحص الأمان (للتشغيل المتكرر)')
    parser.add_argument('--backup', action='store_true', help='إنشاء نسخة احتياطية من قاعدة البيانات قبل التحديث')
    
    # argparse يقدم --help تلقائياً، لذا لا نحتاج لإضافته يدوياً
    return parser.parse_args()

def show_message(title, message, icon):
    """عرض رسالة للمستخدم"""
    msg_box = QMessageBox()
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setIcon(icon)
    msg_box.exec_()

def check_already_updated(db):
    """التحقق مما إذا كانت البيانات قد تم تحديثها بالفعل"""
    try:
        # التحقق من جدول الفواتير - إذا كانت على الأقل 50% من السجلات تحتوي على وقت، نفترض أن التحديث قد تم بالفعل
        db.connect()
        total_count = db.fetch_one("SELECT COUNT(*) as count FROM invoices")
        if total_count and total_count['count'] > 0:
            with_time_count = db.fetch_one("SELECT COUNT(*) as count FROM invoices WHERE date LIKE '% %'")
            if with_time_count and with_time_count['count'] > 0:
                percentage = (with_time_count['count'] / total_count['count']) * 100
                if percentage > 50:
                    return True
                    
        # تحقق من جدول المدفوعات أيضاً
        total_payments = db.fetch_one("SELECT COUNT(*) as count FROM payments")
        if total_payments and total_payments['count'] > 0:
            payments_with_time = db.fetch_one("SELECT COUNT(*) as count FROM payments WHERE date LIKE '% %'")
            if payments_with_time and payments_with_time['count'] > 0:
                percentage = (payments_with_time['count'] / total_payments['count']) * 100
                if percentage > 50:
                    return True
    except Exception as e:
        print(f"خطأ أثناء التحقق من حالة التحديث: {str(e)}")
    
    return False

def create_backup(db):
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        current_date = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        default_backup_name = f"database_backup_{current_date}.db"
        
        # اختيار مكان حفظ النسخة الاحتياطية
        backup_path, _ = QFileDialog.getSaveFileName(
            None, 
            "حفظ النسخة الاحتياطية", 
            default_backup_name, 
            "SQLite Database (*.db);;All Files (*)"
        )
        
        if not backup_path:
            print("تم إلغاء إنشاء النسخة الاحتياطية بواسطة المستخدم.")
            return False
            
        print(f"جاري إنشاء نسخة احتياطية في: {backup_path}")
        result = db.backup_database(backup_path)
        
        if result:
            print("تم إنشاء النسخة الاحتياطية بنجاح.")
            show_message(
                "نسخة احتياطية", 
                f"تم إنشاء نسخة احتياطية بنجاح في:\n{backup_path}", 
                QMessageBox.Information
            )
            return True
        else:
            print("فشل في إنشاء النسخة الاحتياطية.")
            show_message(
                "خطأ في النسخ الاحتياطي", 
                "فشل في إنشاء النسخة الاحتياطية من قاعدة البيانات.", 
                QMessageBox.Critical
            )
            return False
    except Exception as e:
        error_message = f"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}"
        print(error_message)
        show_message("خطأ في النسخ الاحتياطي", error_message, QMessageBox.Critical)
        return False

def main():
    """الدالة الرئيسية لتحديث التواريخ في قاعدة البيانات"""
    
    # تحليل معلمات سطر الأوامر
    args = parse_arguments()
    
    app = QApplication(sys.argv)
    
    # طباعة رسالة بدء العملية
    print("=== بدء عملية تحديث التواريخ في قاعدة البيانات ===")
    print("سيتم إضافة معلومات الوقت لجميع التواريخ في قاعدة البيانات...")
    print("هذه العملية قد تستغرق بعض الوقت حسب حجم البيانات.")
    print("-" * 50)
    
    # إنشاء اتصال بقاعدة البيانات
    db = Database()
    
    # محاولة الاتصال بقاعدة البيانات
    try:
        # التأكد من وجود اتصال بقاعدة البيانات
        if not db.conn:
            db.connect()
            
        if not db.conn:
            print("خطأ: لا يمكن الاتصال بقاعدة البيانات!")
            show_message("خطأ في الاتصال", "فشل الاتصال بقاعدة البيانات. تأكد من وجود ملف قاعدة البيانات وإمكانية الوصول إليه.", QMessageBox.Critical)
            return
        
        # التحقق مما إذا كانت البيانات قد تم تحديثها بالفعل
        if not args.force and check_already_updated(db):
            print("تم تحديث التواريخ بالفعل!")
            msg = "يبدو أن التواريخ في قاعدة البيانات قد تم تحديثها بالفعل.\n" \
                  "هذا البرنامج مصمم للتشغيل مرة واحدة فقط.\n\n" \
                  "إذا كنت متأكداً من رغبتك في إعادة التحديث، استخدم خيار --force.\n" \
                  "مثال: python update_dates.py --force"
            show_message("تم التحديث بالفعل", msg, QMessageBox.Information)
            return
        
        # إنشاء نسخة احتياطية إذا تم تحديد الخيار
        if args.backup:
            print("إنشاء نسخة احتياطية قبل التحديث...")
            if not create_backup(db):
                # سؤال المستخدم عما إذا كان يريد الاستمرار بدون نسخة احتياطية
                msg_box = QMessageBox()
                msg_box.setWindowTitle("فشل النسخ الاحتياطي")
                msg_box.setText("فشل إنشاء النسخة الاحتياطية. هل ترغب في الاستمرار بدون نسخة احتياطية؟")
                msg_box.setIcon(QMessageBox.Warning)
                msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
                msg_box.setDefaultButton(QMessageBox.No)
                
                if msg_box.exec_() != QMessageBox.Yes:
                    print("تم إلغاء العملية بواسطة المستخدم.")
                    return
            
        # سؤال المستخدم للتأكيد قبل المتابعة
        msg_box = QMessageBox()
        msg_box.setWindowTitle("تأكيد التحديث")
        msg_box.setText("سيتم تحديث جميع التواريخ في قاعدة البيانات بإضافة أوقات عشوائية إليها.\n"
                        "يجب تشغيل هذا البرنامج مرة واحدة فقط.\n\n"
                        "هل تريد المتابعة؟")
        msg_box.setIcon(QMessageBox.Question)
        msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        msg_box.setDefaultButton(QMessageBox.No)
        
        if msg_box.exec_() != QMessageBox.Yes:
            print("تم إلغاء العملية بواسطة المستخدم.")
            return
            
        # تنفيذ عملية تحديث التواريخ
        success, message = DateTimeUtils.update_database_dates(db)
        
        # عرض نتيجة العملية
        if success:
            print("تم تحديث التواريخ بنجاح!")
            print(message)
            show_message("اكتملت العملية", f"تم تحديث التواريخ بنجاح!\n{message}", QMessageBox.Information)
        else:
            print("فشلت عملية التحديث!")
            print(message)
            show_message("خطأ في التحديث", f"فشلت عملية تحديث التواريخ.\n{message}", QMessageBox.Critical)
            
    except Exception as e:
        error_message = f"حدث خطأ غير متوقع: {str(e)}"
        print(error_message)
        show_message("خطأ غير متوقع", error_message, QMessageBox.Critical)
    finally:
        # إغلاق الاتصال بقاعدة البيانات
        if hasattr(db, 'conn') and db.conn:
            db.disconnect()
            print("تم إغلاق الاتصال بقاعدة البيانات.")
    
    print("=== انتهت عملية تحديث التواريخ ===")
    
    # إغلاق التطبيق بعد الانتهاء
    sys.exit(0)

if __name__ == "__main__":
    main() 
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QFrame,
                             QTableWidget, QTableWidgetItem, QHeaderView, QComboBox, QLineEdit,
                             QGridLayout, QSpacerItem, QSizePolicy, QAbstractItemView, QDialog,
                             QFormLayout, QMessageBox, QDoubleSpinBox, QSpinBox, QMenu, QAction,
                             QTabWidget, QDateEdit, QDialogButtonBox, QScrollArea, QCheckBox,
                             QInputDialog, QRadioButton, QButtonGroup)
from PyQt5.QtCore import Qt, QSize, QDate, QTimer
from PyQt5.QtGui import QFont, QIcon, QColor

# Define RTL helper classes directly
class RTLComboBox(QComboBox):
    """Replacement class for RTLComboBox"""
    pass

def apply_rtl_to_all_widgets(widget):
    """Replacement function for apply_rtl_to_all_widgets"""
    pass

from styles import AppStyles  # استيراد التنسيقات من ملف styles.py
from models.suppliers import SupplierModel  # استيراد نموذج الموردين
from models.database import db  # استيراد قاعدة البيانات
from utils.date_utils import DateTimeUtils
import datetime
import random
import time

class SuppliersView(QWidget):
    def __init__(self):
        super().__init__()

        # إعداد التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)

        # إضافة عنوان الصفحة
        header_layout = QHBoxLayout()
        page_title = QLabel("الموردين")
        page_title.setObjectName("page_title")
        page_title.setFont(QFont("Arial", 20, QFont.Bold))
        header_layout.addWidget(page_title)

        # إضافة زر لإضافة مورد جديد
        self.new_supplier_btn = QPushButton("🏭  مورد جديد")
        self.new_supplier_btn.setFixedSize(180, 40)
        self.new_supplier_btn.setObjectName("action_button")
        self.new_supplier_btn.setFont(QFont("Arial", 11))
        self.new_supplier_btn.setCursor(Qt.PointingHandCursor)
        self.new_supplier_btn.clicked.connect(self.add_new_supplier)

        # إضافة زر لعرض سجل المدفوعات
        self.payments_history_btn = QPushButton("💰  سجل المدفوعات")
        self.payments_history_btn.setFixedSize(180, 40)
        self.payments_history_btn.setObjectName("action_button")
        self.payments_history_btn.setStyleSheet("""
            QPushButton#action_button {
                background-color: #3b82f6;
                color: white;
            }
            QPushButton#action_button:hover {
                background-color: #2563eb;
            }
        """)
        self.payments_history_btn.setFont(QFont("Arial", 11))
        self.payments_history_btn.setCursor(Qt.PointingHandCursor)
        self.payments_history_btn.clicked.connect(self.show_payments_history)

        header_layout.addStretch()
        header_layout.addWidget(self.payments_history_btn)
        header_layout.addWidget(self.new_supplier_btn)

        layout.addLayout(header_layout)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("content_separator")
        layout.addWidget(separator)
        layout.addSpacing(20)

        # إضافة منطقة البحث والتصفية
        search_layout = QHBoxLayout()

        # مربع البحث
        search_label = QLabel("بحث:")
        search_label.setObjectName("field_label")
        self.search_input = QLineEdit()
        self.search_input.setObjectName("search_input")
        self.search_input.setPlaceholderText("ابحث عن مورد...")
        self.search_input.setClearButtonEnabled(True)
        self.search_input.setMinimumHeight(40)
        self.search_input.textChanged.connect(self.filter_suppliers)

        # فلتر تاريخ آخر شراء
        date_label = QLabel("تاريخ آخر شراء:")
        date_label.setObjectName("field_label")

        self.date_filter = RTLComboBox()
        self.date_filter.setObjectName("combo_box")
        self.date_filter.addItems(["الكل", "آخر أسبوع", "آخر شهر", "آخر 3 أشهر", "آخر سنة"])
        self.date_filter.setFixedHeight(40)
        self.date_filter.setMinimumWidth(120)
        self.date_filter.setLayoutDirection(Qt.RightToLeft)
        self.date_filter.currentIndexChanged.connect(self.filter_suppliers)

        # إضافة عناصر التصفية إلى التخطيط
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addSpacing(20)
        search_layout.addWidget(date_label)
        search_layout.addWidget(self.date_filter)

        layout.addLayout(search_layout)
        layout.addSpacing(15)

        # إضافة جدول الموردين
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setObjectName("suppliers_table")
        self.suppliers_table.setColumnCount(7)
        self.suppliers_table.setHorizontalHeaderLabels(["#", "اسم المورد", "رقم الهاتف", "البريد الإلكتروني", "إجمالي المشتريات", "آخر عملية شراء", "عدد عمليات الشراء"])
        self.suppliers_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.suppliers_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.suppliers_table.verticalHeader().setVisible(False)
        self.suppliers_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.suppliers_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.suppliers_table.setAlternatingRowColors(False)

        # إضافة قائمة السياق
        self.suppliers_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.suppliers_table.customContextMenuRequested.connect(self.show_context_menu)

        # إضافة النقر المزدوج لعرض تفاصيل المورد
        self.suppliers_table.doubleClicked.connect(self.on_table_double_clicked)

        layout.addWidget(self.suppliers_table)

        # إضافة إحصائيات الموردين
        stats_layout = QHBoxLayout()

        # عدد الموردين
        self.total_suppliers_label = QLabel("إجمالي الموردين: 0")
        self.total_suppliers_label.setObjectName("stats_label")
        stats_layout.addWidget(self.total_suppliers_label)

        stats_layout.addStretch()

        # إجمالي المشتريات
        self.total_purchases_label = QLabel("إجمالي المشتريات: 0.00 ج.م")
        self.total_purchases_label.setObjectName("stats_label")
        stats_layout.addWidget(self.total_purchases_label)

        layout.addLayout(stats_layout)

        # إضافة تعليمات للمستخدم
        hint_label = QLabel("ملاحظة: يمكنك النقر مرتين على أي مورد لعرض تفاصيله وعمليات الشراء، أو النقر بزر الماوس الأيمن للوصول إلى قائمة الخيارات")
        hint_label.setObjectName("hint_label")
        layout.addWidget(hint_label)

        # ملء جدول الموردين بالبيانات من قاعدة البيانات
        self.populate_suppliers_table()

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def show_context_menu(self, position):
        """عرض قائمة السياق عند النقر بزر الماوس الأيمن"""
        # التحقق من وجود صف محدد
        selected_indexes = self.suppliers_table.selectedIndexes()
        if not selected_indexes:
            return

        # الحصول على صف المورد المحدد
        row = selected_indexes[0].row()
        supplier_id = self.suppliers_table.item(row, 0).data(Qt.UserRole)
        supplier_name = self.suppliers_table.item(row, 1).text()

        # التحقق من صلاحية المستخدم
        main_window = None
        parent = self.parent()
        user_id = None

        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')

        # استيراد وحدة التحكم بالمستخدمين
        from controllers.user_controller import UserController

        # إنشاء قائمة السياق
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.RightToLeft)

        # إضافة عنوان المورد
        title_action = QAction(f"المورد: {supplier_name}", self)
        title_action.setEnabled(False)
        title_font = title_action.font()
        title_font.setBold(True)
        title_action.setFont(title_font)
        context_menu.addAction(title_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # إضافة إجراءات - عرض تفاصيل المورد يتطلب صلاحية
        if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "عرض تفاصيل المورد"):
            view_action = QAction("👁️  عرض تفاصيل المورد", self)
            view_action.triggered.connect(lambda: self.view_supplier_details(supplier_id))
            context_menu.addAction(view_action)

        # تعديل بيانات المورد - يتطلب صلاحية
        if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "تعديل مورد"):
            edit_action = QAction("✏️  تعديل بيانات المورد", self)
            edit_action.triggered.connect(lambda: self.edit_supplier(supplier_id))
            context_menu.addAction(edit_action)

        # إضافة عملية شراء - يتطلب صلاحية
        if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "إضافة عملية شراء"):
            add_purchase_action = QAction("📦  إضافة عملية شراء", self)
            add_purchase_action.triggered.connect(lambda: self.add_purchase_for_supplier(supplier_id))
            context_menu.addAction(add_purchase_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # حذف المورد - يتطلب صلاحية
        if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "حذف مورد"):
            delete_action = QAction("🗑️  حذف المورد", self)
            delete_action.triggered.connect(lambda: self.delete_supplier(supplier_id))
            context_menu.addAction(delete_action)

        # عرض القائمة في موقع النقر
        context_menu.exec_(self.suppliers_table.mapToGlobal(position))

    def on_table_double_clicked(self, index):
        """معالجة حدث النقر المزدوج على صف في الجدول"""
        # التحقق من صلاحية المستخدم
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')
            # التحقق من صلاحية عرض تفاصيل المورد
            from controllers.user_controller import UserController
            if not UserController.check_permission(user_id, "عرض تفاصيل المورد", show_message=True, parent_widget=self):
                return

        row = index.row()
        supplier_id = self.suppliers_table.item(row, 0).data(Qt.UserRole)
        self.view_supplier_details(supplier_id)

    def populate_suppliers_table(self):
        """ملء جدول الموردين بالبيانات من قاعدة البيانات"""
        # تنظيف الجدول أولاً
        self.suppliers_table.clearContents()
        self.suppliers_table.setRowCount(0)

        # متغيرات للإحصائيات
        total_suppliers = 0
        total_purchase_amount = 0

        # استرجاع بيانات الموردين من قاعدة البيانات
        suppliers_data = SupplierModel.get_all_suppliers()

        # طباعة للتصحيح - عرض عدد الموردين المسترجعة
        print(f"تم استرجاع {len(suppliers_data) if suppliers_data else 0} مورد من قاعدة البيانات")

        # طباعة أول مورد (إن وجد) للتصحيح
        if suppliers_data and len(suppliers_data) > 0:
            print("أول مورد:", suppliers_data[0])

        # إضافة بيانات الموردين للجدول
        for supplier in suppliers_data:
            row = self.suppliers_table.rowCount()
            self.suppliers_table.insertRow(row)

            # رقم المسلسل
            serial_item = QTableWidgetItem(str(row + 1))
            serial_item.setTextAlignment(Qt.AlignCenter)
            # تخزين معرف المورد في البيانات المخفية
            serial_item.setData(Qt.UserRole, supplier.get("id", ""))
            self.suppliers_table.setItem(row, 0, serial_item)

            # اسم المورد
            name_item = QTableWidgetItem(supplier.get("name", ""))
            name_item.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 1, name_item)

            # رقم الهاتف
            phone_item = QTableWidgetItem(supplier.get("phone", "") if supplier.get("phone") else "")
            phone_item.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 2, phone_item)

            # البريد الإلكتروني
            email_item = QTableWidgetItem(supplier.get("email", "") if supplier.get("email") else "")
            email_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.suppliers_table.setItem(row, 3, email_item)

            # إجمالي المشتريات
            total_purchases = QTableWidgetItem(f"{supplier.get('total_purchases', 0):.2f} ج.م")
            total_purchases.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 4, total_purchases)

            # آخر عملية شراء
            last_purchase = supplier.get("last_purchase_date", None)
            # طباعة التاريخ للتصحيح
            print(f"تاريخ آخر عملية شراء للمورد {supplier.get('name', '')}: {last_purchase}")
            last_purchase_item = QTableWidgetItem(DateTimeUtils.format_date_for_table(last_purchase))
            last_purchase_item.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 5, last_purchase_item)

            # عدد عمليات الشراء
            purchase_count = QTableWidgetItem(str(supplier.get("purchase_count", 0)))
            purchase_count.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 6, purchase_count)

            # تحديث الإحصائيات
            total_suppliers += 1
            total_purchase_amount += supplier.get("total_purchases", 0)

        # تحديث الإحصائيات في الواجهة
        self.update_supplier_stats(total_suppliers, total_purchase_amount)

    def filter_suppliers(self):
        """تصفية الموردين في الجدول بناءً على النص المدخل والتاريخ"""
        search_text = self.search_input.text().strip()
        date_filter = self.date_filter.currentText()

        # الحصول على الموردين المصفى
        filtered_suppliers = SupplierModel.search_suppliers(search_text, date_filter)

        # تحديث الجدول بالنتائج
        self.suppliers_table.clearContents()
        self.suppliers_table.setRowCount(0)

        total_suppliers = 0
        total_purchase_amount = 0

        for supplier in filtered_suppliers:
            row = self.suppliers_table.rowCount()
            self.suppliers_table.insertRow(row)

            # رقم المسلسل
            serial_item = QTableWidgetItem(str(row + 1))
            serial_item.setTextAlignment(Qt.AlignCenter)
            # تخزين معرف المورد في البيانات المخفية
            serial_item.setData(Qt.UserRole, supplier.get("id", ""))
            self.suppliers_table.setItem(row, 0, serial_item)

            # اسم المورد
            name_item = QTableWidgetItem(supplier.get("name", ""))
            name_item.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 1, name_item)

            # رقم الهاتف
            phone_item = QTableWidgetItem(supplier.get("phone", "") if supplier.get("phone") else "")
            phone_item.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 2, phone_item)

            # البريد الإلكتروني
            email_item = QTableWidgetItem(supplier.get("email", "") if supplier.get("email") else "")
            email_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.suppliers_table.setItem(row, 3, email_item)

            # إجمالي المشتريات
            total_purchases = QTableWidgetItem(f"{supplier.get('total_purchases', 0):.2f} ج.م")
            total_purchases.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 4, total_purchases)

            # آخر عملية شراء
            last_purchase = supplier.get("last_purchase_date", None)
            # طباعة التاريخ للتصحيح
            print(f"تاريخ آخر عملية شراء للمورد {supplier.get('name', '')}: {last_purchase}")
            last_purchase_item = QTableWidgetItem(DateTimeUtils.format_date_for_table(last_purchase))
            last_purchase_item.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 5, last_purchase_item)

            # عدد عمليات الشراء
            purchase_count = QTableWidgetItem(str(supplier.get("purchase_count", 0)))
            purchase_count.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 6, purchase_count)

            # تحديث الإحصائيات
            total_suppliers += 1
            total_purchase_amount += supplier.get("total_purchases", 0)

        # تحديث إحصائيات الموردين
        self.update_supplier_stats(total_suppliers, total_purchase_amount)

    def update_supplier_stats(self, supplier_count, total_purchase_amount):
        """تحديث إحصائيات الموردين في الواجهة"""
        self.total_suppliers_label.setText(f"إجمالي الموردين: {supplier_count}")
        self.total_purchases_label.setText(f"إجمالي المشتريات: {total_purchase_amount:.2f} ج.م")

    def get_supplier_by_id(self, supplier_id):
        """الحصول على بيانات المورد من قاعدة البيانات باستخدام المعرف"""
        return SupplierModel.get_supplier_by_id(supplier_id)

    def get_supplier_purchases(self, supplier_id):
        """الحصول على مشتريات المورد من قاعدة البيانات"""
        print(f"[DEBUG] Obteniendo compras para el proveedor ID: {supplier_id}")

        # Verificar que el supplier_id es un entero válido
        try:
            supplier_id = int(supplier_id)
            print(f"[DEBUG] ID de proveedor validado como entero: {supplier_id}")
        except (ValueError, TypeError):
            print(f"[DEBUG] Error al convertir supplier_id a entero: {supplier_id}")
            return []

        # Importar los modelos necesarios
        from models.suppliers import SupplierModel
        from models.purchases import PurchaseModel

        # Método 1: Usar directamente la función del modelo de proveedores
        try:
            purchases = SupplierModel.get_supplier_purchases(supplier_id)
            print(f"[DEBUG] Compras encontradas con SupplierModel.get_supplier_purchases: {len(purchases) if purchases else 0}")

            if purchases and len(purchases) > 0:
                print(f"[DEBUG] Primera compra encontrada: {purchases[0]}")
                return purchases
        except Exception as e:
            import traceback
            print(f"[DEBUG] Error usando SupplierModel.get_supplier_purchases: {str(e)}")
            print(traceback.format_exc())

        # Método 2: Usar search_purchases con supplier_id como filtro
        try:
            purchases = PurchaseModel.search_purchases(supplier_id=supplier_id)
            print(f"[DEBUG] Compras encontradas con PurchaseModel.search_purchases: {len(purchases) if purchases else 0}")

            if purchases and len(purchases) > 0:
                print(f"[DEBUG] Primera compra: {purchases[0]}")
                return purchases
        except Exception as e:
            import traceback
            print(f"[DEBUG] Error en search_purchases: {str(e)}")
            print(traceback.format_exc())

        # Método 3: obtener todas las compras y filtrar manualmente
        try:
            # Verificar si hay compras en el sistema
            all_purchases = PurchaseModel.get_all_purchases()

            if all_purchases:
                # Filtrar compras por supplier_id
                filtered_purchases = [p for p in all_purchases if str(p.get('supplier_id')) == str(supplier_id)]

                if filtered_purchases:
                    return filtered_purchases

        except Exception as e:
            pass

        # Si no se pudo obtener nada, devolver lista vacía
        return []

    def get_purchase_items(self, purchase_id):
        """الحصول على عناصر عملية الشراء"""
        # Importar el modelo de compras para obtener los items
        from models.purchases import PurchaseModel
        # Obtener los elementos de la compra desde la base de datos
        return PurchaseModel.get_purchase_items(purchase_id) if hasattr(PurchaseModel, 'get_purchase_items') else []

    def view_supplier_details(self, supplier_id):
        """عرض تفاصيل المورد بالمعرف المحدد"""
        # التحقق من صلاحية المستخدم
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')
            # التحقق من صلاحية عرض تفاصيل المورد
            from controllers.user_controller import UserController
            if not UserController.check_permission(user_id, "عرض تفاصيل المورد", show_message=True, parent_widget=self):
                return

        # الحصول على بيانات المورد من قاعدة البيانات
        supplier = self.get_supplier_by_id(supplier_id)

        if not supplier:
            QMessageBox.warning(self, "تحذير", "لم يتم العثور على المورد!")
            return

        # الحصول على عمليات الشراء
        purchases = self.get_supplier_purchases(supplier_id)

        # عرض نافذة تفاصيل المورد
        dialog = SupplierDetailsDialog(self, supplier, purchases)
        dialog.exec_()

    def add_new_supplier(self):
        """فتح نافذة إضافة مورد جديد"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية إضافة مورد
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "إضافة مورد", show_message=True, parent_widget=self):
                    return

            dialog = SupplierDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                # الحصول على بيانات المورد الجديد
                supplier_data = dialog.get_supplier_data()

                # إضافة المورد إلى قاعدة البيانات
                supplier_id = SupplierModel.add_supplier(supplier_data)

                if supplier_id:
                    QMessageBox.information(self, "تم بنجاح", "تم إضافة المورد الجديد بنجاح.")
                    # تحديث جدول الموردين
                    self.populate_suppliers_table()
                else:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء إضافة المورد الجديد!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء محاولة إضافة مورد جديد: {str(e)}")

    def edit_supplier(self, supplier_id):
        """فتح نافذة تعديل بيانات المورد المحدد"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية تعديل مورد
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "تعديل مورد", show_message=True, parent_widget=self):
                    return

            # الحصول على بيانات المورد من قاعدة البيانات
            supplier = self.get_supplier_by_id(supplier_id)

            if not supplier:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على المورد!")
                return

            # فتح نافذة تعديل المورد
            dialog = SupplierDialog(self, supplier)
            if dialog.exec_() == QDialog.Accepted:
                # الحصول على البيانات المعدلة
                supplier_data = dialog.get_supplier_data()

                # تحديث بيانات المورد في قاعدة البيانات
                success = SupplierModel.update_supplier(supplier_id, supplier_data)

                if success:
                    QMessageBox.information(self, "تم بنجاح", "تم تحديث بيانات المورد بنجاح.")
                    # تحديث جدول الموردين
                    self.populate_suppliers_table()
                else:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء تحديث بيانات المورد!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء محاولة تعديل بيانات المورد: {str(e)}")

    def delete_supplier(self, supplier_id):
        """حذف المورد المحدد من قاعدة البيانات"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية حذف مورد
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "حذف مورد", show_message=True, parent_widget=self):
                    return

            # التأكد من رغبة المستخدم في حذف المورد
            confirmation = QMessageBox.question(
                self,
                "تأكيد الحذف",
                "هل أنت متأكد من رغبتك في حذف هذا المورد؟ سيتم إزالة جميع بياناته من النظام.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if confirmation == QMessageBox.Yes:
                # حذف المورد من قاعدة البيانات
                success = SupplierModel.delete_supplier(supplier_id)

                if success:
                    QMessageBox.information(self, "تم بنجاح", "تم حذف المورد بنجاح.")
                    # تحديث جدول الموردين
                    self.populate_suppliers_table()
                else:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء محاولة حذف المورد!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء محاولة حذف المورد: {str(e)}")

    def add_new_purchase(self):
        """إنشاء عملية شراء جديدة"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية إضافة عملية شراء
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "إضافة عملية شراء", show_message=True, parent_widget=self):
                    return

            # Obtener la lista de proveedores directamente de la base de datos
            suppliers = SupplierModel.get_all_suppliers()

            if not suppliers:
                QMessageBox.warning(self, "تنبيه", "لا يوجد موردين مسجلين. يرجى إضافة مورد أولاً.")
                return

            supplier_dialog = SupplierSelectionDialog(self, suppliers)
            if supplier_dialog.exec_() == QDialog.Accepted:
                selected_supplier = supplier_dialog.get_selected_supplier()
                if selected_supplier:
                    self.add_purchase_for_supplier(selected_supplier["id"])
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء محاولة إضافة عملية شراء جديدة: {str(e)}")

    def add_purchase_for_supplier(self, supplier_id):
        """إضافة عملية شراء لمورد محدد - تم تحسين آلية إنشاء الفواتير"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية إضافة عملية شراء
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "إضافة عملية شراء", show_message=True, parent_widget=self):
                    return False

            print(f"[DEBUG] Iniciando proceso de añadir compra para proveedor ID: {supplier_id}")
            supplier = self.get_supplier_by_id(supplier_id)
            if not supplier:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على المورد!")
                return False
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")
            return False

        # Crear un diálogo para la nueva compra
        purchase_dialog = PurchaseDialog(self, supplier)
        if purchase_dialog.exec_() == QDialog.Accepted:
            # Obtener datos de la compra
            purchase_data = purchase_dialog.get_purchase_data()
            purchase_items = purchase_dialog.get_purchase_items()

            # Validar que hay ítems en la compra
            if not purchase_items:
                QMessageBox.warning(self, "تنبيه", "لا يمكن إنشاء فاتورة شراء بدون عناصر.")
                return False

            # إنشاء رقم مرجعي فريد للفاتورة الجديدة
            now = datetime.datetime.now()
            unique_reference = f"PO-{now.year}-{now.month:02d}-{now.day:02d}-{now.hour:02d}{now.minute:02d}{now.second:02d}"
            purchase_data["reference_number"] = unique_reference

            # Añadir ID del proveedor - تحويل المعرف إلى رقم صحيح إذا كان نصًا
            try:
                if isinstance(supplier_id, str) and supplier_id.isdigit():
                    supplier_id = int(supplier_id)
                elif not isinstance(supplier_id, int):
                    raise ValueError(f"معرف المورد بتنسيق غير صالح: {supplier_id} من النوع {type(supplier_id).__name__}")

                purchase_data["supplier_id"] = supplier_id
                print(f"[DEBUG] تم تعيين معرف المورد: {supplier_id} (النوع: {type(supplier_id).__name__})")
            except Exception as e:
                print(f"[DEBUG] خطأ أثناء تحويل معرف المورد: {str(e)}")
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء معالجة معرف المورد: {str(e)}")
                return False

            # تسجيل بيانات عملية الشراء للتشخيص
            print(f"[DEBUG] Datos de la compra a guardar: {purchase_data}")
            print(f"[DEBUG] Elementos de la compra a guardar: {purchase_items}")

            # فحص تفاصيل العناصر
            for i, item in enumerate(purchase_items):
                print(f"[DEBUG] Revisando item {i+1}:")
                for key, value in item.items():
                    print(f"   - {key}: {value} (tipo: {type(value).__name__})")

            # Validar los datos necesarios para la compra
            if 'total' not in purchase_data or purchase_data['total'] <= 0:
                print(f"[DEBUG] Error: Total de compra inválido: {purchase_data.get('total')}")
                QMessageBox.warning(self, "تنبيه", "المبلغ الإجمالي للفاتورة غير صالح. يرجى التحقق من العناصر.")
                return False

            # Preparar datos para la base de datos
            print("[DEBUG] Preparando datos de items para la base de datos...")

            # Verificar el método de pago
            payment_method = purchase_data.get('payment_method', 'نقداً')
            paid_amount = purchase_data.get('paid_amount', 0)
            total_amount = purchase_data.get('total', 0)

            # Asegurar que los montos sean coherentes
            if payment_method == 'نقداً' and paid_amount < total_amount:
                print(f"[DEBUG] Advertencia: Pago en efectivo pero monto pagado ({paid_amount}) menor que total ({total_amount})")

            if payment_method == 'آجل' and paid_amount > 0:
                print(f"[DEBUG] Nota: Pago aplazado con anticipo de {paid_amount}")

            # Intentar añadir la compra a la base de datos
            print("[DEBUG] Intentando añadir la compra a la base de datos...")
            from models.purchases import PurchaseModel

            try:
                purchase_id = PurchaseModel.add_purchase(purchase_data, purchase_items)
                print(f"[DEBUG] Resultado de add_purchase: {purchase_id}")

                if not purchase_id:
                    print("[DEBUG] ERROR: No se pudo obtener el ID de la compra creada. Verifique la función add_purchase.")
                    QMessageBox.critical(self, "خطأ", "فشلت عملية إضافة الشراء: لم يتم الحصول على معرف الفاتورة.")
                    return False

                # Verificar que la compra se guardó correctamente
                purchase_verificado = PurchaseModel.get_purchase_by_id(purchase_id)

                if not purchase_verificado:
                    print(f"[DEBUG] ERROR: La compra con ID {purchase_id} no se puede recuperar de la base de datos.")
                    QMessageBox.warning(self, "تنبيه", "تم إنشاء الفاتورة ولكن قد تكون هناك مشكلة. يرجى التحقق من قائمة المشتريات.")
                else:
                    print(f"[DEBUG] Compra verificada correctamente. Datos: {purchase_verificado}")
                    QMessageBox.information(self, "تم بنجاح", "تم إضافة عملية الشراء بنجاح.")

                # Actualizar la vista
                self.refresh_page()
                return True

            except Exception as e:
                import traceback
                print("[DEBUG] Error al crear la compra:")
                print(traceback.format_exc())
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة عملية الشراء: {str(e)}")
                return False

        # Si el diálogo fue cancelado
        print("[DEBUG] La compra no se añadió correctamente, manteniendo diálogo actual")
        return False

    def apply_styles(self):
        """تطبيق الأنماط على العناصر"""
        # استخدام التنسيقات من ملف الستايلات بدلاً من التعريف المحلي
        self.setStyleSheet(AppStyles.get_all_view_styles())

        # Aplicar estilo para la tabla de proveedores con un fondo uniforme
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
            }
            QTableWidget::item {
                background-color: #f8fafc;
                padding: 8px;
                border-bottom: 1px solid #e2e8f0;
            }
            QTableWidget::item:selected {
                background-color: rgba(59, 130, 246, 0.3);
                color: #0f172a;
            }
        """)

    def refresh_page(self):
        """تحديث بيانات الصفحة عند الانتقال إليها أو عند فتح التطبيق"""
        # تحديث بيانات الموردين في الجدول
        self.populate_suppliers_table()

        # تحديث حالة تفعيل الأزرار بناءً على صلاحيات المستخدم
        self.update_buttons_state()

        print("تم تحديث صفحة الموردين")

    def update_buttons_state(self):
        """تحديث حالة تفعيل الأزرار بناءً على صلاحيات المستخدم"""
        # التحقق من وجود مستخدم حالي
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        if not main_window or not main_window.current_user:
            return

        user_id = main_window.current_user.get('id')
        username = main_window.current_user.get('username')

        # المستخدم admin له جميع الصلاحيات
        if username == 'admin':
            # تفعيل جميع الأزرار
            self.new_supplier_btn.setEnabled(True)
            self.payments_history_btn.setEnabled(True)
            self.new_supplier_btn.setStyleSheet("")
            self.payments_history_btn.setStyleSheet("""
                QPushButton#action_button {
                    background-color: #3b82f6;
                    color: white;
                }
                QPushButton#action_button:hover {
                    background-color: #2563eb;
                }
            """)
            return

        # استيراد وحدة التحكم بالمستخدمين
        from controllers.user_controller import UserController

        # تحديث حالة زر إضافة مورد جديد
        has_add_supplier_permission = UserController.check_permission(user_id, "إضافة مورد")
        self.new_supplier_btn.setEnabled(has_add_supplier_permission)
        if not has_add_supplier_permission:
            self.new_supplier_btn.setStyleSheet("""
                background-color: #f0f0f0;
                color: #999999;
                border: 1px solid #cccccc;
            """)
        else:
            self.new_supplier_btn.setStyleSheet("")

        # تحديث حالة زر عرض سجل المدفوعات
        has_view_payments_permission = UserController.check_permission(user_id, "عرض سجل المدفوعات")
        self.payments_history_btn.setEnabled(has_view_payments_permission)
        if not has_view_payments_permission:
            self.payments_history_btn.setStyleSheet("""
                background-color: #f0f0f0;
                color: #999999;
                border: 1px solid #cccccc;
            """)
        else:
            self.payments_history_btn.setStyleSheet("""
                QPushButton#action_button {
                    background-color: #3b82f6;
                    color: white;
                }
                QPushButton#action_button:hover {
                    background-color: #2563eb;
                }
            """)

    def show_payments_history(self):
        """عرض سجل مدفوعات ديون الموردين"""
        try:
            # التحقق من وجود الجدول أولا
            table_exists = db.fetch_one("SELECT name FROM sqlite_master WHERE type='table' AND name='supplier_payments'")

            if not table_exists:
                # إنشاء الجدول إذا لم يكن موجودًا
                db.execute("""
                    CREATE TABLE IF NOT EXISTS supplier_payments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        supplier_id INTEGER NOT NULL,
                        amount REAL NOT NULL,
                        payment_date TEXT NOT NULL,
                        notes TEXT,
                        user_id INTEGER,
                        FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
                    )
                """)
                db.commit()

                QMessageBox.information(
                    self,
                    "سجل المدفوعات",
                    "تم إنشاء جدول سجل المدفوعات. لا توجد مدفوعات مسجلة بعد."
                )

            # فتح نافذة عرض سجل المدفوعات
            payments_dialog = SupplierPaymentsHistoryDialog(self)
            payments_dialog.exec_()

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء فتح سجل المدفوعات: {str(e)}"
            )
            print(f"[ERROR] خطأ في فتح سجل المدفوعات: {str(e)}")

class SupplierDialog(QDialog):
    """نافذة إضافة أو تعديل مورد"""
    def __init__(self, parent=None, supplier_data=None):
        super().__init__(parent)

        # تعيين عنوان النافذة حسب الوضع (إضافة/تعديل)
        self.is_edit_mode = supplier_data is not None
        if self.is_edit_mode:
            self.setWindowTitle("تعديل بيانات المورد")
        else:
            self.setWindowTitle("إضافة مورد جديد")

        self.setMinimumWidth(450)
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # إنشاء نموذج إدخال البيانات
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setSpacing(15)

        # حقل اسم المورد
        self.name_input = QLineEdit()
        self.name_input.setObjectName("search_input")
        self.name_input.setPlaceholderText("أدخل اسم المورد")
        form_layout.addRow("اسم المورد:", self.name_input)

        # حقل رقم الهاتف
        self.phone_input = QLineEdit()
        self.phone_input.setObjectName("search_input")
        self.phone_input.setPlaceholderText("أدخل رقم الهاتف")
        form_layout.addRow("رقم الهاتف:", self.phone_input)

        # حقل البريد الإلكتروني
        self.email_input = QLineEdit()
        self.email_input.setObjectName("search_input")
        self.email_input.setPlaceholderText("أدخل البريد الإلكتروني")
        form_layout.addRow("البريد الإلكتروني:", self.email_input)

        # حقل العنوان
        self.address_input = QLineEdit()
        self.address_input.setObjectName("search_input")
        self.address_input.setPlaceholderText("أدخل العنوان")
        form_layout.addRow("العنوان:", self.address_input)

        # حقل الشخص المسؤول
        self.contact_person_input = QLineEdit()
        self.contact_person_input.setObjectName("search_input")
        self.contact_person_input.setPlaceholderText("أدخل اسم الشخص المسؤول")
        form_layout.addRow("الشخص المسؤول:", self.contact_person_input)

        layout.addLayout(form_layout)
        layout.addSpacing(20)

        # إضافة أزرار الحفظ والإلغاء
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)

        # تعريب الأزرار
        buttons.button(QDialogButtonBox.Ok).setText("حفظ")
        buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")

        layout.addWidget(buttons)

        # تعبئة البيانات إذا كنا في وضع التعديل
        if self.is_edit_mode:
            self.name_input.setText(supplier_data["name"])
            self.phone_input.setText(supplier_data["phone"])
            self.email_input.setText(supplier_data["email"])
            self.address_input.setText(supplier_data["address"])
            self.contact_person_input.setText(supplier_data["contact_person"])

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def get_supplier_data(self):
        """الحصول على بيانات المورد المدخلة"""
        return {
            "name": self.name_input.text(),
            "phone": self.phone_input.text(),
            "email": self.email_input.text(),
            "address": self.address_input.text(),
            "contact_person": self.contact_person_input.text(),
        }

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        self.setStyleSheet(AppStyles.get_all_view_styles())

class SupplierSelectionDialog(QDialog):
    """نافذة اختيار مورد"""
    def __init__(self, parent, suppliers_data):
        super().__init__(parent)

        self.suppliers_data = suppliers_data
        self.selected_supplier = None

        self.setWindowTitle("اختيار مورد")
        self.setMinimumSize(500, 400)
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # إضافة مربع بحث
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        search_label.setObjectName("field_label")

        self.search_input = QLineEdit()
        self.search_input.setObjectName("search_input")
        self.search_input.setPlaceholderText("ابحث عن مورد...")
        self.search_input.setClearButtonEnabled(True)
        self.search_input.textChanged.connect(self.filter_suppliers)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)

        layout.addLayout(search_layout)
        layout.addSpacing(10)

        # إضافة جدول الموردين
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setObjectName("suppliers_table")
        self.suppliers_table.setColumnCount(4)
        self.suppliers_table.setHorizontalHeaderLabels(["#", "اسم المورد", "رقم الهاتف", "الشخص المسؤول"])
        self.suppliers_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.suppliers_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.suppliers_table.verticalHeader().setVisible(False)
        self.suppliers_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.suppliers_table.setSelectionMode(QAbstractItemView.SingleSelection)

        # إضافة النقر المزدوج لاختيار المورد
        self.suppliers_table.doubleClicked.connect(self.on_supplier_selected)

        layout.addWidget(self.suppliers_table)

        # إضافة أزرار الاختيار والإلغاء
        buttons_layout = QHBoxLayout()

        select_button = QPushButton("اختيار")
        select_button.setObjectName("action_button")
        select_button.clicked.connect(self.on_select_button_clicked)

        new_supplier_button = QPushButton("إضافة مورد جديد")
        new_supplier_button.setObjectName("secondary_button")
        new_supplier_button.clicked.connect(self.on_new_supplier_button_clicked)

        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(select_button)
        buttons_layout.addWidget(new_supplier_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_button)

        layout.addSpacing(10)
        layout.addLayout(buttons_layout)

        # ملء جدول الموردين
        self.populate_suppliers_table()

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def populate_suppliers_table(self):
        """ملء جدول الموردين"""
        # تنظيف الجدول
        self.suppliers_table.clearContents()
        self.suppliers_table.setRowCount(0)

        # إضافة بيانات الموردين
        for supplier in self.suppliers_data:
            row = self.suppliers_table.rowCount()
            self.suppliers_table.insertRow(row)

            # رقم المورد
            id_item = QTableWidgetItem(str(supplier["id"]))
            id_item.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 0, id_item)

            # اسم المورد
            name_item = QTableWidgetItem(supplier["name"])
            self.suppliers_table.setItem(row, 1, name_item)

            # رقم الهاتف
            phone_item = QTableWidgetItem(supplier["phone"])
            phone_item.setTextAlignment(Qt.AlignCenter)
            self.suppliers_table.setItem(row, 2, phone_item)

            # الشخص المسؤول
            contact_item = QTableWidgetItem(supplier["contact_person"])
            self.suppliers_table.setItem(row, 3, contact_item)

    def filter_suppliers(self):
        """تصفية الموردين حسب معايير البحث"""
        search_text = self.search_input.text().lower()

        for row in range(self.suppliers_table.rowCount()):
            supplier_name = self.suppliers_table.item(row, 1).text().lower()
            supplier_phone = self.suppliers_table.item(row, 2).text().lower()
            supplier_contact = self.suppliers_table.item(row, 3).text().lower()

            if search_text in supplier_name or search_text in supplier_phone or search_text in supplier_contact:
                self.suppliers_table.setRowHidden(row, False)
            else:
                self.suppliers_table.setRowHidden(row, True)

    def on_supplier_selected(self, index):
        """التعامل مع حدث النقر المزدوج لاختيار المورد"""
        row = index.row()
        supplier_id = self.suppliers_table.item(row, 0).data(Qt.UserRole)

        # البحث عن المورد المحدد
        for supplier in self.suppliers_data:
            if supplier["id"] == supplier_id:
                self.selected_supplier = supplier
                self.accept()
                break

    def on_select_button_clicked(self):
        """التعامل مع حدث النقر على زر الاختيار"""
        selected_rows = self.suppliers_table.selectedIndexes()
        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "يرجى تحديد مورد أولاً.")
            return

        row = selected_rows[0].row()
        supplier_id = self.suppliers_table.item(row, 0).data(Qt.UserRole)

        # البحث عن المورد المحدد
        for supplier in self.suppliers_data:
            if supplier["id"] == supplier_id:
                self.selected_supplier = supplier
                self.accept()
                break

    def on_new_supplier_button_clicked(self):
        """التعامل مع حدث النقر على زر إضافة مورد جديد"""
        parent = self.parentWidget()
        if parent:
            self.reject()
            parent.add_new_supplier()

    def get_selected_supplier(self):
        """الحصول على المورد المحدد"""
        return self.selected_supplier

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        self.setStyleSheet(AppStyles.get_all_view_styles())

        # Aplicar estilo para la tabla de proveedores con un fondo uniforme
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
            }
            QTableWidget::item {
                background-color: #f8fafc;
                padding: 8px;
                border-bottom: 1px solid #e2e8f0;
            }
            QTableWidget::item:selected {
                background-color: rgba(59, 130, 246, 0.3);
                color: #0f172a;
            }
        """)

class PurchaseDialog(QDialog):
    """نافذة إنشاء عملية شراء جديدة"""
    def __init__(self, parent, supplier):
        super().__init__(parent)

        self.supplier = supplier
        self.items = []

        self.setWindowTitle(f"عملية شراء جديدة من: {supplier['name']}")
        self.setMinimumSize(900, 650)
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # تهيئة قيم افتراضية للمرجع والتاريخ والحالة (لاستخدامها في الحفظ، ولكن بدون عرض القسم)
        now = QDate.currentDate()
        self.reference_number = f"PO-{now.year()}-{now.month():02d}-{now.day():02d}"
        self.purchase_date = now
        self.purchase_status = "مدفوعة"  # حالة افتراضية: مدفوعة
        self.paid_amount = 0  # المبلغ المدفوع
        self.remaining_amount = 0  # المبلغ المتبقي

        # قسم عناصر عملية الشراء
        items_section = QFrame()
        items_section.setObjectName("filter_frame")
        items_layout = QVBoxLayout(items_section)

        # عنوان القسم
        items_title = QLabel("عناصر عملية الشراء")
        items_title.setObjectName("section_title")
        items_title.setFont(QFont("Arial", 12, QFont.Bold))
        items_layout.addWidget(items_title)

        # فاصل أفقي
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.HLine)
        separator2.setFrameShadow(QFrame.Sunken)
        separator2.setObjectName("filter_separator")
        items_layout.addWidget(separator2)

        # أزرار إضافة عناصر (إزالة زر الحذف واستبداله بقائمة سياق)
        items_buttons_layout = QHBoxLayout()

        add_item_button = QPushButton("➕  إضافة عنصر")
        add_item_button.setObjectName("secondary_button")
        add_item_button.clicked.connect(self.add_item)

        items_buttons_layout.addWidget(add_item_button)
        items_buttons_layout.addStretch()

        items_layout.addLayout(items_buttons_layout)
        items_layout.addSpacing(10)

        # جدول عناصر عملية الشراء
        self.items_table = QTableWidget()
        self.items_table.setObjectName("items_table")
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels(["#", "اسم المنتج", "السعر", "الكمية", "الإجمالي", "ملاحظات"])
        self.items_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.items_table.verticalHeader().setVisible(False)
        self.items_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setAlternatingRowColors(False)

        # إضافة قائمة السياق للجدول
        self.items_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.items_table.customContextMenuRequested.connect(self.show_context_menu)

        # Aplicar un color de fondo uniforme para todas las filas
        self.items_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
            }
            QTableWidget::item {
                background-color: #f8fafc;
                padding: 8px;
                border-bottom: 1px solid #e2e8f0;
            }
            QTableWidget::item:selected {
                background-color: rgba(59, 130, 246, 0.3);
                color: #0f172a;
            }
        """)

        items_layout.addWidget(self.items_table)

        # إجمالي عملية الشراء
        totals_layout = QHBoxLayout()

        # عدد العناصر
        self.items_count_label = QLabel("عدد العناصر: 0")
        self.items_count_label.setObjectName("field_label")

        # إجمالي المبلغ
        self.total_amount_label = QLabel("إجمالي المبلغ: 0.00 ج.م")
        self.total_amount_label.setObjectName("field_label")
        self.total_amount_label.setFont(QFont("Arial", 10, QFont.Bold))

        totals_layout.addWidget(self.items_count_label)
        totals_layout.addStretch()
        totals_layout.addWidget(self.total_amount_label)

        items_layout.addLayout(totals_layout)
        layout.addWidget(items_section)

        # إضافة قسم خيارات الدفع
        payment_section = QFrame()
        payment_section.setObjectName("filter_frame")
        payment_layout = QVBoxLayout(payment_section)

        # عنوان القسم
        payment_title = QLabel("خيارات الدفع")
        payment_title.setObjectName("section_title")
        payment_title.setFont(QFont("Arial", 12, QFont.Bold))
        payment_layout.addWidget(payment_title)

        # فاصل أفقي
        separator3 = QFrame()
        separator3.setFrameShape(QFrame.HLine)
        separator3.setFrameShadow(QFrame.Sunken)
        separator3.setObjectName("filter_separator")
        payment_layout.addWidget(separator3)

        # إنشاء تخطيط للخيارات
        options_layout = QHBoxLayout()

        # إنشاء مجموعة أزرار الاختيار
        self.payment_group = QButtonGroup(self)

        # خيار الدفع الفوري
        self.paid_radio = QRadioButton("مدفوعة")
        self.paid_radio.setChecked(True)  # مختار افتراضيًا
        self.paid_radio.setObjectName("payment_option")
        self.paid_radio.setFont(QFont("Arial", 10))
        self.paid_radio.clicked.connect(self.update_payment_status)
        self.payment_group.addButton(self.paid_radio)

        # خيار الدفع الآجل
        self.unpaid_radio = QRadioButton("آجل (غير مدفوعة)")
        self.unpaid_radio.setObjectName("payment_option")
        self.unpaid_radio.setFont(QFont("Arial", 10))
        self.unpaid_radio.clicked.connect(self.update_payment_status)
        self.payment_group.addButton(self.unpaid_radio)

        # إضافة الأزرار إلى التخطيط
        options_layout.addWidget(self.paid_radio)
        options_layout.addWidget(self.unpaid_radio)
        options_layout.addStretch()

        payment_layout.addLayout(options_layout)

        # إنشاء قسم تفاصيل الدفع الجزئي
        self.partial_payment_section = QFrame()
        partial_payment_layout = QVBoxLayout(self.partial_payment_section)
        partial_payment_layout.setContentsMargins(10, 0, 10, 0)

        # عنوان القسم الفرعي
        partial_title = QLabel("تفاصيل الدفع الجزئي")
        partial_title.setFont(QFont("Arial", 10, QFont.Bold))
        partial_payment_layout.addWidget(partial_title)

        # تخطيط الحقول
        fields_layout = QGridLayout()
        fields_layout.setColumnStretch(1, 1)  # جعل العمود الثاني يأخذ المساحة المتاحة

        # حقل المبلغ الإجمالي (للعرض فقط)
        total_label = QLabel("المبلغ الإجمالي:")
        total_label.setObjectName("field_label")
        self.total_display = QLabel("0.00 ج.م")
        self.total_display.setObjectName("value_label")
        self.total_display.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        fields_layout.addWidget(total_label, 0, 0)
        fields_layout.addWidget(self.total_display, 0, 1)

        # حقل المبلغ المدفوع
        paid_label = QLabel("المبلغ المدفوع:")
        paid_label.setObjectName("field_label")
        self.paid_amount_input = QDoubleSpinBox()
        self.paid_amount_input.setRange(0, 9999999)
        self.paid_amount_input.setDecimals(2)
        self.paid_amount_input.setSuffix(" ج.م")
        self.paid_amount_input.setAlignment(Qt.AlignLeft)
        self.paid_amount_input.valueChanged.connect(self.update_remaining_amount)
        fields_layout.addWidget(paid_label, 1, 0)
        fields_layout.addWidget(self.paid_amount_input, 1, 1)

        # حقل المبلغ المتبقي (للعرض فقط)
        remaining_label = QLabel("المبلغ المتبقي:")
        remaining_label.setObjectName("field_label")
        self.remaining_display = QLabel("0.00 ج.م")
        self.remaining_display.setObjectName("value_label")
        self.remaining_display.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.remaining_display.setStyleSheet("color: #ef4444; font-weight: bold;")
        fields_layout.addWidget(remaining_label, 2, 0)
        fields_layout.addWidget(self.remaining_display, 2, 1)

        partial_payment_layout.addLayout(fields_layout)
        payment_layout.addWidget(self.partial_payment_section)

        # إخفاء قسم الدفع الجزئي بشكل افتراضي
        self.partial_payment_section.setVisible(False)

        # وصف الخيارات
        payment_description = QLabel("اختر 'مدفوعة' للدفع الفوري أو 'آجل' لتسجيل الفاتورة كدين على المتجر")
        payment_description.setObjectName("hint_label")
        payment_description.setStyleSheet("color: #64748b; font-size: 9pt;")
        payment_layout.addWidget(payment_description)

        layout.addWidget(payment_section)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        cancel_button = QPushButton("إلغاء")
        cancel_button.setObjectName("cancel_button")
        cancel_button.clicked.connect(self.reject)

        save_button = QPushButton("💾  حفظ عملية الشراء")
        save_button.setObjectName("primary_button")
        save_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_button)

        layout.addSpacing(10)
        layout.addLayout(buttons_layout)

        # تحديث الإجماليات
        self.update_totals()

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def show_context_menu(self, position):
        """عرض قائمة السياق عند النقر بزر الماوس الأيمن على جدول العناصر"""
        # التحقق من وجود صف محدد
        if self.items_table.rowCount() == 0:
            return

        selected_rows = self.items_table.selectedIndexes()
        if not selected_rows:
            return

        # إنشاء قائمة السياق
        context_menu = QMenu(self)

        # إضافة خيارات القائمة
        delete_action = QAction("❌ حذف العنصر", self)
        delete_action.triggered.connect(self.delete_selected_item)
        context_menu.addAction(delete_action)

        # عرض القائمة في موقع المؤشر
        context_menu.exec_(self.items_table.viewport().mapToGlobal(position))

    def add_item(self):
        """إضافة عنصر جديد لعملية الشراء"""
        dialog = PurchaseItemDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            # الحصول على بيانات العنصر
            item_data = dialog.get_item_data()

            # التحقق من وضع التعديل أو الإضافة
            if item_data.get('is_editing', False) and item_data.get('existing_product_id'):
                # وضع التعديل - تحديث المنتج الموجود في قاعدة البيانات
                from models.products import ProductModel

                product_id = item_data['existing_product_id']
                old_stock = item_data.get('old_stock', 0)
                new_stock = item_data['stock']

                # تحديث بيانات المنتج في قاعدة البيانات
                update_data = {
                    'name': item_data['name'],
                    'category': item_data['category'],
                    'price': item_data['sell_price'],
                    'cost': item_data['cost'],
                    'stock': new_stock
                }

                success = ProductModel.update_product(product_id, update_data)

                if success:
                    print(f"[DEBUG] تم تحديث المنتج الموجود: {item_data['name']}")
                    # إضافة معرف المنتج الحقيقي
                    item_data["product_id"] = product_id
                else:
                    print(f"[DEBUG] فشل في تحديث المنتج: {item_data['name']}")
                    QMessageBox.warning(self, "خطأ", f"فشل في تحديث المنتج: {item_data['name']}")
                    return
            else:
                # وضع الإضافة - البحث عن المنتج أو إنشاء جديد
                from models.products import ProductModel
                product_code = item_data.get('product_code')
                product_name = item_data.get('name')

                # Verificar si el producto ya existe por código o nombre
                existing_product = None
                if product_code:
                    print(f"[DEBUG] Buscando producto existente por código: {product_code}")
                    existing_product = ProductModel.get_product_by_code(product_code)

                if not existing_product and product_name:
                    print(f"[DEBUG] Buscando producto existente por nombre: {product_name}")
                    existing_product = ProductModel.get_product_by_name(product_name)

                # Si encontramos un producto existente, usamos su ID real
                if existing_product:
                    print(f"[DEBUG] Producto encontrado en la base de datos: {existing_product}")
                    item_data["product_id"] = existing_product.get('id')
                    print(f"[DEBUG] Usando ID de producto existente: {item_data['product_id']}")
                else:
                    # Para productos nuevos, usamos identificador temporal negativo
                    # para diferenciarlos de los productos existentes
                    print(f"[DEBUG] Producto nuevo, asignando ID temporal")
                    item_data["product_id"] = None  # Será creado al guardar la factura

            # Asegurar que tenemos el nombre y código de producto
            if not item_data.get("product_name"):
                item_data["product_name"] = item_data.get("name", "")

            # Añadir a la lista de productos
            self.items.append(item_data)

            # تحديث الجدول
            self.update_items_table()

            # تحديث الإجماليات
            self.update_totals()

    def delete_selected_item(self):
        """حذف العنصر المحدد من جدول العناصر"""
        selected_rows = self.items_table.selectedIndexes()
        if not selected_rows:
            QMessageBox.warning(self, "تنبيه", "يرجى تحديد عنصر لحذفه.")
            return

        row = selected_rows[0].row()
        product_name = self.items_table.item(row, 1).text()

        # عرض رسالة تأكيد قبل الحذف
        confirmation = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المنتج '{product_name}' من قائمة المشتريات؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirmation == QMessageBox.Yes:
            # حذف العنصر من القائمة حسب الصف المحدد
            if row >= 0 and row < len(self.items):
                del self.items[row]

                # تحديث الجدول
                self.update_items_table()

                # تحديث الإجماليات
                self.update_totals()

    def update_items_table(self):
        """تحديث جدول عناصر عملية الشراء"""
        # تنظيف الجدول
        self.items_table.clearContents()
        self.items_table.setRowCount(0)

        # إضافة العناصر للجدول
        for i, item in enumerate(self.items):
            row = self.items_table.rowCount()
            self.items_table.insertRow(row)

            # ترقيم المنتج (معرف أو فهرس)
            id_text = "جديد"
            if item.get("product_id") is not None:
                id_text = str(item["product_id"])
            else:
                id_text = f"جديد {i+1}"

            id_item = QTableWidgetItem(id_text)
            id_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 0, id_item)

            # اسم المنتج
            name_item = QTableWidgetItem(item["name"])
            self.items_table.setItem(row, 1, name_item)

            # السعر
            price_item = QTableWidgetItem(f"{item['price']:.2f}")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 2, price_item)

            # الكمية
            quantity_item = QTableWidgetItem(str(item["quantity"]))
            quantity_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 3, quantity_item)

            # الإجمالي
            total_item = QTableWidgetItem(f"{item['total']:.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 4, total_item)

            # ملاحظات
            notes_item = QTableWidgetItem(item.get("notes", ""))
            self.items_table.setItem(row, 5, notes_item)

    def update_totals(self):
        """تحديث إجماليات عملية الشراء"""
        items_count = len(self.items)
        total_amount = sum(item["total"] for item in self.items)

        self.items_count_label.setText(f"عدد العناصر: {items_count}")
        self.total_amount_label.setText(f"إجمالي المبلغ: {total_amount:.2f} ج.م")

        # تحديث المبلغ الإجمالي في قسم الدفع الجزئي
        if hasattr(self, 'total_display'):
            self.total_display.setText(f"{total_amount:.2f} ج.م")

        # تحديث حالة الدفع والمبالغ
        self.update_payment_status()

    def get_purchase_data(self):
        """الحصول على بيانات عملية الشراء"""
        total_amount = sum(item["total"] for item in self.items)

        # تحديث المبالغ حسب حالة الدفع
        if self.paid_radio.isChecked():
            paid_amount = total_amount
            remaining_amount = 0
            status = "مدفوعة"
            payment_method = "نقداً"
        else:
            paid_amount = self.paid_amount
            remaining_amount = self.remaining_amount
            status = "غير مدفوعة"
            payment_method = "آجل"  # Método de pago aplazado cuando no está pagado completamente

            # التأكد من أن المبالغ متوافقة
            if paid_amount > total_amount:
                paid_amount = total_amount

            remaining_amount = total_amount - paid_amount

            # التحقق من الحالة مرة أخرى
            if remaining_amount <= 0:
                status = "مدفوعة"
                payment_method = "نقداً"
            else:
                status = "غير مدفوعة"

        return {
            "reference_number": self.reference_number,
            "date": self.purchase_date.toString("yyyy/MM/dd"),
            "status": status,
            "subtotal": total_amount,
            "tax": 0,
            "discount": 0,
            "total": total_amount,
            "paid_amount": paid_amount,
            "remaining_amount": remaining_amount,
            "payment_method": payment_method,
            "notes": "",
            "supplier_id": self.supplier['id']
        }

    def get_purchase_items(self):
        """الحصول على عناصر عملية الشراء"""
        return self.items

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        self.setStyleSheet(AppStyles.get_all_view_styles())

    def update_payment_status(self):
        """تحديث حالة الدفع بناءً على الخيار المحدد"""
        total = sum(item["total"] for item in self.items)

        if self.paid_radio.isChecked():
            self.purchase_status = "مدفوعة"
            # إخفاء قسم الدفع الجزئي
            self.partial_payment_section.setVisible(False)
            # تحديث المبلغ المدفوع والمتبقي
            self.paid_amount = total
            self.remaining_amount = 0
        else:
            self.purchase_status = "غير مدفوعة"
            # عرض قسم الدفع الجزئي
            self.partial_payment_section.setVisible(True)
            # تحديث المبلغ الإجمالي في قسم الدفع الجزئي
            self.total_display.setText(f"{total:.2f} ج.م")

            # الحصول على قيمة المبلغ المدفوع الحالية
            current_paid = self.paid_amount_input.value()

            # إذا كان المبلغ المدفوع صفراً، نحتفظ به كما هو
            # وإلا نتأكد من أنه لا يتجاوز الإجمالي
            if current_paid > 0:
                self.paid_amount_input.setValue(min(current_paid, total))

            # حساب المبلغ المتبقي
            self.update_remaining_amount()

    def update_remaining_amount(self):
        """تحديث المبلغ المتبقي بناءً على المبلغ المدفوع"""
        total = sum(item["total"] for item in self.items)
        paid = self.paid_amount_input.value()

        # التأكد من أن المبلغ المدفوع لا يتجاوز الإجمالي
        if paid > total:
            paid = total
            self.paid_amount_input.setValue(total)

        # حساب المبلغ المتبقي
        remaining = total - paid
        self.remaining_display.setText(f"{remaining:.2f} ج.م")

        # تحديث المتغيرات للاستخدام في الحفظ
        self.paid_amount = paid
        self.remaining_amount = remaining

        # تحديث حالة الفاتورة بناءً على المبلغ المدفوع
        if remaining <= 0:
            self.purchase_status = "مدفوعة"
        else:
            self.purchase_status = "غير مدفوعة"

class PurchaseItemDialog(QDialog):
    """نافذة إضافة عنصر لعملية الشراء"""
    def __init__(self, parent):
        super().__init__(parent)

        # متغيرات لتتبع حالة المنتج
        self.existing_product = None
        self.is_editing_mode = False

        self.setWindowTitle("إضافة عنصر جديد")
        self.setFixedSize(450, 420)  # تعيين حجم ثابت للنافذة
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 10, 8, 10)  # تقليل الهوامش
        layout.setSpacing(3)  # تقليل المسافة بين العناصر

        # إنشاء نموذج بيانات
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setSpacing(5)  # تقليل المسافة بين العناصر

        # حقل كود المنتج
        code_label = QLabel("كود المنتج:")

        # إنشاء ويدجت حاوي لحقل الإدخال وزر التوليد
        code_widget = QWidget()

        # جعل الويدجت الحاوي شفافًا (إزالة الخلفية البيضاء)
        code_widget.setStyleSheet("background-color: transparent;")

        # إنشاء تخطيط أفقي للويدجت الحاوي
        code_layout = QHBoxLayout(code_widget)
        code_layout.setSpacing(5)
        code_layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء حقل إدخال الكود وتحديد حجمه
        self.barcode_input = QLineEdit()
        self.barcode_input.setPlaceholderText("أدخل كود المنتج")
        self.barcode_input.setMaxLength(50)
        self.barcode_input.setFixedWidth(150)  # تحديد عرض ثابت لحقل الإدخال
        self.barcode_input.setAlignment(Qt.AlignCenter)  # توسيط النص في حقل الإدخال

        # ربط حدث تغيير النص للتحقق من وجود المنتج
        self.barcode_input.textChanged.connect(self.check_existing_product)

        # إنشاء زر توليد كود بار عشوائي
        self.generate_barcode_btn = QPushButton("توليد")
        self.generate_barcode_btn.setObjectName("secondary_button")
        self.generate_barcode_btn.setFixedWidth(50)  # تقليل العرض من 60 إلى 50 بكسل
        self.generate_barcode_btn.clicked.connect(self.generate_random_barcode)

        # إضافة مساحة مرنة لدفع الزر إلى أقصى اليسار
        code_layout.addWidget(self.barcode_input)
        code_layout.addStretch(1)  # إضافة مساحة مرنة بين حقل الإدخال والزر
        code_layout.addWidget(self.generate_barcode_btn)

        # إضافة الويدجت الحاوي إلى نموذج البيانات
        form_layout.addRow(code_label, code_widget)

        # حقل اسم المنتج
        name_label = QLabel("اسم المنتج:")
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("أدخل اسم المنتج")
        self.name_input.setMaxLength(100)
        self.name_input.setAlignment(Qt.AlignCenter)  # توسيط النص في حقل الإدخال
        form_layout.addRow(name_label, self.name_input)

        # حقل تصنيف المنتج
        category_label = QLabel("تصنيف المنتج:")
        self.category_input = RTLComboBox()
        self.category_input.setObjectName("combo_box")
        self.category_input.setEditable(True)  # جعل الكومبوبوكس قابل للتحرير
        self.category_input.lineEdit().setAlignment(Qt.AlignCenter)  # توسيط النص

        # جلب قائمة التصنيفات من قاعدة البيانات
        try:
            from models.products import ProductModel
            categories = ProductModel.get_product_categories()
            self.category_input.addItems(categories)
            self.category_input.setCurrentText("")  # جعل الحقل فارغًا بشكل افتراضي
        except:
            # في حالة وجود مشكلة في جلب التصنيفات، إضافة تصنيفات افتراضية
            self.category_input.addItems(["مشروبات", "حلويات", "مأكولات", "أخرى"])

        form_layout.addRow(category_label, self.category_input)

        # حقل سعر الشراء (التكلفة)
        cost_label = QLabel("تكلفة المنتج:")
        self.cost_input = QDoubleSpinBox()
        self.cost_input.setRange(0, 100000)
        self.cost_input.setDecimals(2)
        self.cost_input.setSuffix(" ج.م")
        self.cost_input.setMinimumHeight(25)  # تقليل الارتفاع
        self.cost_input.setAlignment(Qt.AlignCenter)  # توسيط النص
        self.cost_input.valueChanged.connect(self.update_total)
        form_layout.addRow(cost_label, self.cost_input)

        # حقل سعر البيع
        price_label = QLabel("سعر البيع:")
        self.sell_price_input = QDoubleSpinBox()
        self.sell_price_input.setRange(0, 100000)
        self.sell_price_input.setDecimals(2)
        self.sell_price_input.setSuffix(" ج.م")
        self.sell_price_input.setMinimumHeight(25)  # تقليل الارتفاع
        self.sell_price_input.setAlignment(Qt.AlignCenter)  # توسيط النص
        form_layout.addRow(price_label, self.sell_price_input)

        # حقل الكمية
        quantity_label = QLabel("كمية المخزون:")
        self.quantity_input = QSpinBox()
        self.quantity_input.setRange(0, 10000)
        self.quantity_input.setMinimumHeight(25)  # تقليل الارتفاع
        self.quantity_input.setAlignment(Qt.AlignCenter)  # توسيط النص
        self.quantity_input.valueChanged.connect(self.update_total)
        form_layout.addRow(quantity_label, self.quantity_input)

        # حقل الإجمالي (للعرض فقط)
        total_label = QLabel("الإجمالي:")
        self.total_input = QLineEdit()
        self.total_input.setReadOnly(True)
        self.total_input.setAlignment(Qt.AlignCenter)  # توسيط النص
        form_layout.addRow(total_label, self.total_input)

        # حقل الملاحظات
        notes_label = QLabel("ملاحظات:")
        self.notes_input = QLineEdit()
        self.notes_input.setPlaceholderText("إضافة ملاحظات (اختياري)")
        self.notes_input.setAlignment(Qt.AlignCenter)  # توسيط النص
        form_layout.addRow(notes_label, self.notes_input)

        # تم إزالة خيار إضافة للمفضلة

        layout.addLayout(form_layout)

        # إضافة أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهامش العلوي
        buttons_layout.setSpacing(10)  # تعيين المسافة بين الأزرار

        # إضافة مساحة مرنة على الجانبين لتوسيط الأزرار
        buttons_layout.addStretch(1)

        # زر الحفظ
        save_button = QPushButton("حفظ")
        save_button.setObjectName("action_button")
        save_button.setMinimumHeight(30)  # تقليل الارتفاع من 35 إلى 30
        save_button.setFixedWidth(100)  # تقليل العرض من 120 إلى 100
        save_button.clicked.connect(self.accept)

        # زر الإلغاء
        cancel_button = QPushButton("إلغاء")
        cancel_button.setObjectName("secondary_button")
        cancel_button.setMinimumHeight(30)  # تقليل الارتفاع من 35 إلى 30
        cancel_button.setFixedWidth(100)  # تقليل العرض من 120 إلى 100
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addStretch(1)

        layout.addLayout(buttons_layout)

        # تحديث الإجمالي الأولي
        self.update_total()

        # توليد كود بار عشوائي افتراضي
        self.generate_random_barcode()

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def check_existing_product(self, barcode_text):
        """التحقق من وجود منتج بالباركود المدخل وملء الحقول إذا وجد"""
        # تجاهل النصوص الفارغة أو القصيرة جداً
        if not barcode_text or len(barcode_text.strip()) < 3:
            self.existing_product = None
            self.is_editing_mode = False
            self.setWindowTitle("إضافة عنصر جديد")
            return

        try:
            from models.products import ProductModel

            # البحث عن المنتج بالباركود
            product = ProductModel.get_product_by_code(barcode_text.strip())

            if product:
                # تم العثور على المنتج - ملء الحقول
                self.existing_product = product
                self.is_editing_mode = True

                # تغيير عنوان النافذة
                self.setWindowTitle(f"تعديل منتج: {product.get('name', '')}")

                # ملء الحقول ببيانات المنتج الموجود
                self.fill_product_fields(product)

            else:
                # لم يتم العثور على المنتج - وضع الإضافة
                self.existing_product = None
                self.is_editing_mode = False
                self.setWindowTitle("إضافة عنصر جديد")

        except Exception as e:
            print(f"خطأ في التحقق من وجود المنتج: {str(e)}")
            self.existing_product = None
            self.is_editing_mode = False

    def fill_product_fields(self, product):
        """ملء حقول النافذة ببيانات المنتج الموجود"""
        try:
            # منع إطلاق حدث textChanged أثناء ملء الحقول
            self.barcode_input.blockSignals(True)

            # ملء الحقول
            self.name_input.setText(str(product.get('name', '')))
            self.category_input.setCurrentText(str(product.get('category', '')))
            self.cost_input.setValue(float(product.get('cost', 0)))
            self.sell_price_input.setValue(float(product.get('price', 0)))
            self.quantity_input.setValue(int(product.get('stock', 0)))

            # إعادة تفعيل الإشارات
            self.barcode_input.blockSignals(False)

            # تحديث الإجمالي
            self.update_total()

        except Exception as e:
            print(f"خطأ في ملء حقول المنتج: {str(e)}")

    def generate_random_barcode(self):
        """توليد كود بار عشوائي فريد مكون من 8 أرقام فقط"""
        import random
        from models.products import ProductModel

        # توليد رقم عشوائي مكون من 8 أرقام
        barcode = str(random.randint(10000000, 99999999))

        # التحقق من عدم وجود المنتج بنفس الكود
        try:
            existing_product = ProductModel.get_product_by_code(barcode)

            # في حالة وجود منتج بنفس الكود، نولد كود جديد
            while existing_product:
                barcode = str(random.randint(10000000, 99999999))
                existing_product = ProductModel.get_product_by_code(barcode)
        except:
            # Si hay un error al verificar el producto, simplemente continuamos con el código generado
            print("No se pudo verificar si el código ya existe")

        self.barcode_input.setText(barcode)

    def update_total(self):
        """تحديث حساب الإجمالي"""
        cost = self.cost_input.value()
        quantity = self.quantity_input.value()
        total = cost * quantity
        self.total_input.setText(f"{total:.2f} ج.م")

    def get_item_data(self):
        """الحصول على بيانات العنصر"""
        cost = self.cost_input.value()
        sell_price = self.sell_price_input.value()
        quantity = self.quantity_input.value()
        total = cost * quantity
        product_name = self.name_input.text()
        barcode = self.barcode_input.text()
        category = self.category_input.currentText()

        # Asegurarse de que el nombre del producto y el código de barras no estén vacíos
        if not product_name.strip():
            product_name = f"Producto {datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"

        if not barcode.strip():
            self.generate_random_barcode()
            barcode = self.barcode_input.text()

        data = {
            "name": product_name,          # اسم المنتج
            "product_name": product_name,  # تضمين الاسم بمعرف بديل
            "price": cost,                 # سعر الشراء (للتوافق مع الكود القديم)
            "quantity": quantity,
            "stock": quantity,             # Añadir clave 'stock' para compatibilidad con ProductModel.add_product
            "total": total,
            "notes": self.notes_input.text(),
            "product_code": barcode,       # كود البار
            "code": barcode,               # كود البار (بمعرف بديل)
            "unit_price": cost,            # سعر الوحدة للشراء
            "cost": cost,                  # تكلفة الشراء
            "sell_price": sell_price,      # سعر البيع المقترح
            "total_price": total,          # إجمالي السعر (بمعرف بديل للتوافق مع الكود)
            "category": category,          # تصنيف المنتج
            "min_quantity": 5,             # قيمة افتراضية للحد الأدنى للمخزون
            "is_favorite": 0               # قيمة افتراضية للمفضلة
        }

        # إضافة معلومات إضافية في حالة التعديل
        if self.is_editing_mode and self.existing_product:
            data['existing_product_id'] = self.existing_product.get('id')
            data['is_editing'] = True
            data['old_stock'] = self.existing_product.get('stock', 0)
        else:
            data['is_editing'] = False

        return data

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        self.setStyleSheet("""
            QDialog {
                background-color: white;
            }
            QLabel {
                color: #334155;
            }
            QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                border: 1px solid #cbd5e1;
                border-radius: 4px;
                padding: 6px;
                background-color: white;
                color: #0f172a;
            }
            QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                border: 1px solid #3b82f6;
            }
            #action_button {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            #action_button:hover {
                background-color: #2563eb;
            }
            #secondary_button {
                background-color: white;
                color: #3b82f6;
                border: 1px solid #3b82f6;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            #secondary_button:hover {
                background-color: #f1f5f9;
            }
        """)

class SupplierDetailsDialog(QDialog):
    """نافذة عرض تفاصيل المورد وعمليات الشراء"""
    def __init__(self, parent, supplier, purchases):
        super().__init__(parent)

        self.supplier = supplier
        self.purchases = purchases if purchases else []

        # جلب إجمالي الديون المستحقة للمورد
        from models.suppliers import SupplierModel
        self.supplier_debt = SupplierModel.get_supplier_total_debt(supplier['id'])

        self.setWindowTitle(f"تفاصيل المورد: {supplier['name']}")
        self.setMinimumSize(800, 375)  # تعديل الارتفاع ليتطابق مع نافذة العميل
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # قسم بيانات المورد
        supplier_section = QFrame()
        supplier_section.setObjectName("filter_frame")
        supplier_section.setFrameShape(QFrame.StyledPanel)
        supplier_section_layout = QVBoxLayout(supplier_section)
        supplier_section_layout.setContentsMargins(10, 3, 10, 3)  # تقليل الهوامش العمودية
        supplier_section_layout.setSpacing(2)  # تقليل المسافة بين العناصر

        # تخطيط العنوان والفاصل في سطر واحد أفقي
        title_layout = QHBoxLayout()
        title_layout.setSpacing(5)

        # عنوان القسم
        section_title = QLabel("معلومات المورد")
        section_title.setObjectName("section_title")
        section_title.setFont(QFont("Arial", 10, QFont.Bold))
        title_layout.addWidget(section_title)

        # فاصل أفقي
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("filter_separator")
        title_layout.addWidget(separator, 1)

        supplier_section_layout.addLayout(title_layout)

        # بيانات المورد
        supplier_info_layout = QGridLayout()
        supplier_info_layout.setHorizontalSpacing(0)
        supplier_info_layout.setVerticalSpacing(2)  # تقليل المسافة العمودية

        # الصف الأول من البيانات
        name_label = QLabel("اسم المورد:")
        name_label.setObjectName("field_label")
        name_label.setFont(QFont("Arial", 9))

        name_value = QLabel(supplier["name"])
        name_value.setFont(QFont("Arial", 9, QFont.Bold))

        phone_label = QLabel("رقم الهاتف:")
        phone_label.setObjectName("field_label")
        phone_label.setFont(QFont("Arial", 9))

        phone_value = QLabel(supplier["phone"])
        phone_value.setFont(QFont("Arial", 9))

        # تعديل طريقة وضع العناصر لإزالة المسافة بين التسميات والقيم
        name_layout = QHBoxLayout()
        name_layout.setSpacing(0)  # بدون مسافة بين التسمية والقيمة
        name_layout.setContentsMargins(0, 0, 0, 0)  # بدون هوامش في التخطيط
        name_layout.addWidget(name_label)
        name_layout.addWidget(name_value)
        name_layout.addStretch(1)  # إضافة مسافة مرنة في النهاية

        phone_layout = QHBoxLayout()
        phone_layout.setSpacing(0)  # بدون مسافة بين التسمية والقيمة
        phone_layout.setContentsMargins(0, 0, 0, 0)  # بدون هوامش في التخطيط
        phone_layout.addWidget(phone_label)
        phone_layout.addWidget(phone_value)
        phone_layout.addStretch(1)  # إضافة مسافة مرنة في النهاية

        # إضافة التخطيطات إلى الشبكة
        temp_widget_name = QWidget()
        temp_widget_name.setLayout(name_layout)
        temp_widget_name.setContentsMargins(0, 0, 0, 0)  # بدون هوامش في الويدجت

        temp_widget_phone = QWidget()
        temp_widget_phone.setLayout(phone_layout)
        temp_widget_phone.setContentsMargins(0, 0, 0, 0)  # بدون هوامش في الويدجت

        supplier_info_layout.addWidget(temp_widget_name, 0, 0)
        supplier_info_layout.addWidget(temp_widget_phone, 0, 1)

        # الصف الثاني من البيانات
        email_label = QLabel("البريد الإلكتروني:")
        email_label.setObjectName("field_label")
        email_label.setFont(QFont("Arial", 9))

        email_value = QLabel(supplier["email"])
        email_value.setFont(QFont("Arial", 9))

        address_label = QLabel("العنوان:")
        address_label.setObjectName("field_label")
        address_label.setFont(QFont("Arial", 9))

        address_value = QLabel(supplier["address"])
        address_value.setFont(QFont("Arial", 9))

        # إنشاء تخطيطات أفقية بدون مسافات للبريد الإلكتروني والعنوان
        email_layout = QHBoxLayout()
        email_layout.setSpacing(0)
        email_layout.setContentsMargins(0, 0, 0, 0)  # بدون هوامش في التخطيط
        email_layout.addWidget(email_label)
        email_layout.addWidget(email_value)
        email_layout.addStretch(1)

        address_layout = QHBoxLayout()
        address_layout.setSpacing(0)
        address_layout.setContentsMargins(0, 0, 0, 0)  # بدون هوامش في التخطيط
        address_layout.addWidget(address_label)
        address_layout.addWidget(address_value)
        address_layout.addStretch(1)

        # إضافة التخطيطات إلى الشبكة
        temp_widget_email = QWidget()
        temp_widget_email.setLayout(email_layout)
        temp_widget_email.setContentsMargins(0, 0, 0, 0)  # بدون هوامش في الويدجت

        temp_widget_address = QWidget()
        temp_widget_address.setLayout(address_layout)
        temp_widget_address.setContentsMargins(0, 0, 0, 0)  # بدون هوامش في الويدجت

        supplier_info_layout.addWidget(temp_widget_email, 1, 0)
        supplier_info_layout.addWidget(temp_widget_address, 1, 1)

        # الصف الثالث من البيانات - إعادة تنظيم العناصر مثل نافذة العميل
        total_purchases_label = QLabel("إجمالي المشتريات:")
        total_purchases_label.setObjectName("field_label")
        total_purchases_label.setFont(QFont("Arial", 9))

        total_purchases_value = QLabel(f"{supplier.get('total_purchases', 0):.2f} ج.م")
        total_purchases_value.setFont(QFont("Arial", 9))

        purchase_count_label = QLabel("عدد عمليات الشراء:")
        purchase_count_label.setObjectName("field_label")
        purchase_count_label.setFont(QFont("Arial", 9))

        purchase_count_value = QLabel(str(supplier.get("purchase_count", 0)))
        purchase_count_value.setFont(QFont("Arial", 9))

        last_purchase_label = QLabel("آخر عملية شراء:")
        last_purchase_label.setObjectName("field_label")
        last_purchase_label.setFont(QFont("Arial", 9))

        last_purchase_value = QLabel(supplier.get("last_purchase_date", "") if supplier.get("last_purchase_date") else "لا يوجد")
        last_purchase_value.setFont(QFont("Arial", 9))

        # إنشاء تخطيطات أفقية بدون مسافات للصف الثالث - نفس طريقة العميل
        purchases_layout = QHBoxLayout()
        purchases_layout.setSpacing(0)
        purchases_layout.setContentsMargins(0, 0, 0, 0)
        purchases_layout.addWidget(total_purchases_label)
        purchases_layout.addWidget(total_purchases_value)
        purchases_layout.addStretch(1)

        count_layout = QHBoxLayout()
        count_layout.setSpacing(0)
        count_layout.setContentsMargins(0, 0, 0, 0)
        count_layout.addWidget(purchase_count_label)
        count_layout.addWidget(purchase_count_value)
        count_layout.addStretch(1)

        last_layout = QHBoxLayout()
        last_layout.setSpacing(0)
        last_layout.setContentsMargins(0, 0, 0, 0)
        last_layout.addWidget(last_purchase_label)
        last_layout.addWidget(last_purchase_value)
        last_layout.addStretch(1)

        # إضافة التخطيطات إلى الشبكة
        temp_widget_purchases = QWidget()
        temp_widget_purchases.setLayout(purchases_layout)
        temp_widget_purchases.setContentsMargins(0, 0, 0, 0)

        temp_widget_count = QWidget()
        temp_widget_count.setLayout(count_layout)
        temp_widget_count.setContentsMargins(0, 0, 0, 0)

        temp_widget_last = QWidget()
        temp_widget_last.setLayout(last_layout)
        temp_widget_last.setContentsMargins(0, 0, 0, 0)

        supplier_info_layout.addWidget(temp_widget_purchases, 2, 0)
        supplier_info_layout.addWidget(temp_widget_count, 2, 1)
        supplier_info_layout.addWidget(temp_widget_last, 2, 2)

        # إضافة الشخص المسؤول إلى صف جديد إذا كان موجودًا
        if supplier.get("contact_person"):
            contact_label = QLabel("الشخص المسؤول:")
            contact_label.setObjectName("field_label")
            contact_label.setFont(QFont("Arial", 9))

            contact_value = QLabel(supplier["contact_person"])
            contact_value.setFont(QFont("Arial", 9))

            contact_layout = QHBoxLayout()
            contact_layout.setSpacing(0)
            contact_layout.setContentsMargins(0, 0, 0, 0)
            contact_layout.addWidget(contact_label)
            contact_layout.addWidget(contact_value)
            contact_layout.addStretch(1)

            temp_widget_contact = QWidget()
            temp_widget_contact.setLayout(contact_layout)
            temp_widget_contact.setContentsMargins(0, 0, 0, 0)

            supplier_info_layout.addWidget(temp_widget_contact, 3, 0)

        supplier_section_layout.addLayout(supplier_info_layout)
        layout.addWidget(supplier_section)

        # قسم عمليات الشراء
        layout.addSpacing(5)  # تقليل المسافة من 10 إلى 5 مثل نافذة العميل

        purchases_section = QFrame()
        purchases_section.setObjectName("filter_frame")
        purchases_section.setFrameShape(QFrame.StyledPanel)
        purchases_section_layout = QVBoxLayout(purchases_section)
        purchases_section_layout.setContentsMargins(10, 5, 10, 5)  # تقليل الهوامش
        purchases_section_layout.setSpacing(3)  # تقليل المسافة بين العناصر

        # تخطيط العنوان والفاصل في سطر واحد أفقي
        title_products_layout = QHBoxLayout()
        title_products_layout.setSpacing(5)

        # عنوان القسم
        self.purchases_title = QLabel(f"سجل عمليات الشراء ({len(self.purchases)})")
        self.purchases_title.setObjectName("section_title")
        self.purchases_title.setFont(QFont("Arial", 10, QFont.Bold))  # تقليل حجم الخط من 12 إلى 10
        title_products_layout.addWidget(self.purchases_title)

        # فاصل أفقي
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.HLine)
        separator2.setFrameShadow(QFrame.Sunken)
        separator2.setObjectName("filter_separator")
        title_products_layout.addWidget(separator2, 1)

        purchases_section_layout.addLayout(title_products_layout)

        # جدول عمليات الشراء
        self.purchases_table = QTableWidget()
        self.purchases_table.setObjectName("items_table")
        self.purchases_table.setColumnCount(5)
        self.purchases_table.setHorizontalHeaderLabels(["رقم الطلب", "الرقم المرجعي", "التاريخ", "المبلغ", "الحالة"])
        self.purchases_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.purchases_table.verticalHeader().setVisible(False)
        self.purchases_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.purchases_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.purchases_table.setAlternatingRowColors(True)  # تفعيل ألوان الصفوف البديلة

        # إضافة قائمة السياق للجدول
        self.purchases_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.purchases_table.customContextMenuRequested.connect(self.show_purchases_context_menu)

        # إضافة النقر المزدوج لعرض تفاصيل عملية الشراء
        self.purchases_table.doubleClicked.connect(self.view_purchase_details)

        # ملء جدول عمليات الشراء - قم بتحديث القائمة فوراً عند بدء النافذة
        self.populate_purchases_table()

        purchases_section_layout.addWidget(self.purchases_table)

        layout.addWidget(purchases_section)

        # إضافة قسم حالة الرصيد (الديون والمستحقات)
        layout.addSpacing(5)

        balance_section = QFrame()
        balance_section.setObjectName("filter_frame")
        balance_section.setFrameShape(QFrame.StyledPanel)
        balance_section_layout = QVBoxLayout(balance_section)
        balance_section_layout.setContentsMargins(8, 4, 8, 4)
        balance_section_layout.setSpacing(2)  # تقليل المسافة العمودية

        # تخطيط العنوان والفاصل في سطر واحد أفقي
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش غير الضرورية

        # عنوان القسم - تصغير الخط
        balance_title = QLabel("حالة الرصيد")
        balance_title.setObjectName("section_title")
        balance_title.setFont(QFont("Arial", 10, QFont.Bold))
        title_layout.addWidget(balance_title)

        # فاصل أفقي
        separator3 = QFrame()
        separator3.setFrameShape(QFrame.HLine)
        separator3.setFrameShadow(QFrame.Sunken)
        separator3.setObjectName("filter_separator")
        title_layout.addWidget(separator3, 1)

        balance_section_layout.addLayout(title_layout)

        # إطار المبلغ المستحق للمورد (الدين للمورد) - باللون الأزرق
        debt_frame = QFrame()
        debt_frame.setObjectName("supplier_debt_frame")
        debt_frame.setFrameShape(QFrame.StyledPanel)
        debt_frame.setMaximumWidth(200)  # تحديد الحد الأقصى للعرض
        debt_frame.setStyleSheet("""
            #supplier_debt_frame {
                background-color: rgba(219, 234, 254, 0.7);
                border: 1px solid #3b82f6;
                border-radius: 4px;
                padding: 3px;
            }
        """)

        debt_layout = QVBoxLayout(debt_frame)
        debt_layout.setContentsMargins(3, 2, 3, 2)  # تقليل الهوامش
        debt_layout.setSpacing(1)

        debt_title = QLabel("الدين للمورد")
        debt_title.setStyleSheet("color: #2563eb; font-weight: bold; font-size: 11px;")
        debt_title.setAlignment(Qt.AlignCenter)

        # استخدام قيمة الدين الحقيقية المحسوبة من قاعدة البيانات
        total_debt = self.supplier_debt

        self.debt_value = QLabel(f"{total_debt:.2f} ج.م")
        self.debt_value.setStyleSheet("color: #2563eb; font-size: 14px; font-weight: bold;")
        self.debt_value.setAlignment(Qt.AlignCenter)

        debt_layout.addWidget(debt_title)
        debt_layout.addWidget(self.debt_value)

        # تخطيط للفريم الأزرق وزر دفع الدين
        debt_container = QHBoxLayout()
        debt_container.setSpacing(6)  # تقليل المسافة
        debt_container.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش غير الضرورية

        # إضافة زر دفع الدين
        payment_button = QPushButton("💰 دفع للمورد")
        payment_button.setObjectName("action_button")
        payment_button.setCursor(Qt.PointingHandCursor)
        payment_button.setMinimumHeight(60)  # تغيير الارتفاع من 40 إلى 60
        payment_button.setMaximumHeight(60)  # تغيير الارتفاع من 40 إلى 60
        payment_button.setMinimumWidth(100)
        payment_button.clicked.connect(self.pay_supplier)
        payment_button.setStyleSheet("""
            QPushButton#action_button {
                background-color: #3b82f6;
                color: white;
                border: 1px solid #60a5fa;
                border-radius: 4px;
                padding: 4px 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton#action_button:hover {
                background-color: #2563eb;
            }
        """)

        # إضافة الفريم الأزرق وزر الدفع إلى الحاوية
        debt_container.addWidget(debt_frame)
        debt_container.addWidget(payment_button)

        # إضافة زر الإغلاق في نفس الصف مع زر دفع الدين ولكن في أقصى اليسار
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.close)
        close_button.setMinimumWidth(100)
        close_button.setMinimumHeight(60)  # تغيير الارتفاع من 40 إلى 60
        close_button.setMaximumHeight(60)  # تغيير الارتفاع من 40 إلى 60
        close_button.setObjectName("action_button")  # تغيير من secondary_button إلى action_button
        close_button.setCursor(Qt.PointingHandCursor)
        close_button.setStyleSheet("""
            QPushButton#action_button {
                background-color: #ef4444;
                color: white;
                border: 1px solid #f87171;
                border-radius: 4px;
                padding: 4px 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton#action_button:hover {
                background-color: #dc2626;
            }
        """)

        # إنشاء تخطيط للإطارات المالية
        balance_info_layout = QHBoxLayout()
        balance_info_layout.setSpacing(10)  # تقليل المسافة
        balance_info_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش

        # تجميع العناصر في أقصى اليمين واليسار
        balance_info_layout.addLayout(debt_container)  # الفريم الأزرق وزر الدفع في أقصى اليمين
        balance_info_layout.addStretch(1)  # مساحة مرنة (لدفع زر الإغلاق إلى اليسار)

        # إضافة زر لعرض سجل المدفوعات لهذا المورد
        show_payments_history_btn = QPushButton("📋 سجل المدفوعات")
        show_payments_history_btn.setObjectName("action_button")  # تغيير من secondary_button إلى action_button
        show_payments_history_btn.setCursor(Qt.PointingHandCursor)
        show_payments_history_btn.setMinimumHeight(60)  # تغيير الارتفاع من 40 إلى 60
        show_payments_history_btn.setMaximumHeight(60)  # تغيير الارتفاع من 40 إلى 60
        show_payments_history_btn.setMinimumWidth(120)
        show_payments_history_btn.clicked.connect(self.show_supplier_payments)
        show_payments_history_btn.setStyleSheet("""
            QPushButton#action_button {
                background-color: #0ea5e9;
                color: white;
                border: 1px solid #38bdf8;
                border-radius: 4px;
                padding: 4px 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton#action_button:hover {
                background-color: #0284c7;
            }
        """)

        # إضافة زر لإنشاء فاتورة جديدة
        new_invoice_btn = QPushButton("📝 فاتورة جديدة")
        new_invoice_btn.setObjectName("action_button")  # تغيير من secondary_button إلى action_button
        new_invoice_btn.setCursor(Qt.PointingHandCursor)
        new_invoice_btn.setMinimumHeight(60)  # تغيير الارتفاع من 40 إلى 60
        new_invoice_btn.setMaximumHeight(60)  # تغيير الارتفاع من 40 إلى 60
        new_invoice_btn.setMinimumWidth(120)
        new_invoice_btn.clicked.connect(self.add_new_purchase)
        new_invoice_btn.setStyleSheet("""
            QPushButton#action_button {
                background-color: #22c55e;
                color: white;
                border: 1px solid #4ade80;
                border-radius: 4px;
                padding: 4px 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton#action_button:hover {
                background-color: #16a34a;
            }
        """)

        balance_info_layout.addWidget(show_payments_history_btn)
        balance_info_layout.addWidget(new_invoice_btn)
        balance_info_layout.addWidget(close_button)  # زر الإغلاق في أقصى اليسار

        # إضافة قسم الرصيد إلى التخطيط الرئيسي
        balance_section_layout.addLayout(balance_info_layout)

        # إضافة قسم الرصيد إلى التخطيط الرئيسي
        layout.addWidget(balance_section)
        layout.addSpacing(5)

        # تطبيق الأنماط
        self.apply_styles()
        self.apply_additional_styles()

        # تحديث فوري للبيانات عند فتح النافذة
        # استخدام QTimer لضمان أن النافذة تظهر أولاً ثم يبدأ التحديث
        QTimer.singleShot(100, self.refresh_purchases)
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def apply_additional_styles(self):
        """تطبيق أنماط إضافية خاصة بنافذة تفاصيل المورد"""
        # أنماط إضافية لهذه النافذة لتكون متوافقة مع نافذة تفاصيل العميل
        additional_styles = """
            #field_label {
                color: #3b82f6;  /* أزرق أساسي لجميع تسميات الحقول */
                font-weight: bold;  /* جعل النص أكثر وضوحاً */
            }

            #section_title {
                color: #1e3a8a;  /* أزرق أغمق لعناوين الأقسام */
                font-weight: bold;
            }

            #filter_container {
                padding: 0px;
                margin: 0px;
                background-color: transparent;
            }

            #action_button {
                margin-top: 0px;
                padding-top: 0px;
                padding-bottom: 0px;
                margin-bottom: 0px;
            }

            QTableWidget {
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                background-color: white;
            }

            QTableWidget::item:selected {
                background-color: rgba(59, 130, 246, 0.3);
                color: #0f172a;
            }
        """

        # تطبيق الأنماط الإضافية
        self.setStyleSheet(self.styleSheet() + additional_styles)

    def show_purchases_context_menu(self, position):
        """عرض قائمة السياق عند النقر بزر الماوس الأيمن على جدول المشتريات"""
        # التحقق من وجود صفوف في الجدول
        if self.purchases_table.rowCount() == 0:
            return

        # التحقق من وجود صف محدد
        selected_rows = self.purchases_table.selectedIndexes()
        if not selected_rows:
            return

        # الحصول على الصف المحدد والبيانات ذات الصلة
        row = selected_rows[0].row()
        purchase_id = int(self.purchases_table.item(row, 0).text())
        reference_number = self.purchases_table.item(row, 1).text()

        # إنشاء قائمة السياق
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.RightToLeft)

        # إضافة عنوان الفاتورة
        title_action = QAction(f"فاتورة رقم: {purchase_id}", self)
        title_action.setEnabled(False)
        title_font = title_action.font()
        title_font.setBold(True)
        title_action.setFont(title_font)
        context_menu.addAction(title_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # إضافة إجراءات
        view_action = QAction("🔍  عرض تفاصيل الفاتورة", self)
        view_action.triggered.connect(lambda: self.view_purchase_details(self.purchases_table.model().index(row, 0)))
        context_menu.addAction(view_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # إضافة زر الحذف
        delete_action = QAction("🗑️  حذف الفاتورة", self)
        delete_action.triggered.connect(lambda: self.delete_purchase(purchase_id))
        context_menu.addAction(delete_action)

        # عرض القائمة في موقع المؤشر
        context_menu.exec_(self.purchases_table.viewport().mapToGlobal(position))

    def delete_purchase(self, purchase_id):
        """حذف عملية شراء كاملة"""
        # البحث عن الفاتورة في القائمة للحصول على معلومات إضافية
        purchase = None
        for p in self.purchases:
            if p["id"] == purchase_id:
                purchase = p
                break

        if not purchase:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة المحددة!")
            return

        # عرض رسالة تأكيد قبل الحذف
        reference_number = purchase.get("reference_number", "")
        total_amount = purchase.get("total", 0)

        confirmation = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الفاتورة رقم {purchase_id} (المرجع: {reference_number})؟\n"
            f"المبلغ الإجمالي: {total_amount:.2f} ج.م\n\n"
            "سيتم حذف جميع بيانات الفاتورة وعناصرها بشكل دائم.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirmation == QMessageBox.Yes:
            try:
                # استدعاء نموذج المشتريات لحذف الفاتورة
                from models.purchases import PurchaseModel

                # تنفيذ الحذف
                success = PurchaseModel.delete_purchase(purchase_id)

                if success:
                    # إزالة الفاتورة من القائمة المحلية
                    self.purchases = [p for p in self.purchases if p.get('id') != purchase_id]

                    # تحديث عنوان القسم ليعكس عدد الفواتير الجديد
                    self.purchases_title.setText(f"سجل عمليات الشراء ({len(self.purchases)})")

                    # تحديث الجدول
                    self.populate_purchases_table()

                    # تحديث بيانات المورد (لأن إجمالي المشتريات سيتغير)
                    parent = self.parentWidget()
                    if parent:
                        updated_supplier = parent.get_supplier_by_id(self.supplier["id"])
                        if updated_supplier:
                            self.supplier = updated_supplier
                            # تحديث واجهة المستخدم بالبيانات المحدثة
                            parent.populate_suppliers_table()

                    QMessageBox.information(self, "تم الحذف", f"تم حذف الفاتورة رقم {purchase_id} بنجاح.")
                else:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء محاولة حذف الفاتورة.")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")
                print(f"[ERROR] خطأ في حذف الفاتورة: {str(e)}")

    def refresh_purchases(self):
        """تحديث قائمة المشتريات من قاعدة البيانات - تم تحسين آلية العرض"""
        parent = self.parentWidget()
        if parent:
            supplier_id = self.supplier["id"]
            print(f"[DEBUG] تحديث المشتريات للمورد ذو المعرف: {supplier_id}")

            try:
                # استيراد نموذج المشتريات
                from models.purchases import PurchaseModel

                # جلب جميع المشتريات أولاً
                all_purchases = PurchaseModel.get_all_purchases()

                if all_purchases:
                    print(f"[DEBUG] تم العثور على {len(all_purchases)} فاتورة في النظام")

                    # تصفية المشتريات بحسب معرف المورد بطريقة أكثر مرونة
                    self.purchases = []
                    supplier_id_variations = [
                        supplier_id,           # المعرف كرقم
                        str(supplier_id),      # المعرف كنص
                        int(supplier_id) if str(supplier_id).isdigit() else None  # المعرف كرقم صحيح
                    ]

                    # طباعة جميع معرفات الموردين في الفواتير للتشخيص
                    unique_supplier_ids = set()
                    for p in all_purchases:
                        if p.get('supplier_id') is not None:
                            unique_supplier_ids.add(str(p.get('supplier_id')))
                    print(f"[DEBUG] معرفات الموردين المتوفرة في الفواتير: {unique_supplier_ids}")
                    print(f"[DEBUG] معرف المورد الذي نبحث عنه: {supplier_id_variations}")

                    # تصفية الفواتير التي تنتمي لهذا المورد
                    for purchase in all_purchases:
                        purchase_supplier_id = purchase.get('supplier_id')
                        # مقارنة بجميع الأشكال المحتملة لمعرف المورد
                        if purchase_supplier_id is not None and (
                            purchase_supplier_id in supplier_id_variations or
                            str(purchase_supplier_id) in supplier_id_variations
                        ):
                            self.purchases.append(purchase)
                            print(f"[DEBUG] تمت إضافة الفاتورة رقم {purchase.get('id')} للقائمة (supplier_id={purchase_supplier_id})")

                    # ترتيب الفواتير حسب الأحدث
                    self.purchases.sort(key=lambda x: x.get('id', 0), reverse=True)

                    print(f"[DEBUG] تم العثور على {len(self.purchases)} فاتورة لهذا المورد")
                else:
                    print("[DEBUG] لم يتم العثور على أي فواتير في النظام")
                    self.purchases = []

                # مشكلة محتملة في هيكل جدول الفواتير - محاولة التصحيح
                if not self.purchases:
                    # محاولة استدعاء وظيفة مخصصة للحصول على فواتير المورد مباشرة
                    try:
                        from models.suppliers import SupplierModel
                        direct_purchases = SupplierModel.get_supplier_purchases(supplier_id)
                        if direct_purchases and len(direct_purchases) > 0:
                            self.purchases = direct_purchases
                            print(f"[DEBUG] تم الحصول على {len(self.purchases)} فاتورة مباشرة من نموذج الموردين")
                    except Exception as direct_error:
                        print(f"[DEBUG] فشل الحصول على الفواتير مباشرة: {str(direct_error)}")

            except Exception as e:
                import traceback
                print(f"[DEBUG] خطأ في استرجاع الفواتير: {str(e)}")
                print(traceback.format_exc())
                self.purchases = []

            # تحديث بيانات المورد
            try:
                updated_supplier = parent.get_supplier_by_id(supplier_id)
                if updated_supplier:
                    self.supplier = updated_supplier
            except Exception as e:
                print(f"[DEBUG] خطأ في تحديث بيانات المورد: {str(e)}")

            # تحديث واجهة المستخدم
            self.populate_purchases_table()

            # تحديث عنوان القسم ليعكس عدد الفواتير
            if hasattr(self, 'purchases_title'):
                self.purchases_title.setText(f"سجل عمليات الشراء ({len(self.purchases)})")

            # عرض رسالة تأكيد - eliminado para no mostrar notificación
            message = f"تم تحديث سجل المشتريات. تم العثور على {len(self.purchases)} عملية شراء."
            print(f"[DEBUG] {message}")
            # Se elimina la siguiente línea para evitar mostrar la notificación
            # QMessageBox.information(self, "تم التحديث", message)
            return True
        return False

    def populate_purchases_table(self):
        """ملء جدول عمليات الشراء بالبيانات"""
        # تنظيف الجدول
        self.purchases_table.clearContents()
        self.purchases_table.setRowCount(0)

        if not self.purchases:
            row = self.purchases_table.rowCount()
            self.purchases_table.insertRow(row)
            empty_item = QTableWidgetItem("لا توجد عمليات شراء سابقة")
            empty_item.setTextAlignment(Qt.AlignCenter)
            self.purchases_table.setSpan(row, 0, 1, 5)
            self.purchases_table.setItem(row, 0, empty_item)
            return

        for purchase in self.purchases:
            row = self.purchases_table.rowCount()
            self.purchases_table.insertRow(row)

            # رقم الطلب
            id_item = QTableWidgetItem(str(purchase["id"]))
            id_item.setTextAlignment(Qt.AlignCenter)
            self.purchases_table.setItem(row, 0, id_item)

            # الرقم المرجعي
            reference_item = QTableWidgetItem(purchase.get("reference_number", ""))
            reference_item.setTextAlignment(Qt.AlignCenter)
            self.purchases_table.setItem(row, 1, reference_item)

            # تاريخ عملية الشراء
            date_item = QTableWidgetItem(purchase.get("date", ""))
            date_item.setTextAlignment(Qt.AlignCenter)
            self.purchases_table.setItem(row, 2, date_item)

            # مبلغ عملية الشراء
            amount_item = QTableWidgetItem(f"{purchase.get('total', 0):.2f} ج.م")
            amount_item.setTextAlignment(Qt.AlignCenter)
            self.purchases_table.setItem(row, 3, amount_item)

            # حالة عملية الشراء
            status_item = QTableWidgetItem(purchase.get("status", ""))
            status_item.setTextAlignment(Qt.AlignCenter)

            # تلوين حالة عملية الشراء
            status = purchase.get("status", "")
            if status == "مدفوعة":
                status_item.setForeground(QColor(39, 174, 96))
            elif status == "غير مدفوعة":
                status_item.setForeground(QColor(231, 76, 60))
            elif status == "ملغية":
                status_item.setForeground(QColor(127, 140, 141))

            self.purchases_table.setItem(row, 4, status_item)

    def view_purchase_details(self, index):
        """عرض تفاصيل عملية الشراء عند النقر المزدوج"""
        if not self.purchases:
            return

        row = index.row()
        purchase_id = int(self.purchases_table.item(row, 0).text())

        # الحصول على عناصر عملية الشراء
        parent = self.parentWidget()
        items = parent.get_purchase_items(purchase_id)

        # العثور على عملية الشراء من القائمة
        purchase = None
        for p in self.purchases:
            if p["id"] == purchase_id:
                purchase = p
                break

        if purchase:
            # إنشاء نافذة منبثقة لعرض تفاصيل الفاتورة وعناصرها
            invoice_dialog = PurchaseInvoiceDetailsDialog(self, purchase, items)
            invoice_dialog.exec_()

    def edit_supplier(self):
        """فتح نافذة تعديل بيانات المورد"""
        parent = self.parentWidget()
        if parent:
            parent.edit_supplier(self.supplier["id"])
            # تحديث بيانات المورد في النافذة
            self.supplier = parent.get_supplier_by_id(self.supplier["id"])
            self.close()

    def add_new_purchase(self):
        """إضافة عملية شراء جديدة"""
        parent = self.parentWidget()
        if parent:
            # حفظ معرف المورد لإعادة فتح الحوار
            supplier_id = self.supplier["id"]
            print(f"[DEBUG] Iniciando proceso de añadir compra para proveedor ID: {supplier_id}")

            # استدعاء وظيفة إضافة الشراء
            result = parent.add_purchase_for_supplier(supplier_id)
            print(f"[DEBUG] Resultado de add_purchase_for_supplier: {result}")

            # إذا تمت إضافة الشراء بنجاح، تحديث الحوار
            if result:
                print("[DEBUG] Compra añadida correctamente, actualizando diálogo de detalles del proveedor")

                # تحديث البيانات عن طريق استدعاء وظيفة التحديث
                self.refresh_purchases()

                # عرض رسالة نجاح
                QMessageBox.information(self, "تم بنجاح", "تم إضافة عملية الشراء بنجاح وتحديث البيانات.")

                return True
            else:
                # إذا فشلت إضافة الشراء، نبقي هذا الحوار مفتوحًا
                print("[DEBUG] La compra no se añadió correctamente, manteniendo diálogo actual")
                QMessageBox.warning(self, "تنبيه", "لم تتم إضافة عملية الشراء.")
                return False

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        self.setStyleSheet(AppStyles.get_all_view_styles())

    def pay_supplier(self):
        """دفع مبلغ للمورد لتسوية الدين"""
        # الحصول على إجمالي الدين المستحق للمورد
        total_debt = self.supplier_debt

        if total_debt <= 0:
            QMessageBox.information(
                self,
                "لا يوجد دين",
                "لا يوجد دين مستحق لهذا المورد."
            )
            return

        # إنشاء نافذة حوار لإدخال المبلغ مع تطبيق التنسيقات
        from styles import AppStyles

        # إنشاء QInputDialog وتطبيق التنسيقات عليه
        input_dialog = QInputDialog(self)
        input_dialog.setWindowTitle("دفع للمورد")
        input_dialog.setLabelText(f"أدخل المبلغ المراد دفعه (الحد الأقصى: {total_debt:.2f} ج.م):")
        input_dialog.setInputMode(QInputDialog.DoubleInput)
        input_dialog.setDoubleValue(min(total_debt, 100))
        input_dialog.setDoubleRange(1, total_debt)
        input_dialog.setDoubleDecimals(2)

        # تطبيق التنسيقات على النافذة
        input_dialog.setStyleSheet(AppStyles.get_input_dialog_style())

        # عرض النافذة والحصول على النتيجة
        ok = input_dialog.exec_() == QInputDialog.Accepted
        amount = input_dialog.doubleValue() if ok else 0

        if not ok or amount <= 0:
            return

        # إنشاء نافذة حوار لتأكيد الدفع
        confirm = QMessageBox.question(
            self,
            "تأكيد الدفع",
            f"هل أنت متأكد من دفع مبلغ {amount:.2f} ج.م للمورد {self.supplier['name']}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                # إنشاء جدول سجل المدفوعات إذا لم يكن موجودًا
                db.execute("""
                    CREATE TABLE IF NOT EXISTS supplier_payments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        supplier_id INTEGER NOT NULL,
                        amount REAL NOT NULL,
                        payment_date TEXT NOT NULL,
                        notes TEXT,
                        user_id INTEGER,
                        FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
                    )
                """)

                # الحصول على التاريخ والوقت الحالي
                now = datetime.datetime.now()
                payment_date = now.strftime("%Y/%m/%d %H:%M:%S")

                # إضافة السجل في جدول المدفوعات
                payment_query = """
                    INSERT INTO supplier_payments (supplier_id, amount, payment_date, notes)
                    VALUES (?, ?, ?, ?)
                """
                notes = f"تسديد دين للمورد {self.supplier['name']} بقيمة {amount:.2f} ج.م"
                db.execute(payment_query, (self.supplier['id'], amount, payment_date, notes))

                # تحديث حالة الدفع للفواتير غير المدفوعة
                from models.purchases import PurchaseModel

                # استرجاع الفواتير غير المدفوعة لهذا المورد
                unpaid_purchases_query = """
                    SELECT id, total, paid_amount, remaining_amount
                    FROM purchases
                    WHERE supplier_id = ? AND status = 'غير مدفوعة'
                    ORDER BY date ASC
                """
                unpaid_purchases = db.fetch_all(unpaid_purchases_query, (self.supplier['id'],))

                if not unpaid_purchases:
                    QMessageBox.information(self, "تنبيه", "لا توجد فواتير غير مدفوعة لتطبيق الدفعة عليها.")
                    # رغم عدم وجود فواتير، نقوم بتسجيل المدفوعات في السجل
                    db.commit()
                    QMessageBox.information(
                        self,
                        "تم الدفع",
                        f"تم دفع مبلغ {amount:.2f} ج.م للمورد {self.supplier['name']} بنجاح وتم تسجيله في سجل المدفوعات."
                    )
                    return

                # مبلغ الدفع المتبقي للتوزيع
                remaining_payment = amount

                # تطبيق الدفع على الفواتير بالترتيب (الأقدم أولاً)
                applied_purchases = []

                for purchase in unpaid_purchases:
                    if remaining_payment <= 0:
                        break

                    purchase_id = purchase['id']
                    purchase_remaining = purchase['remaining_amount']

                    # المبلغ الذي سيتم دفعه لهذه الفاتورة
                    payment_for_this_purchase = min(remaining_payment, purchase_remaining)

                    # تحديث الفاتورة
                    new_paid_amount = purchase['paid_amount'] + payment_for_this_purchase
                    new_remaining_amount = purchase['remaining_amount'] - payment_for_this_purchase
                    new_status = "مدفوعة" if new_remaining_amount <= 0 else "غير مدفوعة"

                    update_query = """
                        UPDATE purchases
                        SET paid_amount = ?,
                            remaining_amount = ?,
                            status = ?
                        WHERE id = ?
                    """
                    db.execute(update_query, (new_paid_amount, new_remaining_amount, new_status, purchase_id))

                    # إضافة تفاصيل للملاحظات
                    applied_purchases.append({
                        "id": purchase_id,
                        "amount_paid": payment_for_this_purchase,
                        "new_status": new_status
                    })

                    # تقليل المبلغ المتبقي للدفع
                    remaining_payment -= payment_for_this_purchase

                # تحديث ملاحظات السجل بتفاصيل الفواتير المدفوعة
                if applied_purchases:
                    details = "\n".join([
                        f"- فاتورة رقم {p['id']}: {p['amount_paid']:.2f} ج.م ({p['new_status']})"
                        for p in applied_purchases
                    ])
                    updated_notes = f"{notes}\n\nتفاصيل الدفع:\n{details}"

                    # تحديث ملاحظات السجل
                    update_notes_query = """
                        UPDATE supplier_payments
                        SET notes = ?
                        WHERE supplier_id = ? AND payment_date = ?
                    """
                    db.execute(update_notes_query, (updated_notes, self.supplier['id'], payment_date))

                # حفظ التغييرات في قاعدة البيانات
                db.commit()

                # إعادة تحميل بيانات المورد والمشتريات
                self.refresh_purchases()

                # تحديث قيمة الدين
                self.supplier_debt = SupplierModel.get_supplier_total_debt(self.supplier['id'])
                self.debt_value.setText(f"{self.supplier_debt:.2f} ج.م")

                QMessageBox.information(
                    self,
                    "تم الدفع",
                    f"تم دفع مبلغ {amount:.2f} ج.م للمورد {self.supplier['name']} بنجاح وتم تسجيله في سجل المدفوعات."
                )

            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "خطأ",
                    f"حدث خطأ أثناء عملية الدفع: {str(e)}"
                )
                print(f"[ERROR] خطأ في دفع مبلغ للمورد: {str(e)}")

    def view_purchase_details(self, index):
        """عرض تفاصيل عملية الشراء عند النقر المزدوج"""
        if not self.purchases:
            return

        row = index.row()
        purchase_id = int(self.purchases_table.item(row, 0).text())

        # الحصول على عناصر عملية الشراء
        parent = self.parentWidget()
        items = parent.get_purchase_items(purchase_id)

        # العثور على عملية الشراء من القائمة
        purchase = None
        for p in self.purchases:
            if p["id"] == purchase_id:
                purchase = p
                break

        if purchase:
            # إنشاء نافذة منبثقة لعرض تفاصيل الفاتورة وعناصرها
            invoice_dialog = PurchaseInvoiceDetailsDialog(self, purchase, items)
            invoice_dialog.exec_()

    def edit_supplier(self):
        """فتح نافذة تعديل بيانات المورد"""
        parent = self.parentWidget()
        if parent:
            parent.edit_supplier(self.supplier["id"])
            # تحديث بيانات المورد في النافذة
            self.supplier = parent.get_supplier_by_id(self.supplier["id"])
            self.close()

    def add_new_purchase(self):
        """إضافة عملية شراء جديدة"""
        parent = self.parentWidget()
        if parent:
            # حفظ معرف المورد لإعادة فتح الحوار
            supplier_id = self.supplier["id"]
            print(f"[DEBUG] Iniciando proceso de añadir compra para proveedor ID: {supplier_id}")

            # استدعاء وظيفة إضافة الشراء
            result = parent.add_purchase_for_supplier(supplier_id)
            print(f"[DEBUG] Resultado de add_purchase_for_supplier: {result}")

            # إذا تمت إضافة الشراء بنجاح، تحديث الحوار
            if result:
                print("[DEBUG] Compra añadida correctamente, actualizando diálogo de detalles del proveedor")

                # تحديث البيانات عن طريق استدعاء وظيفة التحديث
                self.refresh_purchases()

                # عرض رسالة نجاح
                QMessageBox.information(self, "تم بنجاح", "تم إضافة عملية الشراء بنجاح وتحديث البيانات.")

                return True
            else:
                # إذا فشلت إضافة الشراء، نبقي هذا الحوار مفتوحًا
                print("[DEBUG] La compra no se añadió correctamente, manteniendo diálogo actual")
                QMessageBox.warning(self, "تنبيه", "لم تتم إضافة عملية الشراء.")
                return False

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        self.setStyleSheet(AppStyles.get_all_view_styles())

    def show_supplier_payments(self):
        """عرض سجل مدفوعات ديون المورد المحدد"""
        try:
            # التحقق من وجود الجدول أولا
            table_exists = db.fetch_one("SELECT name FROM sqlite_master WHERE type='table' AND name='supplier_payments'")

            if not table_exists:
                # إنشاء الجدول إذا لم يكن موجودًا
                db.execute("""
                    CREATE TABLE IF NOT EXISTS supplier_payments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        supplier_id INTEGER NOT NULL,
                        amount REAL NOT NULL,
                        payment_date TEXT NOT NULL,
                        notes TEXT,
                        user_id INTEGER,
                        FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
                    )
                """)
                db.commit()

                QMessageBox.information(
                    self,
                    "سجل المدفوعات",
                    f"لا توجد مدفوعات مسجلة للمورد {self.supplier['name']} بعد."
                )
                return

            # فتح نافذة عرض سجل المدفوعات مع تمرير معرف المورد
            payments_dialog = SupplierPaymentsHistoryDialog(self, self.supplier["id"])
            payments_dialog.exec_()

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء فتح سجل المدفوعات: {str(e)}"
            )
            print(f"[ERROR] خطأ في فتح سجل المدفوعات: {str(e)}")

# إضافة فئة جديدة لعرض تفاصيل الفاتورة
class PurchaseInvoiceDetailsDialog(QDialog):
    """نافذة عرض تفاصيل فاتورة الشراء وعناصرها"""
    def __init__(self, parent, purchase, items):
        super().__init__(parent)

        self.purchase = purchase
        self.items = items
        self.parent_dialog = parent
        self.modified = False  # متغير للتتبع ما إذا تم تعديل الفاتورة
        purchase_id = purchase.get('id', 'غير معروف')

        self.setWindowTitle(f"تفاصيل فاتورة الشراء رقم {purchase_id}")
        self.setMinimumSize(800, 600)
        self.setLayoutDirection(Qt.RightToLeft)

        # طباعة معلومات تشخيصية
        print(f"[DEBUG] عرض تفاصيل الفاتورة رقم {purchase_id}")
        print(f"[DEBUG] عدد العناصر المستلمة: {len(items) if items else 0}")
        if items:
            for i, item in enumerate(items):
                print(f"[DEBUG] العنصر {i+1}: {item.get('name')} - الكمية: {item.get('quantity')} - الإجمالي: {item.get('total') or item.get('total_price')}")

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)

        # قسم معلومات الفاتورة
        invoice_section = QFrame()
        invoice_section.setObjectName("filter_frame")
        invoice_section.setFrameShape(QFrame.StyledPanel)
        invoice_section_layout = QVBoxLayout(invoice_section)

        # عنوان القسم
        section_title = QLabel("معلومات الفاتورة")
        section_title.setObjectName("section_title")
        section_title.setFont(QFont("Arial", 12, QFont.Bold))
        invoice_section_layout.addWidget(section_title)

        # فاصل أفقي
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("filter_separator")
        invoice_section_layout.addWidget(separator)

        # معلومات الفاتورة
        invoice_info_layout = QGridLayout()
        invoice_info_layout.setSpacing(15)

        # الصف الأول من البيانات
        ref_label = QLabel("الرقم المرجعي:")
        ref_label.setObjectName("field_label")
        ref_value = QLabel(purchase.get("reference_number", ""))
        ref_value.setFont(QFont("Arial", 10, QFont.Bold))

        date_label = QLabel("تاريخ الفاتورة:")
        date_label.setObjectName("field_label")
        date_value = QLabel(purchase.get("date", ""))

        invoice_info_layout.addWidget(ref_label, 0, 0)
        invoice_info_layout.addWidget(ref_value, 0, 1)
        invoice_info_layout.addWidget(date_label, 0, 2)
        invoice_info_layout.addWidget(date_value, 0, 3)

        # الصف الثاني من البيانات
        status_label = QLabel("حالة الفاتورة:")
        status_label.setObjectName("field_label")
        status_value = QLabel(purchase.get("status", ""))

        amount_label = QLabel("إجمالي الفاتورة:")
        amount_label.setObjectName("field_label")
        self.amount_value = QLabel(f"{purchase.get('total', 0):.2f} ج.م")
        self.amount_value.setFont(QFont("Arial", 10, QFont.Bold))

        invoice_info_layout.addWidget(status_label, 1, 0)
        invoice_info_layout.addWidget(status_value, 1, 1)
        invoice_info_layout.addWidget(amount_label, 1, 2)
        invoice_info_layout.addWidget(self.amount_value, 1, 3)

        invoice_section_layout.addLayout(invoice_info_layout)
        layout.addWidget(invoice_section)

        # قسم عناصر الفاتورة
        layout.addSpacing(10)

        items_section = QFrame()
        items_section.setObjectName("filter_frame")
        items_section.setFrameShape(QFrame.StyledPanel)
        items_section_layout = QVBoxLayout(items_section)

        # عنوان قسم العناصر
        items_title = QLabel("عناصر الفاتورة")
        items_title.setObjectName("section_title")
        items_title.setFont(QFont("Arial", 12, QFont.Bold))
        items_section_layout.addWidget(items_title)

        # فاصل أفقي
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.HLine)
        separator2.setFrameShadow(QFrame.Sunken)
        separator2.setObjectName("filter_separator")
        items_section_layout.addWidget(separator2)

        # جدول عناصر الفاتورة
        self.items_table = QTableWidget()
        self.items_table.setObjectName("items_table")
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels(["#", "الباركود", "اسم المنتج", "الكمية", "سعر الوحدة", "خصم", "الإجمالي"])
        self.items_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.items_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.items_table.verticalHeader().setVisible(False)
        self.items_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setAlternatingRowColors(True)

        # إضافة قائمة السياق للجدول
        self.items_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.items_table.customContextMenuRequested.connect(self.show_context_menu)

        # ملء جدول العناصر
        self.populate_items_table()

        items_section_layout.addWidget(self.items_table)

        # إضافة قسم المجاميع
        totals_layout = QHBoxLayout()

        # عدد العناصر
        self.items_count_label = QLabel(f"عدد العناصر: {len(self.items)}")
        self.items_count_label.setObjectName("stats_label")
        totals_layout.addWidget(self.items_count_label)

        totals_layout.addStretch()

        # مجموع الكميات
        total_qty = sum(item.get("quantity", 0) for item in self.items)
        self.total_qty_label = QLabel(f"مجموع الكميات: {total_qty}")
        self.total_qty_label.setObjectName("stats_label")
        totals_layout.addWidget(self.total_qty_label)

        totals_layout.addStretch()

        # الإجمالي
        self.total_amount_label = QLabel(f"إجمالي المبلغ: {purchase.get('total', 0):.2f} ج.م")
        self.total_amount_label.setObjectName("stats_value")
        self.total_amount_label.setFont(QFont("Arial", 10, QFont.Bold))
        totals_layout.addWidget(self.total_amount_label)

        items_section_layout.addLayout(totals_layout)
        layout.addWidget(items_section)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        # زر حفظ التغييرات - يظهر فقط إذا تم التعديل
        self.save_button = QPushButton("💾 حفظ التغييرات")
        self.save_button.setObjectName("primary_button")
        self.save_button.clicked.connect(self.save_changes)
        self.save_button.setVisible(False)  # مخفي بشكل افتراضي

        # زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.setObjectName("action_button")
        close_button.clicked.connect(self.close)
        close_button.setFixedWidth(120)

        buttons_layout.addWidget(self.save_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_button)

        layout.addSpacing(10)
        layout.addLayout(buttons_layout)

        # إضافة تعليمات للمستخدم
        hint_label = QLabel("ملاحظة: يمكنك النقر بزر الماوس الأيمن على أي منتج لعرض خيارات الحذف")
        hint_label.setObjectName("hint_label")
        hint_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(hint_label)

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def show_context_menu(self, position):
        """عرض قائمة السياق عند النقر بزر الماوس الأيمن على جدول العناصر"""
        # التحقق من وجود صفوف في الجدول
        if self.items_table.rowCount() == 0:
            return

        # التحقق من وجود صف محدد
        selected_rows = self.items_table.selectedIndexes()
        if not selected_rows:
            return

        # الحصول على الصف المحدد
        row = selected_rows[0].row()
        index_item = self.items_table.item(row, 0).text()
        product_name = self.items_table.item(row, 2).text()

        # إنشاء قائمة السياق
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.RightToLeft)

        # إضافة عنوان المنتج
        title_action = QAction(f"المنتج: {product_name}", self)
        title_action.setEnabled(False)
        title_font = title_action.font()
        title_font.setBold(True)
        title_action.setFont(title_font)
        context_menu.addAction(title_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # إضافة خيار الحذف
        delete_action = QAction("🗑️ حذف المنتج من الفاتورة", self)
        delete_action.triggered.connect(lambda: self.delete_item(row))
        context_menu.addAction(delete_action)

        # عرض القائمة في موقع المؤشر
        context_menu.exec_(self.items_table.viewport().mapToGlobal(position))

    def delete_item(self, row):
        """حذف عنصر من الفاتورة"""
        product_name = self.items_table.item(row, 2).text()

        # عرض رسالة تأكيد قبل الحذف
        confirmation = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المنتج '{product_name}' من الفاتورة؟\nلن يتم تطبيق التغييرات إلا بعد حفظها.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirmation == QMessageBox.Yes:
            # حذف العنصر من القائمة
            item_index = int(self.items_table.item(row, 0).text()) - 1
            if 0 <= item_index < len(self.items):
                deleted_item = self.items.pop(item_index)
                self.modified = True  # تحديد أن الفاتورة تم تعديلها

                # إعادة تحديث الجدول
                self.populate_items_table()

                # تحديث الإجماليات
                self.update_totals()

                # إظهار زر الحفظ
                self.save_button.setVisible(True)

                QMessageBox.information(
                    self,
                    "تم الحذف",
                    f"تم حذف المنتج '{product_name}' من الفاتورة. انقر على 'حفظ التغييرات' لتطبيق التعديلات."
                )

    def update_totals(self):
        """تحديث إجماليات الفاتورة بعد التعديل"""
        # حساب المجاميع
        total_qty = sum(item.get("quantity", 0) for item in self.items)
        total_amount = 0

        # حساب إجمالي المبلغ من مجموع أسعار العناصر
        for item in self.items:
            # الحصول على الإجمالي من أحد الحقول المتاحة
            for total_field in ["total_price", "total"]:
                if total_field in item and item[total_field] is not None:
                    total_amount += item[total_field]
                    break

        # تحديث العناصر في الواجهة
        self.items_count_label.setText(f"عدد العناصر: {len(self.items)}")
        self.total_qty_label.setText(f"مجموع الكميات: {total_qty}")
        self.total_amount_label.setText(f"إجمالي المبلغ: {total_amount:.2f} ج.م")
        self.amount_value.setText(f"{total_amount:.2f} ج.م")

        # تحديث المبلغ الإجمالي في بيانات الفاتورة
        self.purchase["total"] = total_amount

    def save_changes(self):
        """حفظ التغييرات في قاعدة البيانات"""
        try:
            # استدعاء نموذج المشتريات لتحديث بيانات الفاتورة
            from models.purchases import PurchaseModel

            # تحديث إجمالي الفاتورة
            purchase_id = self.purchase.get('id')
            total_amount = sum(item.get("total_price", item.get("total", 0)) for item in self.items)

            # تحديث الفاتورة
            update_data = {
                "total": total_amount,
                "subtotal": total_amount,
                "paid_amount": total_amount if self.purchase.get("status") == "مدفوعة" else 0,
                "remaining_amount": 0 if self.purchase.get("status") == "مدفوعة" else total_amount
            }

            # تحديث الفاتورة في قاعدة البيانات
            success = PurchaseModel.update_purchase(purchase_id, update_data, self.items)

            if success:
                # تحديث حالة التعديل
                self.modified = False
                self.save_button.setVisible(False)

                # عرض رسالة نجاح
                QMessageBox.information(self, "تم الحفظ", "تم حفظ التغييرات بنجاح.")

                # تحديث جدول المشتريات في النافذة الأم
                if hasattr(self.parent_dialog, 'refresh_purchases'):
                    self.parent_dialog.refresh_purchases()
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء حفظ التغييرات.")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")
            print(f"[ERROR] خطأ في حفظ التغييرات: {str(e)}")

    def populate_items_table(self):
        """ملء جدول عناصر الفاتورة بالبيانات"""
        # تنظيف الجدول
        self.items_table.clearContents()
        self.items_table.setRowCount(0)

        if not self.items:
            row = self.items_table.rowCount()
            self.items_table.insertRow(row)
            empty_item = QTableWidgetItem("لا توجد عناصر في هذه الفاتورة")
            empty_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setSpan(row, 0, 1, 7)
            self.items_table.setItem(row, 0, empty_item)
            print("[DEBUG] لا توجد عناصر لعرضها في الفاتورة")
            return

        print(f"[DEBUG] ملء جدول العناصر بـ {len(self.items)} عنصر")
        for i, item in enumerate(self.items):
            row = self.items_table.rowCount()
            self.items_table.insertRow(row)

            # رقم العنصر
            index_item = QTableWidgetItem(str(i + 1))
            index_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 0, index_item)

            # الباركود
            barcode = item.get("barcode", "") or item.get("product_code", "") or item.get("code", "")
            barcode_item = QTableWidgetItem(barcode)
            barcode_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 1, barcode_item)

            # اسم المنتج
            product_name = item.get("name", "") or item.get("product_name", "")
            name_item = QTableWidgetItem(product_name)
            name_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.items_table.setItem(row, 2, name_item)

            # الكمية
            qty_item = QTableWidgetItem(str(item.get("quantity", 0)))
            qty_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 3, qty_item)

            # سعر الوحدة - تحسين الحصول على السعر من عدة حقول محتملة
            unit_price = 0
            for price_field in ["unit_price", "cost", "price"]:
                if price_field in item and item[price_field] is not None:
                    unit_price = item[price_field]
                    break

            unit_price_item = QTableWidgetItem(f"{unit_price:.2f}")
            unit_price_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 4, unit_price_item)

            # الخصم
            discount = item.get('discount', 0)
            discount_item = QTableWidgetItem(f"{discount:.2f}")
            discount_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 5, discount_item)

            # الإجمالي - تحسين الحصول على الإجمالي من عدة حقول محتملة
            total = 0
            for total_field in ["total_price", "total"]:
                if total_field in item and item[total_field] is not None:
                    total = item[total_field]
                    break

            # إذا لم يتم العثور على الإجمالي، حسابه من السعر والكمية
            if total == 0 and unit_price > 0 and item.get("quantity", 0) > 0:
                total = unit_price * item.get("quantity", 0)

            total_item = QTableWidgetItem(f"{total:.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 6, total_item)

            print(f"[DEBUG] تمت إضافة العنصر {i+1}: {product_name}, الكمية: {item.get('quantity', 0)}, الإجمالي: {total:.2f}")

    def closeEvent(self, event):
        """التعامل مع حدث إغلاق النافذة - التحقق من وجود تغييرات غير محفوظة"""
        if self.modified:
            reply = QMessageBox.question(
                self,
                "تأكيد الإغلاق",
                "هناك تغييرات لم يتم حفظها. هل تريد حفظ التغييرات قبل الإغلاق؟",
                QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel,
                QMessageBox.Save
            )

            if reply == QMessageBox.Save:
                self.save_changes()
                event.accept()
            elif reply == QMessageBox.Discard:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        self.setStyleSheet(AppStyles.get_all_view_styles())

class SupplierPaymentsHistoryDialog(QDialog):
    """نافذة عرض سجل مدفوعات ديون الموردين"""
    def __init__(self, parent=None, supplier_id=None):
        super().__init__(parent)

        self.supplier_id = supplier_id

        # إعداد النافذة
        self.setWindowTitle("سجل مدفوعات ديون الموردين")
        self.setMinimumSize(850, 600)
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة عنوان وفلاتر
        header_layout = QHBoxLayout()

        # عنوان
        title = QLabel("سجل مدفوعات ديون الموردين")
        title.setObjectName("page_title")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        header_layout.addWidget(title)

        # فلتر حسب المورد (إذا لم يتم تحديد مورد)
        if not supplier_id:
            filter_layout = QHBoxLayout()
            filter_label = QLabel("تصفية حسب المورد:")
            filter_label.setObjectName("field_label")

            self.supplier_filter = RTLComboBox()
            self.supplier_filter.setObjectName("combo_box")
            self.supplier_filter.setFixedHeight(36)
            self.supplier_filter.setMinimumWidth(200)

            # إضافة خيار "الكل" ثم الموردين
            self.supplier_filter.addItem("الكل", None)

            # إضافة قائمة الموردين
            suppliers = SupplierModel.get_all_suppliers()
            for supplier in suppliers:
                self.supplier_filter.addItem(supplier["name"], supplier["id"])

            self.supplier_filter.currentIndexChanged.connect(self.filter_payments)

            filter_layout.addWidget(filter_label)
            filter_layout.addWidget(self.supplier_filter)
            filter_layout.addStretch()

            header_layout.addLayout(filter_layout)

        layout.addLayout(header_layout)

        # فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("content_separator")
        layout.addWidget(separator)

        # جدول المدفوعات
        self.payments_table = QTableWidget()
        self.payments_table.setObjectName("payments_table")
        self.payments_table.setColumnCount(6)
        self.payments_table.setHorizontalHeaderLabels(["#", "اسم المورد", "المبلغ", "تاريخ الدفع", "التفاصيل", "المستخدم"])
        self.payments_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.payments_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.payments_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.payments_table.verticalHeader().setVisible(False)
        self.payments_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.payments_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.payments_table.setAlternatingRowColors(True)

        # تطبيق أنماط لتحسين مظهر الجدول
        self.payments_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #0d47a1;
            }
        """)

        layout.addWidget(self.payments_table)

        # إضافة زر لتصدير السجل
        export_button = QPushButton("📥 تصدير السجل")
        export_button.setObjectName("secondary_button")
        export_button.clicked.connect(self.export_payments)
        export_button.setFixedWidth(150)

        # زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.setObjectName("action_button")
        close_button.clicked.connect(self.close)
        close_button.setFixedWidth(120)

        # تخطيط الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.addWidget(export_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_button)

        layout.addLayout(buttons_layout)

        # ملء جدول المدفوعات
        self.load_payments()

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def load_payments(self):
        """تحميل سجل المدفوعات من قاعدة البيانات"""
        try:
            # بناء استعلام SQL لجلب المدفوعات
            query = """
                SELECT sp.id, s.name as supplier_name, sp.supplier_id, sp.amount, sp.payment_date, sp.notes, sp.user_id
                FROM supplier_payments sp
                LEFT JOIN suppliers s ON sp.supplier_id = s.id
            """
            params = []

            # إضافة تصفية حسب المورد إذا تم تحديد مورد
            if self.supplier_id:
                query += " WHERE sp.supplier_id = ?"
                params.append(self.supplier_id)

            # إضافة ترتيب حسب التاريخ (الأحدث أولاً)
            query += " ORDER BY sp.payment_date DESC"

            # تنفيذ الاستعلام
            payments = db.fetch_all(query, params)

            # تنظيف الجدول
            self.payments_table.clearContents()
            self.payments_table.setRowCount(0)

            if not payments:
                row = self.payments_table.rowCount()
                self.payments_table.insertRow(row)
                empty_item = QTableWidgetItem("لا توجد سجلات مدفوعات")
                empty_item.setTextAlignment(Qt.AlignCenter)
                self.payments_table.setSpan(row, 0, 1, 6)
                self.payments_table.setItem(row, 0, empty_item)
                return

            # إضافة البيانات للجدول
            for payment in payments:
                row = self.payments_table.rowCount()
                self.payments_table.insertRow(row)

                # رقم السجل
                id_item = QTableWidgetItem(str(payment["id"]))
                id_item.setTextAlignment(Qt.AlignCenter)
                self.payments_table.setItem(row, 0, id_item)

                # اسم المورد
                supplier_name = payment["supplier_name"] if payment["supplier_name"] else f"مورد #{payment['supplier_id']}"
                supplier_item = QTableWidgetItem(supplier_name)
                self.payments_table.setItem(row, 1, supplier_item)

                # المبلغ
                amount_item = QTableWidgetItem(f"{payment['amount']:.2f} ج.م")
                amount_item.setTextAlignment(Qt.AlignCenter)
                self.payments_table.setItem(row, 2, amount_item)

                # تاريخ الدفع
                date_item = QTableWidgetItem(payment["payment_date"])
                date_item.setTextAlignment(Qt.AlignCenter)
                self.payments_table.setItem(row, 3, date_item)

                # التفاصيل (زر لعرض التفاصيل إذا كانت طويلة)
                if payment["notes"] and len(payment["notes"]) > 30:
                    details_item = QTableWidgetItem("عرض التفاصيل")
                    details_item.setTextAlignment(Qt.AlignCenter)
                    details_item.setForeground(QColor(25, 118, 210))  # لون أزرق
                    details_item.setData(Qt.UserRole, payment["notes"])  # تخزين التفاصيل الكاملة
                else:
                    details_item = QTableWidgetItem(payment["notes"] if payment["notes"] else "")
                self.payments_table.setItem(row, 4, details_item)

                # المستخدم
                user_id = payment.get("user_id")
                if user_id:
                    # هنا يمكن جلب اسم المستخدم من جدول المستخدمين
                    user_item = QTableWidgetItem(f"مستخدم #{user_id}")
                else:
                    user_item = QTableWidgetItem("النظام")
                user_item.setTextAlignment(Qt.AlignCenter)
                self.payments_table.setItem(row, 5, user_item)

            # إضافة معالج النقر المزدوج لعرض التفاصيل
            self.payments_table.doubleClicked.connect(self.show_payment_details)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل سجل المدفوعات: {str(e)}")
            print(f"[ERROR] خطأ في تحميل سجل المدفوعات: {str(e)}")

    def filter_payments(self):
        """تصفية المدفوعات حسب المورد المحدد"""
        if hasattr(self, 'supplier_filter'):
            selected_index = self.supplier_filter.currentIndex()
            self.supplier_id = self.supplier_filter.itemData(selected_index)
            self.load_payments()

    def show_payment_details(self, index):
        """عرض تفاصيل المدفوعات عند النقر المزدوج على الصف"""
        row = index.row()
        col = index.column()

        # التحقق مما إذا كان النقر على عمود التفاصيل
        if col == 4:
            details = self.payments_table.item(row, col).data(Qt.UserRole)
            if details:
                payment_id = self.payments_table.item(row, 0).text()
                supplier_name = self.payments_table.item(row, 1).text()

                # عرض نافذة منبثقة بالتفاصيل
                detail_dialog = QDialog(self)
                detail_dialog.setWindowTitle(f"تفاصيل الدفع رقم {payment_id}")
                detail_dialog.setMinimumSize(500, 300)
                detail_dialog.setLayoutDirection(Qt.RightToLeft)

                dialog_layout = QVBoxLayout(detail_dialog)

                # عنوان
                title = QLabel(f"تفاصيل دفع لـ {supplier_name}")
                title.setObjectName("section_title")
                title.setFont(QFont("Arial", 12, QFont.Bold))
                dialog_layout.addWidget(title)

                # نص التفاصيل
                details_text = QLabel(details)
                details_text.setObjectName("details_text")
                details_text.setWordWrap(True)
                details_text.setTextInteractionFlags(Qt.TextSelectableByMouse)
                details_text.setStyleSheet("""
                    background-color: #f8f9fa;
                    padding: 15px;
                    border: 1px solid #e0e0e0;
                    border-radius: 5px;
                """)

                # إضافة النص إلى تخطيط قابل للتمرير
                scroll_area = QScrollArea()
                scroll_area.setWidgetResizable(True)
                scroll_area.setWidget(details_text)
                dialog_layout.addWidget(scroll_area)

                # زر إغلاق
                close_button = QPushButton("إغلاق")
                close_button.clicked.connect(detail_dialog.close)
                dialog_layout.addWidget(close_button)

                detail_dialog.exec_()

    def export_payments(self):
        """تصدير سجل المدفوعات إلى ملف نصي"""
        try:
            # إنشاء عنوان الملف
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"supplier_payments_{timestamp}.csv"

            # فتح حوار لاختيار مكان حفظ الملف
            from PyQt5.QtWidgets import QFileDialog
            filepath, _ = QFileDialog.getSaveFileName(
                self, "حفظ سجل المدفوعات", filename, "ملفات CSV (*.csv)"
            )

            if not filepath:
                return  # ألغى المستخدم العملية

            # جمع بيانات من الجدول
            data = []
            headers = ["رقم السجل", "اسم المورد", "المبلغ", "تاريخ الدفع", "التفاصيل"]

            # إضافة بيانات الصفوف
            for row in range(self.payments_table.rowCount()):
                row_data = []
                for col in range(5):  # نأخذ أول 5 أعمدة فقط (بدون عمود المستخدم)
                    if col == 4:  # إذا كان عمود التفاصيل
                        details = self.payments_table.item(row, col).data(Qt.UserRole)
                        if details:
                            # تنظيف التفاصيل من أي أحرف قد تسبب مشاكل في CSV
                            details = details.replace("\n", " ").replace(",", ";")
                            row_data.append(details)
                        else:
                            row_data.append(self.payments_table.item(row, col).text())
                    else:
                        row_data.append(self.payments_table.item(row, col).text())

                data.append(row_data)

            # كتابة البيانات إلى ملف CSV
            with open(filepath, "w", encoding="utf-8") as f:
                # كتابة الترويسة
                f.write(",".join(headers) + "\n")

                # كتابة الصفوف
                for row_data in data:
                    f.write(",".join(row_data) + "\n")

            QMessageBox.information(
                self,
                "تم التصدير",
                f"تم تصدير سجل المدفوعات بنجاح إلى:\n{filepath}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في التصدير",
                f"حدث خطأ أثناء تصدير سجل المدفوعات: {str(e)}"
            )
            print(f"[ERROR] خطأ في تصدير سجل المدفوعات: {str(e)}")

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        self.setStyleSheet(AppStyles.get_all_view_styles())

    def add_new_purchase(self):
        """إنشاء عملية شراء جديدة"""
        # Obtener la lista de proveedores directamente de la base de datos
        suppliers = SupplierModel.get_all_suppliers()

        if not suppliers:
            QMessageBox.warning(self, "تنبيه", "لا يوجد موردين مسجلين. يرجى إضافة مورد أولاً.")
            return

        supplier_dialog = SupplierSelectionDialog(self, suppliers)
        if supplier_dialog.exec_() == QDialog.Accepted:
            selected_supplier = supplier_dialog.get_selected_supplier()
            if selected_supplier:
                self.add_purchase_for_supplier(selected_supplier["id"])

    def show_payments_history(self):
        """عرض سجل مدفوعات ديون الموردين"""
        try:
            # التحقق من وجود الجدول أولا
            table_exists = db.fetch_one("SELECT name FROM sqlite_master WHERE type='table' AND name='supplier_payments'")

            if not table_exists:
                # إنشاء الجدول إذا لم يكن موجودًا
                db.execute("""
                    CREATE TABLE IF NOT EXISTS supplier_payments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        supplier_id INTEGER NOT NULL,
                        amount REAL NOT NULL,
                        payment_date TEXT NOT NULL,
                        notes TEXT,
                        user_id INTEGER,
                        FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
                    )
                """)
                db.commit()

                QMessageBox.information(
                    self,
                    "سجل المدفوعات",
                    "تم إنشاء جدول سجل المدفوعات. لا توجد مدفوعات مسجلة بعد."
                )

            # فتح نافذة عرض سجل المدفوعات
            payments_dialog = SupplierPaymentsHistoryDialog(self)
            payments_dialog.exec_()

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء فتح سجل المدفوعات: {str(e)}"
            )
            print(f"[ERROR] خطأ في فتح سجل المدفوعات: {str(e)}")

    def add_purchase_for_supplier(self, supplier_id):
        """إضافة عملية شراء لمورد محدد - تم تحسين آلية إنشاء الفواتير"""
        print(f"[DEBUG] Iniciando proceso de añadir compra para proveedor ID: {supplier_id}")
        supplier = self.get_supplier_by_id(supplier_id)
        if not supplier:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على المورد!")
            return False

        # Crear un diálogo para la nueva compra
        purchase_dialog = PurchaseDialog(self, supplier)
        if purchase_dialog.exec_() == QDialog.Accepted:
            # Obtener datos de la compra
            purchase_data = purchase_dialog.get_purchase_data()
            purchase_items = purchase_dialog.get_purchase_items()

            # Validar que hay ítems en la compra
            if not purchase_items:
                QMessageBox.warning(self, "تنبيه", "لا يمكن إنشاء فاتورة شراء بدون عناصر.")
                return False

            # إنشاء رقم مرجعي فريد للفاتورة الجديدة
            now = datetime.datetime.now()
            unique_reference = f"PO-{now.year}-{now.month:02d}-{now.day:02d}-{now.hour:02d}{now.minute:02d}{now.second:02d}"
            purchase_data["reference_number"] = unique_reference

            # Añadir ID del proveedor - تحويل المعرف إلى رقم صحيح إذا كان نصًا
            try:
                if isinstance(supplier_id, str) and supplier_id.isdigit():
                    supplier_id = int(supplier_id)
                elif not isinstance(supplier_id, int):
                    raise ValueError(f"معرف المورد بتنسيق غير صالح: {supplier_id} من النوع {type(supplier_id).__name__}")

                purchase_data["supplier_id"] = supplier_id
                print(f"[DEBUG] تم تعيين معرف المورد: {supplier_id} (النوع: {type(supplier_id).__name__})")
            except Exception as e:
                print(f"[DEBUG] خطأ أثناء تحويل معرف المورد: {str(e)}")
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء معالجة معرف المورد: {str(e)}")
                return False

            # تسجيل بيانات عملية الشراء للتشخيص
            print(f"[DEBUG] Datos de la compra a guardar: {purchase_data}")
            print(f"[DEBUG] Elementos de la compra a guardar: {purchase_items}")

            # فحص تفاصيل العناصر
            for i, item in enumerate(purchase_items):
                print(f"[DEBUG] Revisando item {i+1}:")
                for key, value in item.items():
                    print(f"   - {key}: {value} (tipo: {type(value).__name__})")

            # Validar los datos necesarios para la compra
            if 'total' not in purchase_data or purchase_data['total'] <= 0:
                print(f"[DEBUG] Error: Total de compra inválido: {purchase_data.get('total')}")
                QMessageBox.warning(self, "تنبيه", "المبلغ الإجمالي للفاتورة غير صالح. يرجى التحقق من العناصر.")
                return False

            # Preparar datos para la base de datos
            print("[DEBUG] Preparando datos de items para la base de datos...")

            # Verificar el método de pago
            payment_method = purchase_data.get('payment_method', 'نقداً')
            paid_amount = purchase_data.get('paid_amount', 0)
            total_amount = purchase_data.get('total', 0)

            # Asegurar que los montos sean coherentes
            if payment_method == 'نقداً' and paid_amount < total_amount:
                print(f"[DEBUG] Advertencia: Pago en efectivo pero monto pagado ({paid_amount}) menor que total ({total_amount})")

            if payment_method == 'آجل' and paid_amount > 0:
                print(f"[DEBUG] Nota: Pago aplazado con anticipo de {paid_amount}")

            # Intentar añadir la compra a la base de datos
            print("[DEBUG] Intentando añadir la compra a la base de datos...")
            from models.purchases import PurchaseModel

            try:
                purchase_id = PurchaseModel.add_purchase(purchase_data, purchase_items)
                print(f"[DEBUG] Resultado de add_purchase: {purchase_id}")

                if not purchase_id:
                    print("[DEBUG] ERROR: No se pudo obtener el ID de la compra creada. Verifique la función add_purchase.")
                    QMessageBox.critical(self, "خطأ", "فشلت عملية إضافة الشراء: لم يتم الحصول على معرف الفاتورة.")
                    return False

                # Verificar que la compra se guardó correctamente
                purchase_verificado = PurchaseModel.get_purchase_by_id(purchase_id)

                if not purchase_verificado:
                    print(f"[DEBUG] ERROR: La compra con ID {purchase_id} no se puede recuperar de la base de datos.")
                    QMessageBox.warning(self, "تنبيه", "تم إنشاء الفاتورة ولكن قد تكون هناك مشكلة. يرجى التحقق من قائمة المشتريات.")
                else:
                    print(f"[DEBUG] Compra verificada correctamente. Datos: {purchase_verificado}")
                    QMessageBox.information(self, "تم بنجاح", "تم إضافة عملية الشراء بنجاح.")

                # Actualizar la vista
                self.refresh_page()
                return True

            except Exception as e:
                import traceback
                print("[DEBUG] Error al crear la compra:")
                print(traceback.format_exc())
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة عملية الشراء: {str(e)}")
                return False

        # Si el diálogo fue cancelado
        print("[DEBUG] La compra no se añadió correctamente, manteniendo diálogo actual")
        return False

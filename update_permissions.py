"""
سكريبت لتحديث الصلاحيات في جميع الملفات ذات الصلة
"""

import os
import re
import json

# الصلاحيات الجديدة المراد إضافتها
NEW_PERMISSIONS = {
    "العملاء": ["تسجيل دفعة من عميل"],
    "الموردين": ["تسجيل دفعة لمورد"],
    "الفواتير": ["تعديل تصميم الفاتورة"],
    "المدفوعات": ["عرض سجل المدفوعات"]
}

def update_permissions_in_file(file_path):
    """تحديث الصلاحيات في ملف محدد"""
    if not os.path.exists(file_path):
        print(f"الملف {file_path} غير موجود")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # تحديث الصلاحيات في الملف
    for category, permissions in NEW_PERMISSIONS.items():
        for permission in permissions:
            # البحث عن فئة الصلاحيات
            category_pattern = fr'"{category}":\s*\[(.*?)\]'
            category_match = re.search(category_pattern, content, re.DOTALL)
            
            if category_match:
                # التحقق من عدم وجود الصلاحية بالفعل
                if permission not in category_match.group(1):
                    # إضافة الصلاحية الجديدة
                    new_permissions = category_match.group(1).rstrip() + f',\n                    "{permission}"'
                    content = content.replace(category_match.group(1), new_permissions)
            else:
                # إذا لم تكن الفئة موجودة، أضفها بعد آخر فئة
                last_category_pattern = r'(.*?"[^"]+?":\s*\[.*?\]\s*})'
                last_category_match = re.search(last_category_pattern, content, re.DOTALL)
                
                if last_category_match:
                    new_category = f'}},\n                "{category}": [\n                    "{permission}"\n                ]'
                    content = content.replace(last_category_match.group(1), last_category_match.group(1).rstrip('}') + new_category)
    
    # كتابة المحتوى المحدث إلى الملف
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(content)
    
    print(f"تم تحديث الصلاحيات في الملف {file_path}")
    return True

def update_all_permissions():
    """تحديث الصلاحيات في جميع الملفات ذات الصلة"""
    files_to_update = [
        'views/settings.py',
        'models/users.py'
    ]
    
    for file_path in files_to_update:
        update_permissions_in_file(file_path)

if __name__ == "__main__":
    update_all_permissions()

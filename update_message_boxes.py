#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت لتحديث جميع استخدامات QMessageBox في المشروع
واستبدالها بالفئة المخصصة CustomMessageBox
"""

import os
import re
import sys

def find_python_files(directory):
    """البحث عن جميع ملفات Python في المجلد المحدد وجميع المجلدات الفرعية"""
    python_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py') or file.endswith('.pyw'):
                python_files.append(os.path.join(root, file))
    return python_files

def update_imports(file_path):
    """تحديث استيرادات الملف لإضافة CustomMessageBox"""
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()

    # التحقق من وجود QMessageBox في الملف
    if 'QMessageBox' not in content:
        return False, content

    # التحقق من وجود استيراد CustomMessageBox
    if 'from utils.custom_widgets import' in content:
        # إذا كان الاستيراد موجودًا، تأكد من وجود الدوال المطلوبة
        import_pattern = r'from utils\.custom_widgets import (.*)'
        match = re.search(import_pattern, content)
        if match:
            imported_items = match.group(1)
            needed_items = []

            if 'show_information' not in imported_items and 'QMessageBox.information' in content:
                needed_items.append('show_information')

            if 'show_warning' not in imported_items and 'QMessageBox.warning' in content:
                needed_items.append('show_warning')

            if 'show_error' not in imported_items and 'QMessageBox.critical' in content:
                needed_items.append('show_error')

            if 'show_question' not in imported_items and 'QMessageBox.question' in content:
                needed_items.append('show_question')

            if needed_items:
                new_import = f'from utils.custom_widgets import {imported_items.strip()}, {", ".join(needed_items)}'
                content = content.replace(match.group(0), new_import)
                return True, content

        return False, content

    # إضافة استيراد جديد
    needed_items = []

    if 'QMessageBox.information' in content:
        needed_items.append('show_information')

    if 'QMessageBox.warning' in content:
        needed_items.append('show_warning')

    if 'QMessageBox.critical' in content:
        needed_items.append('show_error')

    if 'QMessageBox.question' in content:
        needed_items.append('show_question')

    if not needed_items:
        return False, content

    # البحث عن نمط استيراد PyQt5.QtWidgets
    widgets_import_pattern = r'from PyQt5\.QtWidgets import \(([\s\S]*?)\)'
    match = re.search(widgets_import_pattern, content)

    if match:
        # إزالة QMessageBox من استيرادات QtWidgets
        widgets_imports = match.group(1)
        if 'QMessageBox' in widgets_imports:
            widgets_imports = re.sub(r',\s*QMessageBox', '', widgets_imports)
            widgets_imports = re.sub(r'QMessageBox,\s*', '', widgets_imports)
            new_widgets_import = f'from PyQt5.QtWidgets import ({widgets_imports})'
            content = content.replace(match.group(0), new_widgets_import)

    # إضافة استيراد CustomMessageBox بعد استيرادات PyQt5
    import_line = f'from utils.custom_widgets import {", ".join(needed_items)}'

    # البحث عن آخر استيراد من PyQt5
    last_pyqt_import = re.search(r'from PyQt5\.[a-zA-Z.]+ import .*$', content, re.MULTILINE)
    if last_pyqt_import:
        # إضافة الاستيراد بعد آخر استيراد من PyQt5
        content = content.replace(last_pyqt_import.group(0), f'{last_pyqt_import.group(0)}\n{import_line}')
    else:
        # إضافة الاستيراد في بداية الملف
        content = f'{import_line}\n{content}'

    return True, content

def update_message_boxes(file_path):
    """تحديث استخدامات QMessageBox في الملف"""
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()

    # استبدال QMessageBox.information
    content = re.sub(
        r'QMessageBox\.information\(([^,]+),\s*([^,]+),\s*([^)]+)\)',
        r'show_information(\1, \2, \3)',
        content
    )

    # استبدال QMessageBox.warning
    content = re.sub(
        r'QMessageBox\.warning\(([^,]+),\s*([^,]+),\s*([^)]+)\)',
        r'show_warning(\1, \2, \3)',
        content
    )

    # استبدال QMessageBox.critical
    content = re.sub(
        r'QMessageBox\.critical\(([^,]+),\s*([^,]+),\s*([^)]+)\)',
        r'show_error(\1, \2, \3)',
        content
    )

    # استبدال QMessageBox.question - هذا أكثر تعقيدًا بسبب المعلمات الإضافية
    question_pattern = r'QMessageBox\.question\(([^,]+),\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*([^)]+)\)'

    def replace_question(match):
        parent = match.group(1)
        title = match.group(2)
        text = match.group(3)
        buttons = match.group(4)
        default = match.group(5)
        return f'show_question({parent}, {title}, {text}, {buttons}, {default})'

    content = re.sub(question_pattern, replace_question, content)

    return content

def process_file(file_path):
    """معالجة ملف واحد لتحديث استخدامات QMessageBox"""
    # تحديث الاستيرادات
    imports_updated, content = update_imports(file_path)

    if not imports_updated:
        return

    # تحديث استخدامات QMessageBox
    content = update_message_boxes(content)

    # حفظ التغييرات
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(content)

def main():
    """الدالة الرئيسية للسكريبت"""
    # التأكد من وجود مجلد utils
    if not os.path.exists('utils'):
        return

    # التأكد من وجود ملف custom_widgets.py
    if not os.path.exists('utils/custom_widgets.py'):
        return

    # البحث عن جميع ملفات Python في المشروع
    python_files = find_python_files('.')

    # استثناء ملف السكريبت الحالي وملف custom_widgets.py
    python_files = [f for f in python_files if f != os.path.abspath(__file__) and 'custom_widgets.py' not in f]

    # معالجة كل ملف
    for file_path in python_files:
        process_file(file_path)

if __name__ == "__main__":
    main()

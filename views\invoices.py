from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QFrame,
                             QTableWidget, QTableWidgetItem, QHeaderView, QComboBox, QLineEdit,
                             QDateEdit, QFormLayout, QDialog, QMessageBox, QMenu, QAction,
                             QAbstractItemView, QDialogButtonBox, QSplitter, QInputDialog, QTabWidget,
                             QScrollArea, QGroupBox)
from PyQt5.QtPrintSupport import QPrintDialog, QPrinter
from PyQt5.QtCore import Qt, QDate, QSize, QRect, QTimer
from PyQt5.QtGui import QFont, QIcon, QColor, QPainter, QPageSize, QTextDocument, QTextCursor, QTextTableFormat

# Define RTL helper classes directly
class RTLComboBox(QComboBox):
    """Replacement class for RTLComboBox"""
    pass

def apply_rtl_to_all_widgets(widget):
    """Replacement function for apply_rtl_to_all_widgets"""
    pass

from styles import AppStyles
from models.invoices import InvoiceModel
from models.customers import CustomerModel
from models.products import ProductModel
import datetime
from utils.date_utils import DateTimeUtils

class InvoicesView(QWidget):
    def __init__(self):
        super().__init__()

        # إعداد التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)

        # إضافة عنوان الصفحة
        header_layout = QHBoxLayout()
        page_title = QLabel("الفواتير")
        page_title.setObjectName("page_title")
        page_title.setFont(QFont("Arial", 20, QFont.Bold))
        header_layout.addWidget(page_title)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("content_separator")
        layout.addWidget(separator)
        layout.addSpacing(20)

        # إضافة فلاتر البحث
        filter_layout = QHBoxLayout()

        # البحث النصي
        search_label = QLabel("بحث:")
        search_label.setObjectName("field_label")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث برقم الفاتورة أو اسم العميل")
        self.search_input.setObjectName("search_input")
        self.search_input.textChanged.connect(self.filter_invoices)

        # فلتر التاريخ (باستخدام ComboBox بدلاً من DateEdit)
        period_label = QLabel("الفترة:")
        period_label.setObjectName("field_label")

        self.period_combo = RTLComboBox()
        self.period_combo.setObjectName("combo_box")
        self.period_combo.addItems([
            "اليوم",
            "أمس",
            "آخر 7 أيام",
            "هذا الشهر",
            "آخر 30 يوم",
            "الشهر الماضي",
            "الكل"
        ])
        # تعيين "اليوم" كقيمة افتراضية
        self.period_combo.setCurrentIndex(0)
        self.period_combo.currentIndexChanged.connect(self.filter_invoices)

        # فلتر اسم العميل
        customer_label = QLabel("العميل:")
        customer_label.setObjectName("field_label")
        self.customer_input = QLineEdit()
        self.customer_input.setObjectName("search_input")
        self.customer_input.setPlaceholderText("جميع العملاء")
        self.customer_input.textChanged.connect(self.filter_invoices)

        # فلتر الحالة
        status_label = QLabel("الحالة:")
        status_label.setObjectName("field_label")
        self.status_combo = RTLComboBox()
        self.status_combo.setObjectName("combo_box")
        self.status_combo.addItems(["الكل", "مدفوعة", "غير مدفوعة", "ملغية"])
        self.status_combo.currentIndexChanged.connect(self.filter_invoices)

        # إضافة عناصر الفلتر إلى التخطيط
        filter_layout.addWidget(search_label)
        filter_layout.addWidget(self.search_input)
        filter_layout.addSpacing(15)
        filter_layout.addWidget(period_label)
        filter_layout.addWidget(self.period_combo)
        filter_layout.addSpacing(15)
        filter_layout.addWidget(customer_label)
        filter_layout.addWidget(self.customer_input)
        filter_layout.addSpacing(15)
        filter_layout.addWidget(status_label)
        filter_layout.addWidget(self.status_combo)

        layout.addLayout(filter_layout)
        layout.addSpacing(15)

        # إضافة جدول الفواتير
        self.invoices_table = QTableWidget()
        self.invoices_table.setObjectName("invoices_table")
        self.invoices_table.setColumnCount(7)
        self.invoices_table.setHorizontalHeaderLabels(["رقم الفاتورة", "الرقم المرجعي", "العميل", "التاريخ", "المبلغ", "الحالة", "عدد العناصر"])
        self.invoices_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.invoices_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.invoices_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.invoices_table.verticalHeader().setVisible(False)
        self.invoices_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.invoices_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.invoices_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.invoices_table.setAlternatingRowColors(False)

        # إضافة قائمة السياق
        self.invoices_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.invoices_table.customContextMenuRequested.connect(self.show_context_menu)

        # إضافة النقر المزدوج لعرض تفاصيل الفاتورة
        self.invoices_table.doubleClicked.connect(self.on_table_double_clicked)

        layout.addWidget(self.invoices_table)

        # إضافة إحصائيات الفواتير
        stats_layout = QHBoxLayout()

        # إجمالي الفواتير
        self.total_invoices_label = QLabel("إجمالي الفواتير: 0")
        self.total_invoices_label.setObjectName("stats_label")
        stats_layout.addWidget(self.total_invoices_label)

        stats_layout.addStretch()

        # إجمالي المبالغ
        self.total_amount_label = QLabel("إجمالي المبالغ: 0.00 ج.م")
        self.total_amount_label.setObjectName("stats_label")
        stats_layout.addWidget(self.total_amount_label)

        stats_layout.addSpacing(20)

        # إجمالي المدفوع
        self.total_paid_label = QLabel("إجمالي المدفوع: 0.00 ج.م")
        self.total_paid_label.setObjectName("stats_label")
        self.total_paid_label.setStyleSheet("color: #27ae60; font-weight: bold;")  # لون أخضر للمدفوع
        stats_layout.addWidget(self.total_paid_label)

        stats_layout.addSpacing(20)

        # إجمالي الدين
        self.total_debt_label = QLabel("إجمالي الدين: 0.00 ج.م")
        self.total_debt_label.setObjectName("stats_label")
        self.total_debt_label.setStyleSheet("color: #e74c3c; font-weight: bold;")  # لون أحمر للدين
        stats_layout.addWidget(self.total_debt_label)

        layout.addLayout(stats_layout)

        # إضافة تعليمات للمستخدم
        hint_label = QLabel("ملاحظة: يمكنك النقر مرتين على أي فاتورة لعرض تفاصيلها، أو النقر بزر الماوس الأيمن للوصول إلى قائمة الخيارات")
        hint_label.setObjectName("hint_label")
        layout.addWidget(hint_label)

        # ملء جدول الفواتير بالبيانات
        self.populate_invoices_table()

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def show_context_menu(self, position):
        """عرض قائمة السياق عند النقر بزر الماوس الأيمن"""
        # التحقق من وجود صف محدد
        selected_indexes = self.invoices_table.selectedIndexes()
        if not selected_indexes:
            return

        # الحصول على صف الفاتورة المحددة
        row = selected_indexes[0].row()
        invoice_id = int(self.invoices_table.item(row, 0).text())

        # التحقق من صلاحية المستخدم
        main_window = None
        parent = self.parent()
        user_id = None

        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')

        # استيراد وحدة التحكم بالمستخدمين
        from controllers.user_controller import UserController

        # إنشاء قائمة السياق
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.RightToLeft)

        # إضافة عنوان الفاتورة
        title_action = QAction(f"فاتورة رقم: {invoice_id}", self)
        title_action.setEnabled(False)
        title_font = title_action.font()
        title_font.setBold(True)
        title_action.setFont(title_font)
        context_menu.addAction(title_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # إضافة إجراءات - عرض تفاصيل الفاتورة متاح للجميع
        view_action = QAction("🔍  عرض تفاصيل الفاتورة", self)
        view_action.triggered.connect(lambda: self.view_invoice_details(invoice_id))
        context_menu.addAction(view_action)

        # طباعة الفاتورة - يتطلب صلاحية
        if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "طباعة فاتورة"):
            print_action = QAction("🖨️  طباعة الفاتورة", self)
            print_action.triggered.connect(lambda: self.print_invoice(invoice_id))
            context_menu.addAction(print_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # الإجراءات المتعلقة بحالة الفاتورة
        invoice_status = self.invoices_table.item(row, 5).text()

        # تعليم الفاتورة كمدفوعة - يتطلب صلاحية
        if invoice_status == "غير مدفوعة":
            if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "تعديل فاتورة"):
                mark_paid_action = QAction("✅  تعليم كمدفوعة", self)
                mark_paid_action.triggered.connect(lambda: self.mark_invoice_as_paid(invoice_id))
                context_menu.addAction(mark_paid_action)

        # إلغاء الفاتورة - يتطلب صلاحية
        if invoice_status != "ملغية":
            if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "إلغاء فاتورة"):
                cancel_action = QAction("❌  إلغاء الفاتورة", self)
                cancel_action.triggered.connect(lambda: self.cancel_invoice(invoice_id))
                context_menu.addAction(cancel_action)

        # عرض القائمة في موقع النقر
        context_menu.exec_(self.invoices_table.mapToGlobal(position))

    def on_table_double_clicked(self, index):
        """فتح نافذة تفاصيل الفاتورة عند النقر المزدوج على صف في الجدول"""
        # الحصول على معرف الفاتورة من الصف
        invoice_id = int(self.invoices_table.item(index.row(), 0).text())

        # عرض تفاصيل الفاتورة
        self.view_invoice_details(invoice_id)

    def populate_invoices_table(self):
        """ملء جدول الفواتير بالبيانات مع تطبيق الفلتر الافتراضي"""
        # استدعاء دالة التصفية بدلاً من جلب جميع الفواتير
        # هذا سيضمن تطبيق الفلتر الافتراضي "اليوم" عند التحميل الأولي
        self.filter_invoices()

    def filter_invoices(self):
        """تصفية الفواتير حسب معايير البحث"""
        # الحصول على قيم التصفية
        search_text = self.search_input.text()
        period = self.period_combo.currentText()
        customer_text = self.customer_input.text()
        selected_status = self.status_combo.currentText()

        if selected_status == "الكل":
            selected_status = None

        # تحديد تواريخ البداية والنهاية بناءً على الفترة المحددة
        today = QDate.currentDate()
        from_date = today
        to_date = today

        if period == "اليوم":
            from_date = today
            to_date = today
        elif period == "أمس":
            from_date = today.addDays(-1)
            to_date = today.addDays(-1)
        elif period == "آخر 7 أيام":
            from_date = today.addDays(-7)
            to_date = today
        elif period == "آخر 30 يوم":
            from_date = today.addDays(-30)
            to_date = today
        elif period == "هذا الشهر":
            from_date = QDate(today.year(), today.month(), 1)
            to_date = today
        elif period == "الشهر الماضي":
            prev_month = today.month() - 1
            prev_year = today.year()
            if prev_month == 0:
                prev_month = 12
                prev_year -= 1
            from_date = QDate(prev_year, prev_month, 1)
            if prev_month == 12:
                to_date = QDate(prev_year, prev_month, 31)
            else:
                to_date = QDate(prev_year, prev_month + 1, 1).addDays(-1)
        elif period == "الكل":
            from_date = QDate(2000, 1, 1)  # تاريخ قديم جدًا للحصول على كل البيانات
            to_date = today

        # تحويل التواريخ إلى سلاسل نصية
        from_date_str = from_date.toString("yyyy/MM/dd")
        to_date_str = to_date.toString("yyyy/MM/dd")

        # طلب الفواتير المصفاة من قاعدة البيانات
        filtered_invoices = InvoiceModel.search_invoices(
            search_text=search_text if search_text else None,
            start_date=from_date_str,
            end_date=to_date_str,
            status=selected_status,
            customer_id=None  # لا يتم تصفية العميل بالمعرف هنا بل بالنص
        )

        self.invoices_table.setRowCount(0)

        if not filtered_invoices:
            # لا توجد فواتير تطابق معايير البحث
            self.update_invoice_stats(0, 0, 0, 0)
            return

        # تصفية إضافية حسب اسم العميل إذا تم تحديده
        if customer_text:
            filtered_invoices = [
                inv for inv in filtered_invoices
                if inv["customer_name"] and customer_text.lower() in inv["customer_name"].lower()
            ]

        # تحديث عدد الصفوف
        self.invoices_table.setRowCount(len(filtered_invoices))

        # متغيرات لحساب الإجماليات
        total_amount = 0
        total_paid = 0
        total_debt = 0

        # إضافة البيانات إلى الجدول
        for row, invoice in enumerate(filtered_invoices):
            # رقم الفاتورة
            id_item = QTableWidgetItem(str(invoice["id"]))
            id_item.setTextAlignment(Qt.AlignCenter)
            self.invoices_table.setItem(row, 0, id_item)

            # الرقم المرجعي
            ref_item = QTableWidgetItem(invoice["reference_number"])
            ref_item.setTextAlignment(Qt.AlignCenter)
            self.invoices_table.setItem(row, 1, ref_item)

            # اسم العميل
            customer_name = invoice["customer_name"] if invoice["customer_name"] else "عميل غير مسجل"
            customer_item = QTableWidgetItem(customer_name)
            self.invoices_table.setItem(row, 2, customer_item)

            # تاريخ الفاتورة - تنسيق التاريخ والوقت للعرض
            date_str = invoice["date"] if invoice["date"] else "-"
            # طباعة التاريخ الأصلي للتصحيح
            print(f"تاريخ الفاتورة {invoice['id']} (مصفاة): {date_str}")
            date_item = QTableWidgetItem(DateTimeUtils.format_date_for_table(date_str))
            date_item.setTextAlignment(Qt.AlignCenter)
            self.invoices_table.setItem(row, 3, date_item)

            # المبلغ الإجمالي
            amount_item = QTableWidgetItem(f"{invoice['total']:.2f} ج.م")
            amount_item.setTextAlignment(Qt.AlignCenter)
            self.invoices_table.setItem(row, 4, amount_item)

            # حالة الفاتورة
            status_item = QTableWidgetItem(invoice["status"])
            status_item.setTextAlignment(Qt.AlignCenter)

            # تلوين حالة الفاتورة
            if invoice["status"] == "مدفوعة":
                status_item.setForeground(Qt.darkGreen)
            elif invoice["status"] == "غير مدفوعة":
                status_item.setForeground(Qt.darkBlue)
            elif invoice["status"] == "ملغية":
                status_item.setForeground(Qt.darkRed)

            self.invoices_table.setItem(row, 5, status_item)

            # عدد العناصر
            # جلب عدد العناصر من قاعدة البيانات
            items = InvoiceModel.get_invoice_items(invoice["id"])
            items_count = len(items) if items else 0
            items_item = QTableWidgetItem(str(items_count))
            items_item.setTextAlignment(Qt.AlignCenter)
            self.invoices_table.setItem(row, 6, items_item)

            # حساب الإجماليات حسب حالة الفاتورة
            if invoice["status"] == "مدفوعة":
                total_amount += invoice["total"]
                total_paid += invoice["total"]
            elif invoice["status"] == "غير مدفوعة":
                total_amount += invoice["total"]
                total_debt += invoice["total"]
            # الفواتير الملغية لا تُحسب في أي إجمالي

        # تحديث إحصائيات الفواتير
        self.update_invoice_stats(len(filtered_invoices), total_amount, total_paid, total_debt)

    def update_invoice_stats(self, count, total, paid=0, debt=0):
        """تحديث إحصائيات الفواتير"""
        self.total_invoices_label.setText(f"إجمالي الفواتير: {count}")
        self.total_amount_label.setText(f"إجمالي المبالغ: {total:.2f} ج.م")
        self.total_paid_label.setText(f"إجمالي المدفوع: {paid:.2f} ج.م")
        self.total_debt_label.setText(f"إجمالي الدين: {debt:.2f} ج.م")



    def view_invoice_details(self, invoice_id):
        """عرض تفاصيل الفاتورة"""
        # البحث عن الفاتورة المطلوبة من قاعدة البيانات
        invoice = InvoiceModel.get_invoice_by_id(invoice_id)

        if invoice:
            # الحصول على عناصر الفاتورة من قاعدة البيانات
            invoice_items = InvoiceModel.get_invoice_items(invoice_id)

            # إنشاء وعرض نافذة تفاصيل الفاتورة
            details_dialog = InvoiceDetailsDialog(self, invoice, invoice_items)
            details_dialog.exec_()

            # تحديث جدول الفواتير بعد إغلاق النافذة
            self.populate_invoices_table()
        else:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة المطلوبة")

    def print_invoice(self, invoice_id):
        """طباعة الفاتورة"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية طباعة فاتورة
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "طباعة فاتورة", show_message=True, parent_widget=self):
                    return

            # تنفيذ عملية الطباعة
            QMessageBox.information(self, "طباعة الفاتورة", f"سيتم طباعة الفاتورة رقم {invoice_id}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")

    def mark_invoice_as_paid(self, invoice_id):
        """تعليم الفاتورة كمدفوعة"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية تعديل فاتورة
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "تعديل فاتورة", show_message=True, parent_widget=self):
                    return

            if self.update_invoice_status(invoice_id, "مدفوعة"):
                QMessageBox.information(self, "تم التحديث", f"تم تعليم الفاتورة رقم {invoice_id} كمدفوعة بنجاح.")
                self.populate_invoices_table()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")

    def cancel_invoice(self, invoice_id):
        """إلغاء الفاتورة"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية إلغاء فاتورة
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "إلغاء فاتورة", show_message=True, parent_widget=self):
                    return

            reply = QMessageBox.question(
                self,
                "تأكيد الإلغاء",
                f"هل أنت متأكد من إلغاء الفاتورة رقم {invoice_id}؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                if self.update_invoice_status(invoice_id, "ملغية"):
                    QMessageBox.information(self, "تأكيد العملية", f"تم إلغاء الفاتورة رقم {invoice_id} بنجاح.")
                    self.populate_invoices_table()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")

    def update_invoice_status(self, invoice_id, new_status):
        """تحديث حالة الفاتورة في قاعدة البيانات"""
        invoice = InvoiceModel.get_invoice_by_id(invoice_id)
        if invoice:
            # تحديث حالة الفاتورة
            update_data = {
                "status": new_status
            }
            success = InvoiceModel.update_invoice(invoice_id, update_data)

            if success:
                # تحديث جدول الفواتير
                self.populate_invoices_table()
                return True
            else:
                QMessageBox.critical(self, "خطأ", "فشل تحديث حالة الفاتورة")
                return False
        else:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة المطلوبة")
            return False

    def apply_styles(self):
        """تطبيق الأنماط على العناصر"""
        # استخدام التنسيقات من ملف الستايلات
        self.setStyleSheet(AppStyles.get_all_view_styles())

        # إضافة تنسيقات خاصة
        additional_styles = """
            #search_input, #date_input {
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 8px 12px;
                background-color: white;
            }

            #search_input:hover, #date_input:hover {
                border: 1px solid #64748b;
            }

            #search_input:focus, #date_input:focus {
                border: 1px solid #3b82f6;
            }

            #stats_label {
                color: #64748b;
                font-size: 12px;
                padding: 8px 0;
            }

            #hint_label {
                color: #64748b;
                font-size: 11px;
                font-style: italic;
                padding: 5px 0;
            }

            QTableWidget {
                font-size: 13px;
                background-color: white;
                gridline-color: #e2e8f0;
            }

            QTableWidget::item {
                padding: 8px;
                background-color: #f8fafc;
                border-bottom: 1px solid #f1f5f9;
            }

            QTableWidget::item:selected {
                background-color: rgba(59, 130, 246, 0.15);
                color: #000000;
            }

            QHeaderView::section {
                background-color: #f8fafc;
                color: #475569;
                font-weight: bold;
                border: none;
                padding: 10px;
                border-bottom: 1px solid #cbd5e1;
                border-right: 1px solid #e2e8f0;
            }

            QHeaderView::section:first {
                border-top-right-radius: 6px;
            }

            QHeaderView::section:last {
                border-top-left-radius: 6px;
                border-right: none;
            }

            QMenu {
                background-color: white;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 5px;
            }

            QMenu::item {
                padding: 8px 25px;
                border-radius: 4px;
            }

            QMenu::item:selected {
                background-color: #e2e8f0;
                color: #22c55e;
            }

            QMenu::separator {
                height: 1px;
                background-color: #e2e8f0;
                margin: 5px 0px;
            }
        """

        current_style = self.styleSheet()
        self.setStyleSheet(current_style + additional_styles)

    def refresh_page(self):
        """تحديث بيانات الصفحة عند الانتقال إليها أو عند فتح التطبيق"""
        # تحميل بيانات الفواتير من جديد
        self.populate_invoices_table()

        # تحديث حالة تفعيل الأزرار بناءً على صلاحيات المستخدم
        self.update_buttons_state()

        print("تم تحديث صفحة الفواتير")

    def update_buttons_state(self):
        """تحديث حالة تفعيل الأزرار بناءً على صلاحيات المستخدم"""
        # لا توجد أزرار لتحديث حالتها في تاب الفواتير حالياً
        pass

class InvoiceDetailsDialog(QDialog):
    """نافذة عرض تفاصيل الفاتورة"""

    def __init__(self, parent, invoice, invoice_items):
        super().__init__(parent)
        self.parent = parent
        self.invoice = invoice
        self.invoice_items = invoice_items

        self.setWindowTitle(f"تفاصيل الفاتورة #{invoice['id']}")
        self.setMinimumSize(800, 600)
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إنشاء عنوان الصفحة
        title_label = QLabel(f"تفاصيل الفاتورة #{invoice['id']}")
        title_label.setObjectName("dialog_title")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        layout.addWidget(title_label)

        # إضافة صف معلومات الفاتورة
        info_layout = QHBoxLayout()

        # القسم الأول: العميل والتاريخ
        left_info = QVBoxLayout()
        customer_label = QLabel(f"<b>العميل:</b> {invoice['customer_name'] if invoice['customer_name'] else 'عميل غير مسجل'}")
        customer_label.setObjectName("info_label")
        date_label = QLabel(f"<b>التاريخ:</b> {DateTimeUtils.format_date_for_display(invoice['date'])}")
        date_label.setObjectName("info_label")
        reference_label = QLabel(f"<b>الرقم المرجعي:</b> {invoice['reference_number']}")
        reference_label.setObjectName("info_label")

        left_info.addWidget(customer_label)
        left_info.addWidget(date_label)
        left_info.addWidget(reference_label)
        info_layout.addLayout(left_info)

        info_layout.addStretch()

        # القسم الثاني: الحالة والإجمالي
        right_info = QVBoxLayout()
        status_label = QLabel(f"<b>حالة الفاتورة:</b> {invoice['status']}")
        status_label.setObjectName("info_label")

        # تلوين حالة الفاتورة
        if invoice['status'] == "مدفوعة":
            status_label.setStyleSheet("color: green;")
        elif invoice['status'] == "غير مدفوعة":
            status_label.setStyleSheet("color: blue;")
        elif invoice['status'] == "ملغية":
            status_label.setStyleSheet("color: red;")

        self.total_label = QLabel(f"<b>المبلغ الإجمالي:</b> {invoice['total']:.2f} ج.م")
        self.total_label.setObjectName("info_label")
        self.items_count_label = QLabel(f"<b>عدد العناصر:</b> {len(invoice_items)}")
        self.items_count_label.setObjectName("info_label")

        right_info.addWidget(status_label)
        right_info.addWidget(self.total_label)
        right_info.addWidget(self.items_count_label)
        info_layout.addLayout(right_info)

        layout.addLayout(info_layout)

        # إضافة عنوان قسم العناصر
        items_title = QLabel("عناصر الفاتورة")
        items_title.setObjectName("section_title")
        items_title.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(items_title)

        # إضافة تعليمات للمستخدم
        hint_label = QLabel("ملاحظة: يمكنك النقر بزر الماوس الأيمن على أي عنصر لحذفه من الفاتورة")
        hint_label.setObjectName("hint_label")
        hint_label.setStyleSheet("color: #64748b; font-size: 12px; font-style: italic;")
        layout.addWidget(hint_label)

        # إنشاء جدول لعرض عناصر الفاتورة
        self.items_table = QTableWidget()
        self.items_table.setObjectName("items_table")
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels(["اسم المنتج", "السعر (ج.م)", "الكمية", "الإجمالي (ج.م)", "كود المنتج"])
        self.items_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.items_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.items_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.items_table.verticalHeader().setVisible(False)
        self.items_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.items_table.setAlternatingRowColors(False)

        # إضافة قائمة السياق للجدول
        self.items_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.items_table.customContextMenuRequested.connect(self.show_item_context_menu)

        # إضافة بيانات العناصر إلى الجدول
        self.populate_items_table()

        layout.addWidget(self.items_table)

        # إضافة أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        # زر الطباعة
        print_btn = QPushButton("🖨️  طباعة الفاتورة")
        print_btn.setObjectName("action_button")
        print_btn.setFixedSize(140, 38)
        print_btn.setCursor(Qt.PointingHandCursor)
        print_btn.clicked.connect(self.print_invoice)

        # زر حفظ التغييرات (إذا كانت الفاتورة قابلة للتعديل)
        if invoice['status'] != "ملغية":
            save_btn = QPushButton("💾 حفظ التغييرات")
            save_btn.setObjectName("action_button")
            save_btn.setFixedSize(140, 38)
            save_btn.setCursor(Qt.PointingHandCursor)
            save_btn.clicked.connect(self.save_changes)
            buttons_layout.addWidget(save_btn)

        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setObjectName("secondary_button")
        close_btn.setFixedSize(100, 38)
        close_btn.setCursor(Qt.PointingHandCursor)
        close_btn.clicked.connect(self.accept)

        buttons_layout.addWidget(print_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def show_item_context_menu(self, position):
        """عرض قائمة السياق للعناصر"""
        # التحقق من وجود صف محدد
        selected_indexes = self.items_table.selectedIndexes()
        if not selected_indexes or self.invoice['status'] == "ملغية":
            return

        # إنشاء قائمة السياق
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.RightToLeft)

        # إضافة إجراء الحذف
        row = selected_indexes[0].row()
        product_name = self.items_table.item(row, 0).text()

        # إضافة عنوان العنصر
        title_action = QAction(f"العنصر: {product_name}", self)
        title_action.setEnabled(False)
        title_font = title_action.font()
        title_font.setBold(True)
        title_action.setFont(title_font)
        context_menu.addAction(title_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # إضافة إجراء تعديل السعر
        edit_price_action = QAction("💰 تعديل السعر", self)
        edit_price_action.triggered.connect(lambda: self.edit_item_price(row))
        context_menu.addAction(edit_price_action)

        # إضافة إجراء الحذف (إلغاء عملية البيع)
        delete_action = QAction("❌ حذف", self)
        delete_action.triggered.connect(lambda: self.delete_invoice_item(row))
        context_menu.addAction(delete_action)

        # عرض القائمة في موقع النقر
        context_menu.exec_(self.items_table.mapToGlobal(position))

    def edit_item_price(self, row):
        """تعديل سعر عنصر في الفاتورة"""
        # الحصول على البيانات الحالية للعنصر
        current_price = float(self.items_table.item(row, 1).text())
        quantity = int(self.items_table.item(row, 2).text())
        product_name = self.items_table.item(row, 0).text()

        # إنشاء مربع حوار لتعديل السعر مع تطبيق التنسيقات
        from styles import AppStyles

        # إنشاء QInputDialog وتطبيق التنسيقات عليه
        input_dialog = QInputDialog(self)
        input_dialog.setWindowTitle("تعديل السعر")
        input_dialog.setLabelText(f"أدخل السعر الجديد للمنتج '{product_name}':")
        input_dialog.setInputMode(QInputDialog.DoubleInput)
        input_dialog.setDoubleValue(current_price)
        input_dialog.setDoubleRange(0.01, 999999.99)
        input_dialog.setDoubleDecimals(2)

        # تطبيق التنسيقات على النافذة
        input_dialog.setStyleSheet(AppStyles.get_input_dialog_style())

        # عرض النافذة والحصول على النتيجة
        ok = input_dialog.exec_() == QInputDialog.Accepted
        new_price = input_dialog.doubleValue() if ok else current_price

        if ok and new_price != current_price:
            # تحديث سعر العنصر في القائمة
            self.invoice_items[row]['unit_price'] = new_price
            self.invoice_items[row]['total_price'] = new_price * quantity

            # تحديث الجدول
            price_item = QTableWidgetItem(f"{new_price:.2f}")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 1, price_item)

            total_item = QTableWidgetItem(f"{new_price * quantity:.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 3, total_item)

            # تحديث إجمالي الفاتورة
            self.update_invoice_total()

            # تحديث معلومات الفاتورة
            self.update_invoice_info()

    def delete_invoice_item(self, row):
        """حذف عنصر من الفاتورة (إلغاء عملية البيع)"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = getattr(parent, 'parent', None) if hasattr(parent, 'parent') else None

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية حذف عملية بيع
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "حذف عملية بيع", show_message=True, parent_widget=self):
                    return

            # الحصول على بيانات العنصر المحدد
            product_name = self.invoice_items[row]['product_name']
            product_code = self.invoice_items[row]['product_code']
            quantity = self.invoice_items[row]['quantity']
            total_price = self.invoice_items[row]['total_price']
            customer_name = self.invoice.get('customer_name', 'عميل غير مسجل')

            # عرض رسالة تأكيد
            confirmation = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف المنتج '{product_name}' من الفاتورة؟\n\n"
                f"سيتم إعادة الكمية ({quantity}) إلى المخزون وتحديث حساب العميل '{customer_name}'.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if confirmation == QMessageBox.Yes:
                # استدعاء المحرك لحذف المنتج من الفاتورة وتحديث المخزون وحساب العميل
                from models.invoices import InvoiceModel
                from models.products import ProductModel
                from models.customers import CustomerModel

                # حذف المنتج من الفاتورة في قاعدة البيانات
                success = InvoiceModel.delete_invoice_item(self.invoice['id'], product_code, quantity)

                if success:
                    # إعادة المنتج إلى المخزون
                    ProductModel.update_stock(product_code, quantity, 'add')

                    # تحديث حساب العميل
                    if customer_name != "عميل نقدي" and customer_name != "عميل غير مسجل":
                        CustomerModel.update_account(customer_name, -total_price)

                    # حذف العنصر من القائمة المحلية
                    self.invoice_items.pop(row)

                    # تحديث إجمالي الفاتورة
                    self.invoice['total'] -= total_price

                    # التحقق مما إذا كانت الفاتورة فارغة الآن
                    if len(self.invoice_items) == 0:
                        # حذف الفاتورة بالكامل إذا لم يتبق أي عناصر
                        InvoiceModel.delete_invoice(self.invoice['id'])
                        QMessageBox.information(self, "نجاح", "تم حذف العملية وإلغاء الفاتورة بالكامل بنجاح.")

                        # إغلاق النافذة
                        self.accept()

                        # طلب من الأب تحديث عرض الفواتير
                        if hasattr(self.parent, 'populate_invoices_table'):
                            self.parent.populate_invoices_table()
                    else:
                        # تحديث الجدول
                        self.populate_items_table()

                        # تحديث معلومات الفاتورة
                        self.update_invoice_info()

                        QMessageBox.information(self, "نجاح", "تم حذف العملية بنجاح.")

                        # تحديث تاب العملاء ونافذة تفاصيل العميل
                        self.refresh_customer_related_views()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في حذف العملية. يرجى المحاولة مرة أخرى.")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف العملية: {str(e)}")
            print(f"Error in delete_invoice_item: {str(e)}")

    def refresh_customer_related_views(self):
        """تحديث تاب العملاء ونوافذ تفاصيل العملاء المفتوحة"""
        try:
            # البحث عن النافذة الرئيسية
            main_window = self.window()
            if hasattr(main_window, 'content_widget'):
                # تحديث تاب العملاء
                from views.customers import CustomersView
                for i in range(main_window.content_widget.count()):
                    widget = main_window.content_widget.widget(i)
                    if isinstance(widget, CustomersView):
                        widget.refresh_customers_table()
                        break

            # تحديث جميع نوافذ تفاصيل العملاء المفتوحة
            from PyQt5.QtWidgets import QApplication
            from views.customers import CustomerDetailsDialog
            for widget in QApplication.allWidgets():
                if isinstance(widget, CustomerDetailsDialog) and widget.isVisible():
                    widget.refresh_customer_data()

        except Exception as e:
            print(f"[ERROR] خطأ في تحديث واجهات العملاء: {str(e)}")

    def update_invoice_total(self):
        """تحديث إجمالي الفاتورة"""
        total = sum(item['total_price'] for item in self.invoice_items)
        self.invoice['total'] = total

    def remove_item(self, row):
        """حذف عنصر من الفاتورة"""
        deleted_item = self.invoice_items[row]

        # حذف العنصر من القائمة
        self.invoice_items.pop(row)

        # تحديث إجمالي الفاتورة والقيم
        self.invoice['total'] -= deleted_item['total_price']

        # تحديث الجدول
        self.items_table.removeRow(row)

        # إعادة ترقيم الصفوف في الجدول
        for i in range(self.items_table.rowCount()):
            item = QTableWidgetItem(str(i + 1))
            item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(i, 0, item)

        # تحديث معلومات الفاتورة
        self.update_invoice_info()

    def update_invoice_info(self):
        """تحديث معلومات الفاتورة المعروضة"""
        self.total_label.setText(f"<b>المبلغ الإجمالي:</b> {self.invoice['total']:.2f} ج.م")
        self.items_count_label.setText(f"<b>عدد العناصر:</b> {len(self.invoice_items)}")

    def save_changes(self):
        """حفظ التغييرات على الفاتورة الرئيسية"""
        from models.invoices import InvoiceModel

        # تحديث بيانات الفاتورة في قاعدة البيانات
        try:
            # تحضير بيانات الفاتورة للتحديث
            invoice_data = {
                'total': self.invoice['total'],
                'subtotal': self.invoice['total'],  # نفترض أن الإجمالي الفرعي يساوي الإجمالي
                'tax': 0,  # يمكن تحديثه لاحقاً إذا كانت هناك ضرائب
                'discount': 0,  # يمكن تحديثه لاحقاً إذا كانت هناك خصومات
                'paid_amount': self.invoice.get('paid_amount', self.invoice['total']),
                'remaining_amount': self.invoice.get('remaining_amount', 0),
                'payment_method': self.invoice.get('payment_method', 'نقداً'),
                'status': self.invoice.get('status', 'مدفوعة'),
                'notes': self.invoice.get('notes', '')
            }

            # حفظ التغييرات في قاعدة البيانات
            success = InvoiceModel.update_invoice_with_items(
                self.invoice['id'],
                invoice_data,
                self.invoice_items
            )

            if success:
                # عرض رسالة تأكيد
                QMessageBox.information(self, "حفظ التغييرات", "تم حفظ التغييرات بنجاح.")

                # إغلاق النافذة
                self.accept()

                # طلب من الأب تحديث عرض الفواتير
                if hasattr(self.parent, 'populate_invoices_table'):
                    self.parent.populate_invoices_table()
            else:
                QMessageBox.warning(self, "خطأ في الحفظ", "فشل في حفظ التغييرات. يرجى المحاولة مرة أخرى.")

        except Exception as e:
            # عرض رسالة خطأ
            QMessageBox.critical(
                self,
                "خطأ في الحفظ",
                f"حدث خطأ أثناء محاولة حفظ التغييرات: {str(e)}"
            )

    def populate_items_table(self):
        """ملء جدول العناصر ببيانات عناصر الفاتورة"""
        # تحديث عدد الصفوف
        self.items_table.setRowCount(len(self.invoice_items))

        # إضافة البيانات إلى الجدول
        for row, item in enumerate(self.invoice_items):
            # اسم المنتج
            name_item = QTableWidgetItem(item["product_name"])
            self.items_table.setItem(row, 0, name_item)

            # سعر المنتج
            price_item = QTableWidgetItem(f"{item['unit_price']:.2f}")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 1, price_item)

            # الكمية
            quantity_item = QTableWidgetItem(str(item["quantity"]))
            quantity_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 2, quantity_item)

            # إجمالي السعر
            total_item = QTableWidgetItem(f"{item['total_price']:.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 3, total_item)

            # كود المنتج
            product_code_item = QTableWidgetItem(str(item["product_code"]))
            product_code_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 4, product_code_item)

    def print_invoice(self):
        """طباعة الفاتورة"""
        printer = QPrinter(QPrinter.HighResolution)
        dialog = QPrintDialog(printer, self)

        if dialog.exec_() == QPrintDialog.Accepted:
            QMessageBox.information(self, "طباعة الفاتورة", "تم إرسال الفاتورة إلى الطابعة.")

    def apply_styles(self):
        """تطبيق الأنماط على عناصر النافذة"""
        styles = """
            QDialog {
                background-color: white;
            }

            #info_frame {
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                background-color: #f8fafc;
                padding: 5px;
            }

            #dialog_header {
                color: #0f172a;
                padding: 5px 0;
            }

            #info_separator {
                background-color: #e2e8f0;
                color: #e2e8f0;
                max-height: 1px;
            }

            #info_label {
                color: #334155;
                font-size: 13px;
                padding: 5px 0;
            }

            #section_title {
                color: #0f172a;
                padding: 5px 0;
            }

            #items_table {
                border: 1px solid #e2e8f0;
                border-radius: 6px;
            }

            #items_table::item {
                background-color: #f8fafc;  /* Color de fondo más oscuro para todas las filas */
            }

            #items_table::item:selected {
                background-color: rgba(59, 130, 246, 0.3);
                color: #0f172a;
            }

            #items_table QHeaderView::section {
                background-color: #f1f5f9;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #cbd5e1;
                font-weight: bold;
                color: #475569;
            }

            QMenu {
                background-color: white;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 5px;
            }

            QMenu::item {
                padding: 8px 25px;
                border-radius: 4px;
            }

            QMenu::item:selected {
                background-color: #e2e8f0;
                color: #22c55e;
            }

            QMenu::separator {
                height: 1px;
                background-color: #e2e8f0;
                margin: 5px 0px;
            }
        """

        self.setStyleSheet(styles)
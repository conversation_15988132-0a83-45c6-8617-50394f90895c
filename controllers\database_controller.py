"""
وحدة التحكم بقاعدة البيانات - مسؤولة عن إدارة وتهيئة قاعدة البيانات
"""

from models.database import db
from PyQt5.QtWidgets import QMessageBox
import os
import datetime

class DatabaseController:
    """تحكم في قاعدة البيانات وعملياتها الرئيسية"""

    @staticmethod
    def initialize_database():
        """تهيئة قاعدة البيانات وإنشاء الجداول المطلوبة"""
        try:
            # إنشاء اتصال بقاعدة البيانات
            if not db.connect():
                return False, "فشل الاتصال بقاعدة البيانات"

            # إنشاء الجداول
            db.create_tables()

            # ترحيل البيانات من جدول purchase_invoices إلى جدول purchases إذا لزم الأمر
            try:
                db.migrate_purchase_data()
            except Exception as e:
                error_message = f"خطأ في ترحيل بيانات المشتريات: {str(e)}"
                db.log_error(error_message)
                # لا نوقف عملية التهيئة في حالة فشل الترحيل

            # تحديث هيكل قاعدة البيانات (إضافة حقول جديدة)
            DatabaseController.update_database_schema()

            # تهيئة نظام المستخدمين
            try:
                from controllers.user_controller import UserController
                success, user_message = UserController.initialize_user_system()
                if not success:
                    print(f"تحذير: {user_message}")
            except Exception as e:
                error_message = f"خطأ في تهيئة نظام المستخدمين: {str(e)}"
                print(error_message)
                db.log_error(error_message)
                # لا نوقف عملية التهيئة في حالة فشل تهيئة نظام المستخدمين

            # إنشاء مجلدات النظام
            os.makedirs("backups", exist_ok=True)

            return True, "تم تهيئة قاعدة البيانات بنجاح"
        except Exception as e:
            error_message = f"خطأ في تهيئة قاعدة البيانات: {str(e)}"
            db.log_error(error_message)
            return False, error_message
        finally:
            db.disconnect()

    @staticmethod
    def update_database_schema():
        """تحديث هيكل قاعدة البيانات بإضافة أعمدة جديدة"""
        try:
            # التحقق من وجود عمود product_type في جدول المنتجات
            column_exists = db.fetch_one("""
                SELECT COUNT(*) as count FROM pragma_table_info('products')
                WHERE name = 'product_type'
            """)

            # إذا لم يكن العمود موجودًا، قم بإضافته
            if column_exists and column_exists['count'] == 0:
                db.execute("""
                    ALTER TABLE products
                    ADD COLUMN product_type TEXT DEFAULT 'physical'
                """)
                print("تم إضافة عمود product_type إلى جدول المنتجات")

                # تعيين جميع المنتجات الحالية كمنتجات فعلية (physical)
                db.execute("""
                    UPDATE products SET product_type = 'physical'
                """)
                print("تم تحديث جميع المنتجات الحالية كمنتجات فعلية")

                # حفظ التغييرات
                db.commit()

            # التحقق من بنية جدول المدفوعات وإضافة الأعمدة الناقصة
            from models.payments import PaymentModel
            columns = PaymentModel.check_payments_table_schema()
            print("أعمدة جدول المدفوعات الحالية:", columns)

            # التحقق من وجود عمود payment_date في جدول المدفوعات
            if 'payment_date' not in columns:
                db.execute("""
                    ALTER TABLE payments
                    ADD COLUMN payment_date TEXT
                """)
                print("تم إضافة عمود payment_date إلى جدول المدفوعات")

                # تحديث البيانات الموجودة باستخدام حقل created_at
                db.execute("""
                    UPDATE payments
                    SET payment_date = created_at
                    WHERE payment_date IS NULL
                """)

            # التحقق من وجود عمود payment_method في جدول المدفوعات
            if 'payment_method' not in columns:
                db.execute("""
                    ALTER TABLE payments
                    ADD COLUMN payment_method TEXT DEFAULT 'نقداً'
                """)
                print("تم إضافة عمود payment_method إلى جدول المدفوعات")

                # تحديث جميع السجلات الحالية
                db.execute("""
                    UPDATE payments
                    SET payment_method = 'نقداً'
                    WHERE payment_method IS NULL
                """)

            # التحقق من وجود عمود notes في جدول المدفوعات
            if 'notes' not in columns:
                db.execute("""
                    ALTER TABLE payments
                    ADD COLUMN notes TEXT
                """)
                print("تم إضافة عمود notes إلى جدول المدفوعات")

            # حفظ التغييرات
            db.commit()

            return True
        except Exception as e:
            error_message = f"خطأ في تحديث هيكل قاعدة البيانات: {str(e)}"
            db.log_error(error_message)
            print(error_message)
            return False

    @staticmethod
    def test_connection(db_settings=None):
        """اختبار الاتصال بقاعدة البيانات باستخدام إعدادات محددة"""
        try:
            # تخزين الإعدادات الحالية
            current_settings = {
                "db_type": db.db_type,
                "db_path": db.db_path,
                "db_host": db.db_host,
                "db_port": db.db_port,
                "db_name": db.db_name,
                "db_user": db.db_user,
                "db_password": db.db_password
            }

            # تطبيق الإعدادات الجديدة مؤقتًا إذا تم تحديدها
            if db_settings:
                for key, value in db_settings.items():
                    setattr(db, key, value)

            # اختبار الاتصال
            if db.test_connection():
                result = True
                message = "تم الاتصال بقاعدة البيانات بنجاح"
            else:
                result = False
                message = "فشل الاتصال بقاعدة البيانات"

            # استعادة الإعدادات الأصلية
            if db_settings:
                for key, value in current_settings.items():
                    setattr(db, key, value)

            return result, message
        except Exception as e:
            error_message = f"خطأ في اختبار الاتصال: {str(e)}"
            db.log_error(error_message)

            # استعادة الإعدادات الأصلية
            if db_settings:
                for key, value in current_settings.items():
                    setattr(db, key, value)

            return False, error_message

    @staticmethod
    def save_settings(db_settings):
        """حفظ إعدادات قاعدة البيانات"""
        try:
            # حفظ الإعدادات في QSettings
            if 'db_type' in db_settings:
                db.settings.setValue("db_type", db_settings['db_type'])
            if 'db_path' in db_settings:
                db.settings.setValue("db_path", db_settings['db_path'])

            # تطبيق الإعدادات على كائن قاعدة البيانات
            db.load_settings()

            return True, "تم حفظ الإعدادات بنجاح"
        except Exception as e:
            error_message = f"خطأ في حفظ الإعدادات: {str(e)}"
            db.log_error(error_message)
            return False, error_message

    @staticmethod
    def create_backup(backup_path=None):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            # استخدام المسار المحدد أو إنشاء مسار تلقائي
            if not backup_path:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"backups/backup_{timestamp}.db"

            # إنشاء النسخة الاحتياطية
            result_path = db.backup_database(backup_path)

            if result_path:
                return True, f"تم إنشاء النسخة الاحتياطية بنجاح: {result_path}"
            else:
                return False, "فشل إنشاء النسخة الاحتياطية"
        except Exception as e:
            error_message = f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
            db.log_error(error_message)
            return False, error_message

    @staticmethod
    def restore_backup(backup_path):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                return False, "ملف النسخة الاحتياطية غير موجود"

            # استعادة النسخة الاحتياطية
            result = db.restore_database(backup_path)

            if result:
                return True, "تم استعادة قاعدة البيانات بنجاح"
            else:
                return False, "فشل استعادة قاعدة البيانات"
        except Exception as e:
            error_message = f"خطأ في استعادة النسخة الاحتياطية: {str(e)}"
            db.log_error(error_message)
            return False, error_message

    @staticmethod
    def get_backup_files():
        """الحصول على قائمة ملفات النسخ الاحتياطية المتوفرة"""
        try:
            # التأكد من وجود مجلد النسخ الاحتياطية
            if not os.path.exists("backups"):
                os.makedirs("backups", exist_ok=True)
                return []

            # استخراج جميع ملفات النسخ الاحتياطية (.db)
            backup_files = []
            for filename in os.listdir("backups"):
                if filename.endswith(".db"):
                    file_path = os.path.join("backups", filename)
                    file_size = os.path.getsize(file_path) / (1024 * 1024)  # حجم الملف بالميجابايت
                    file_date = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))

                    backup_files.append({
                        "filename": filename,
                        "path": file_path,
                        "size": file_size,
                        "date": file_date.strftime("%Y-%m-%d %H:%M:%S")
                    })

            # ترتيب الملفات حسب تاريخ التعديل (الأحدث أولاً)
            backup_files.sort(key=lambda x: x["date"], reverse=True)

            return backup_files
        except Exception as e:
            db.log_error(f"خطأ في استخراج ملفات النسخ الاحتياطية: {str(e)}")
            return []

    @staticmethod
    def vacuum_database():
        """إعادة تنظيم قاعدة البيانات SQLite"""
        try:
            result = db.vacuum_database()

            if result:
                return True, "تم تنظيم قاعدة البيانات بنجاح"
            else:
                return False, "فشل تنظيم قاعدة البيانات"
        except Exception as e:
            error_message = f"خطأ في تنظيم قاعدة البيانات: {str(e)}"
            db.log_error(error_message)
            return False, error_message

    @staticmethod
    def reset_database():
        """إعادة تعيين قاعدة البيانات وحذف جميع البيانات"""
        try:
            # إنشاء نسخة احتياطية قبل إعادة التعيين
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"backups/before_reset_{timestamp}.db"
            db.backup_database(backup_path)

            # إعادة تعيين قاعدة البيانات
            result = db.reset_database()

            if result:
                return True, "تم إعادة تعيين قاعدة البيانات بنجاح"
            else:
                return False, "فشل إعادة تعيين قاعدة البيانات"
        except Exception as e:
            error_message = f"خطأ في إعادة تعيين قاعدة البيانات: {str(e)}"
            db.log_error(error_message)
            return False, error_message

    @staticmethod
    def handle_error(parent_widget, error_title, error_message):
        """عرض رسالة خطأ في واجهة المستخدم"""
        QMessageBox.critical(parent_widget, error_title, error_message)

    @staticmethod
    def handle_success(parent_widget, success_title, success_message):
        """عرض رسالة نجاح في واجهة المستخدم"""
        QMessageBox.information(parent_widget, success_title, success_message)
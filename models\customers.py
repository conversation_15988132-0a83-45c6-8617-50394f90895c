"""
وحدة إدارة العملاء - توفر وظائف للتعامل مع بيانات العملاء في قاعدة البيانات
"""

from models.database import db
from utils.date_utils import DateTimeUtils
import datetime

class CustomerModel:
    """نموذج التعامل مع بيانات العملاء"""
    
    @staticmethod
    def get_all_customers():
        """استرجاع جميع العملاء من قاعدة البيانات"""
        query = """
            SELECT id, name, phone, email, address, total_purchases, last_purchase, visit_count
            FROM customers
            ORDER BY name
        """
        return db.fetch_all(query)
    
    @staticmethod
    def get_customer_by_id(customer_id):
        """استرجاع عميل بواسطة المعرف"""
        query = """
            SELECT id, name, phone, email, address, total_purchases, last_purchase, visit_count
            FROM customers
            WHERE id = ?
        """
        return db.fetch_one(query, (customer_id,))

    @staticmethod
    def get_customer_by_phone(phone):
        """استرجاع عميل بواسطة رقم الهاتف"""
        query = """
            SELECT id, name, phone, email, address, total_purchases, last_purchase, visit_count
            FROM customers
            WHERE phone = ?
        """
        return db.fetch_one(query, (phone,))

    @staticmethod
    def get_customer_by_name(name):
        """استرجاع عميل بواسطة الاسم"""
        query = """
            SELECT id, name, phone, email, address, total_purchases, last_purchase, visit_count
            FROM customers
            WHERE name = ?
        """
        return db.fetch_one(query, (name,))
    
    @staticmethod
    def search_customers(search_text, date_filter=None):
        """البحث عن عملاء بواسطة النص وتاريخ آخر زيارة"""
        base_query = """
            SELECT id, name, phone, email, address, total_purchases, last_purchase, visit_count
            FROM customers
            WHERE (name LIKE ? OR phone LIKE ? OR email LIKE ?)
        """
        search_pattern = f"%{search_text}%"
        
        # إضافة فلترة التاريخ إذا تم تحديدها
        if date_filter and date_filter != "الكل":
            current_date = datetime.datetime.now().strftime("%Y/%m/%d")
            
            if date_filter == "آخر أسبوع":
                # الحصول على تاريخ قبل أسبوع
                one_week_ago = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime("%Y/%m/%d")
                query = base_query + " AND (last_purchase >= ? OR last_purchase LIKE ?)"
                # استخدام نمط البحث للتواريخ، مع دعم التاريخ المتبوع بأي وقت
                date_pattern = f"{one_week_ago}%"
                params = (search_pattern, search_pattern, search_pattern, one_week_ago, date_pattern)
                print(f"البحث عن آخر أسبوع: من {one_week_ago} إلى {current_date}")
                
            elif date_filter == "آخر شهر":
                # الحصول على تاريخ قبل شهر
                one_month_ago = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime("%Y/%m/%d")
                query = base_query + " AND (last_purchase >= ? OR last_purchase LIKE ?)"
                date_pattern = f"{one_month_ago}%"
                params = (search_pattern, search_pattern, search_pattern, one_month_ago, date_pattern)
                print(f"البحث عن آخر شهر: من {one_month_ago} إلى {current_date}")
                
            elif date_filter == "آخر 3 أشهر":
                # الحصول على تاريخ قبل 3 أشهر
                three_months_ago = (datetime.datetime.now() - datetime.timedelta(days=90)).strftime("%Y/%m/%d")
                query = base_query + " AND (last_purchase >= ? OR last_purchase LIKE ?)"
                date_pattern = f"{three_months_ago}%"
                params = (search_pattern, search_pattern, search_pattern, three_months_ago, date_pattern)
                print(f"البحث عن آخر 3 أشهر: من {three_months_ago} إلى {current_date}")
                
            elif date_filter == "آخر سنة":
                # الحصول على تاريخ قبل سنة
                one_year_ago = (datetime.datetime.now() - datetime.timedelta(days=365)).strftime("%Y/%m/%d")
                query = base_query + " AND (last_purchase >= ? OR last_purchase LIKE ?)"
                date_pattern = f"{one_year_ago}%"
                params = (search_pattern, search_pattern, search_pattern, one_year_ago, date_pattern)
                print(f"البحث عن آخر سنة: من {one_year_ago} إلى {current_date}")
                
            else:
                query = base_query
                params = (search_pattern, search_pattern, search_pattern)
        else:
            query = base_query
            params = (search_pattern, search_pattern, search_pattern)
        
        query += " ORDER BY name"
        results = db.fetch_all(query, params)
        print(f"تم العثور على {len(results)} عميل")
        return results
    
    @staticmethod
    def add_customer(customer_data):
        """إضافة عميل جديد إلى قاعدة البيانات"""
        query = """
            INSERT INTO customers (
                name, phone, email, address, total_purchases, last_purchase, visit_count
            )
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            customer_data.get('name'),
            customer_data.get('phone'),
            customer_data.get('email'),
            customer_data.get('address'),
            customer_data.get('total_purchases', 0),
            customer_data.get('last_purchase', ''),
            customer_data.get('visit_count', 0)
        )
        
        success = db.execute(query, params)
        if success:
            db.commit()
            return db.get_last_insert_id()
        return None
    
    @staticmethod
    def update_customer(customer_id, customer_data):
        """تحديث بيانات عميل موجود"""
        query = """
            UPDATE customers
            SET name = ?,
                phone = ?,
                email = ?,
                address = ?,
                updated_at = ?
            WHERE id = ?
        """
        params = (
            customer_data.get('name'),
            customer_data.get('phone'),
            customer_data.get('email'),
            customer_data.get('address'),
            datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            customer_id
        )
        
        success = db.execute(query, params)
        if success:
            db.commit()
            return True
        return False
    
    @staticmethod
    def delete_customer(customer_id):
        """حذف عميل من قاعدة البيانات"""
        # حذف العميل والتأكد من عدم وجود فواتير مرتبطة
        try:
            # البدء بمعاملة قاعدة بيانات
            if not db.conn:
                db.connect()
            
            # تحديث الفواتير لتكون غير مرتبطة بعميل (بدلاً من حذفها)
            update_invoices_query = "UPDATE invoices SET customer_id = NULL WHERE customer_id = ?"
            db.execute(update_invoices_query, (customer_id,))
            
            # حذف العميل
            delete_query = "DELETE FROM customers WHERE id = ?"
            db.execute(delete_query, (customer_id,))
            
            # حفظ التغييرات
            db.commit()
            return True
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            db.rollback()
            db.log_error(f"خطأ في حذف العميل: {str(e)}")
            return False
    
    @staticmethod
    def update_customer_purchases(customer_id, purchase_amount):
        """تحديث إجمالي مشتريات العميل وتاريخ آخر شراء"""
        if not customer_id:
            return False
            
        # الحصول على بيانات العميل الحالية
        customer = CustomerModel.get_customer_by_id(customer_id)
        if not customer:
            return False
            
        # حساب المجموع الجديد وزيادة عدد الزيارات
        current_total = customer.get('total_purchases', 0)
        new_total = current_total + purchase_amount
        visit_count = customer.get('visit_count', 0) + 1
        
        # تحديث العميل بالبيانات الجديدة
        query = """
            UPDATE customers
            SET total_purchases = ?,
                last_purchase = ?,
                visit_count = ?
            WHERE id = ?
        """
        
        # استخدام التاريخ والوقت الحاليين
        current_datetime = DateTimeUtils.get_current_date_time()
        
        params = (new_total, current_datetime, visit_count, customer_id)
        
        success = db.execute(query, params)
        if success:
            db.commit()
            return True
        return False
    
    @staticmethod
    def update_account(customer_name, amount):
        """تحديث حساب العميل بإضافة أو خصم مبلغ معين"""
        try:
            # البحث عن العميل بواسطة الاسم
            query = """
                SELECT id, total_purchases
                FROM customers
                WHERE name = ?
                LIMIT 1
            """
            customer = db.fetch_one(query, (customer_name,))
            
            if not customer:
                db.log_error(f"العميل غير موجود: {customer_name}")
                return False
                
            customer_id = customer.get('id')
            current_total = customer.get('total_purchases', 0)
            new_total = current_total + amount
            
            # تحديث إجمالي مشتريات العميل
            update_query = """
                UPDATE customers
                SET total_purchases = ?,
                    updated_at = ?
                WHERE id = ?
            """
            params = (
                new_total,
                datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                customer_id
            )
            
            success = db.execute(update_query, params)
            if success:
                db.commit()
                print(f"تم تحديث حساب العميل {customer_name} بمبلغ {amount} ليصبح الإجمالي {new_total}")
                return True
            return False
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            if db.conn:
                db.rollback()
            db.log_error(f"خطأ في تحديث حساب العميل {customer_name}: {str(e)}")
            print(f"خطأ في تحديث حساب العميل: {str(e)}")
            return False
    
    @staticmethod
    def get_customer_stats():
        """استرجاع إحصائيات العملاء"""
        # إجمالي عدد العملاء
        total_query = "SELECT COUNT(*) as total FROM customers"
        total_result = db.fetch_one(total_query)
        total_customers = total_result.get('total', 0) if total_result else 0
        
        # إجمالي قيمة المشتريات
        value_query = "SELECT SUM(total_purchases) as total_value FROM customers"
        value_result = db.fetch_one(value_query)
        total_value = value_result.get('total_value', 0) if value_result else 0
        
        # متوسط قيمة الشراء لكل عميل
        if total_customers > 0:
            avg_value = total_value / total_customers
        else:
            avg_value = 0
        
        return {
            "total_customers": total_customers,
            "total_value": total_value,
            "avg_value": avg_value
        }
    
    @staticmethod
    def get_top_customers(limit=5):
        """استرجاع أفضل العملاء من حيث قيمة المشتريات"""
        query = """
            SELECT id, name, phone, total_purchases, visit_count
            FROM customers
            ORDER BY total_purchases DESC
            LIMIT ?
        """
        return db.fetch_all(query, (limit,))
    
    @staticmethod
    def get_recent_customers(limit=5):
        """استرجاع أحدث العملاء من حيث تاريخ آخر زيارة"""
        query = """
            SELECT id, name, phone, total_purchases, last_purchase
            FROM customers
            WHERE last_purchase IS NOT NULL AND last_purchase <> ''
            ORDER BY last_purchase DESC
            LIMIT ?
        """
        return db.fetch_all(query, (limit,))
    
    @staticmethod
    def get_customer_invoices(customer_id):
        """استرجاع فواتير عميل محدد"""
        query = """
            SELECT id, reference_number, date, total, paid_amount, remaining_amount, status
            FROM invoices
            WHERE customer_id = ?
            ORDER BY date DESC
        """
        return db.fetch_all(query, (customer_id,))
    
    @staticmethod
    def get_customer_products(customer_id):
        """استرجاع جميع المنتجات التي اشتراها العميل مع معلومات الفاتورة"""
        query = """
            SELECT 
                item.id as item_id,
                item.product_id,
                item.product_name,
                item.product_code,
                item.quantity,
                item.unit_price,
                item.total_price,
                inv.id as invoice_id,
                inv.reference_number,
                inv.date,
                inv.status
            FROM invoice_items item
            JOIN invoices inv ON item.invoice_id = inv.id
            WHERE inv.customer_id = ?
            ORDER BY inv.date DESC, item.id
        """
        return db.fetch_all(query, (customer_id,))
    
    @staticmethod
    def get_customer_total_debt(customer_id):
        """استرجاع إجمالي الدين المستحق على العميل"""
        try:
            # حساب إجمالي المبالغ المتبقية من الفواتير غير المدفوعة
            remaining_query = """
                SELECT SUM(remaining_amount) as total_remaining
                FROM invoices
                WHERE customer_id = ? AND status = 'غير مدفوعة'
            """
            remaining_result = db.fetch_one(remaining_query, (customer_id,))
            total_debt = remaining_result.get('total_remaining', 0) if remaining_result and remaining_result.get('total_remaining') else 0
            
            return max(0, total_debt)  # التأكد من أن الدين لا يكون سالبًا
            
        except Exception as e:
            db.log_error(f"خطأ في حساب إجمالي دين العميل {customer_id}: {str(e)}")
            print(f"خطأ في حساب إجمالي دين العميل {customer_id}: {str(e)}")
            return 0  # إرجاع صفر في حالة الخطأ 
# -*- coding: utf-8 -*-
"""
مساعد الطباعة الحرارية
يحتوي على دوال مساعدة لحل مشاكل الطباعة على الطابعات الحرارية
"""

from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
from PyQt5.QtGui import QPainter, QFont, QTextDocument, QTextCursor
from PyQt5.QtCore import QSizeF, QSettings, Qt
from PyQt5.QtWidgets import QMessageBox, QApplication
import time
import sys


class ThermalPrinterHelper:
    """فئة مساعدة للطباعة على الطابعات الحرارية"""
    
    @staticmethod
    def create_thermal_printer(paper_width_mm=58):
        """إنشاء كائن طابعة محسن للطابعات الحرارية"""
        try:
            # إنشاء كائن الطابعة
            printer = QPrinter(QPrinter.HighResolution)
            
            # تعيين إعدادات الطابعة الحرارية
            printer.setOutputFormat(QPrinter.NativeFormat)
            printer.setColorMode(QPrinter.GrayScale)
            printer.setResolution(203)  # دقة مناسبة للطابعات الحرارية
            
            # تعيين حجم الورق
            estimated_height = 200  # ارتفاع تقديري
            printer.setPaperSize(QSizeF(paper_width_mm, estimated_height), QPrinter.Millimeter)
            
            # تعيين هوامش صغيرة
            printer.setPageMargins(1, 1, 1, 1, QPrinter.Millimeter)
            
            # تعيين اتجاه الطباعة
            printer.setOrientation(QPrinter.Portrait)
            
            return printer
            
        except Exception as e:
            print(f"خطأ في إنشاء الطابعة: {e}")
            return None
    
    @staticmethod
    def check_printer_status(printer):
        """فحص حالة الطابعة قبل الطباعة"""
        try:
            if printer.printerState() == QPrinter.Error:
                return False, "الطابعة في حالة خطأ"
            elif printer.printerState() == QPrinter.Aborted:
                return False, "تم إلغاء عملية الطباعة"
            elif printer.printerState() == QPrinter.Active:
                return True, "الطابعة جاهزة"
            else:
                return True, "حالة الطابعة غير معروفة"
        except Exception as e:
            return False, f"خطأ في فحص حالة الطابعة: {e}"
    
    @staticmethod
    def safe_print_widget(widget, printer, parent_widget=None):
        """طباعة آمنة لـ widget مع معالجة الأخطاء"""
        try:
            # فحص حالة الطابعة
            status_ok, status_msg = ThermalPrinterHelper.check_printer_status(printer)
            if not status_ok:
                if parent_widget:
                    QMessageBox.warning(parent_widget, "تحذير", status_msg)
                return False
            
            # إنشاء QPainter
            painter = QPainter()
            
            # التحقق من نجاح بدء الرسم
            if not painter.begin(printer):
                if parent_widget:
                    QMessageBox.critical(parent_widget, "خطأ في الطباعة", 
                                       "فشل في بدء عملية الطباعة. تأكد من تشغيل الطابعة.")
                return False
            
            try:
                # تعيين خصائص الرسم للطابعات الحرارية
                painter.setRenderHint(QPainter.Antialiasing, False)
                painter.setRenderHint(QPainter.TextAntialiasing, False)
                painter.setRenderHint(QPainter.SmoothPixmapTransform, False)
                
                # حساب التحجيم المناسب
                page_rect = printer.pageRect()
                widget_rect = widget.rect()
                
                # تحجيم أفضل للطابعات الحرارية
                scale_x = page_rect.width() / widget_rect.width()
                scale_y = page_rect.height() / widget_rect.height()
                scale_factor = min(scale_x, scale_y) * 0.95  # تقليل قليلاً لضمان عدم القطع
                
                # تطبيق التحجيم
                painter.scale(scale_factor, scale_factor)
                
                # رسم محتوى الـ widget
                widget.render(painter)
                
                # التأكد من إنهاء الرسم بشكل صحيح
                painter.end()
                
                # انتظار قصير للتأكد من اكتمال الطباعة
                time.sleep(0.5)
                
                return True
                
            except Exception as paint_error:
                painter.end()  # التأكد من إنهاء الرسم حتى في حالة الخطأ
                raise paint_error
                
        except Exception as e:
            error_msg = f"حدث خطأ أثناء الطباعة:\n{str(e)}\n\nتأكد من:\n"
            error_msg += "• تشغيل الطابعة الحرارية\n"
            error_msg += "• توصيل كابل USB أو الشبكة\n"
            error_msg += "• توفر ورق الطباعة\n"
            error_msg += "• تثبيت تعريف الطابعة الصحيح"
            
            if parent_widget:
                QMessageBox.critical(parent_widget, "خطأ في الطباعة", error_msg)
            
            return False
    
    @staticmethod
    def print_text_directly(text, printer, parent_widget=None):
        """طباعة نص مباشرة على الطابعة الحرارية"""
        try:
            # إنشاء مستند نصي
            document = QTextDocument()
            document.setPlainText(text)
            
            # تعيين خط مناسب للطباعة الحرارية
            font = QFont("Courier New", 10)
            document.setDefaultFont(font)
            
            # طباعة المستند
            document.print_(printer)
            
            return True
            
        except Exception as e:
            if parent_widget:
                QMessageBox.critical(parent_widget, "خطأ في الطباعة", 
                                   f"حدث خطأ أثناء طباعة النص: {str(e)}")
            return False
    
    @staticmethod
    def show_print_dialog(printer, parent_widget=None, title="طباعة على طابعة حرارية"):
        """عرض مربع حوار الطباعة مع إعدادات محسنة"""
        try:
            print_dialog = QPrintDialog(printer, parent_widget)
            print_dialog.setWindowTitle(title)
            
            # تعيين خيارات الطباعة
            print_dialog.setOptions(
                QPrintDialog.PrintToFile |
                QPrintDialog.PrintSelection |
                QPrintDialog.PrintPageRange |
                QPrintDialog.PrintCurrentPage
            )
            
            return print_dialog.exec_() == QPrintDialog.Accepted
            
        except Exception as e:
            if parent_widget:
                QMessageBox.critical(parent_widget, "خطأ", 
                                   f"حدث خطأ في مربع حوار الطباعة: {str(e)}")
            return False
    
    @staticmethod
    def get_available_printers():
        """الحصول على قائمة الطابعات المتاحة"""
        try:
            from PyQt5.QtPrintSupport import QPrinterInfo
            
            printers = []
            for printer_info in QPrinterInfo.availablePrinters():
                printers.append({
                    'name': printer_info.printerName(),
                    'description': printer_info.description(),
                    'is_default': printer_info.isDefault(),
                    'is_remote': printer_info.isRemote()
                })
            
            return printers
            
        except Exception as e:
            print(f"خطأ في الحصول على قائمة الطابعات: {e}")
            return []
    
    @staticmethod
    def find_thermal_printers():
        """البحث عن الطابعات الحرارية المتصلة"""
        try:
            thermal_keywords = ['xprinter', 'thermal', 'pos', 'receipt', 'حراري']
            available_printers = ThermalPrinterHelper.get_available_printers()
            
            thermal_printers = []
            for printer in available_printers:
                printer_name_lower = printer['name'].lower()
                printer_desc_lower = printer['description'].lower()
                
                for keyword in thermal_keywords:
                    if keyword in printer_name_lower or keyword in printer_desc_lower:
                        thermal_printers.append(printer)
                        break
            
            return thermal_printers
            
        except Exception as e:
            print(f"خطأ في البحث عن الطابعات الحرارية: {e}")
            return []
    
    @staticmethod
    def test_printer_connection(printer_name):
        """اختبار اتصال الطابعة"""
        try:
            printer = QPrinter()
            printer.setPrinterName(printer_name)
            
            # محاولة إنشاء مهمة طباعة تجريبية
            painter = QPainter()
            if painter.begin(printer):
                painter.end()
                return True, "الطابعة متصلة وجاهزة"
            else:
                return False, "فشل في الاتصال بالطابعة"
                
        except Exception as e:
            return False, f"خطأ في اختبار الطابعة: {str(e)}"


def test_thermal_printing():
    """دالة اختبار للطباعة الحرارية"""
    try:
        print("=== اختبار الطباعة الحرارية ===")
        
        # البحث عن الطابعات الحرارية
        thermal_printers = ThermalPrinterHelper.find_thermal_printers()
        print(f"تم العثور على {len(thermal_printers)} طابعة حرارية:")
        
        for printer in thermal_printers:
            print(f"- {printer['name']} ({printer['description']})")
            
            # اختبار الاتصال
            success, message = ThermalPrinterHelper.test_printer_connection(printer['name'])
            print(f"  حالة الاتصال: {message}")
        
        # إنشاء طابعة تجريبية
        test_printer = ThermalPrinterHelper.create_thermal_printer()
        if test_printer:
            print("تم إنشاء كائن الطابعة بنجاح")
        else:
            print("فشل في إنشاء كائن الطابعة")
            
    except Exception as e:
        print(f"خطأ في اختبار الطباعة: {e}")


if __name__ == "__main__":
    # تشغيل اختبار الطباعة الحرارية
    app = QApplication(sys.argv)
    test_thermal_printing()
    sys.exit()

import os
import tempfile
import shutil
from PyQt5.QtWidgets import (
    <PERSON>W<PERSON><PERSON>, QVBoxLayout, QHBoxLayout, QTabWidget, QScrollArea, QFormLayout,
    QLabel, QLineEdit, QPushButton, QGroupBox, QFileDialog, QProgressBar,
    QTableWidget, QTableWidgetItem, QHeaderView, QFrame, QCheckBox, QSpinBox,
    QMessageBox, QDialog, QComboBox, QProgressDialog, QGridLayout
)
from PyQt5.QtCore import Qt, QSettings, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QIcon
from controllers.database_controller import DatabaseController
from controllers.user_controller import UserController
from PyQt5.QtCore import Qt, QSize, QDate
from PyQt5.QtGui import QPixmap
from styles import AppStyles  # استيراد التنسيقات من ملف styles.py

# Define RTL helper classes directly
class RTLComboBox(QComboBox):
    """Replacement class for RTLComboBox"""
    pass

def apply_rtl_to_all_widgets(widget):
    """Replacement function for apply_rtl_to_all_widgets"""
    pass

# إعدادات افتراضية للتطبيق
DEFAULT_SETTINGS = {
    # الإعدادات العامة
    "app_name": "Smart Manager",
    "company_name": "شركتي",
    "company_phone": "01xxxxxxxxx",

    "company_address": "عنوان الشركة",
    "currency": "ج.م",
    "language": "العربية",
    "theme": "فاتح",
    # إعدادات المخزون
    "stock_alert": 5,
    "auto_stock_alert": True,
    # إعدادات الفواتير
    "invoice_notes": "شكراً لتعاملكم معنا",
    # إعدادات النظام
    "auto_backup": True,
    "backup_every_days": 7,
    "backup_location": "backups/",
}

class SettingsView(QWidget):
    def __init__(self):
        super().__init__()

        # إنشاء كائن الإعدادات
        self.settings = QSettings("MyCompany", "SmartManager")

        # إعداد التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)

        # إضافة عنوان الصفحة
        header_layout = QHBoxLayout()
        page_title = QLabel("الإعدادات")
        page_title.setObjectName("page_title")
        page_title.setFont(QFont("Arial", 20, QFont.Bold))
        header_layout.addWidget(page_title)

        # إضافة زر لحفظ الإعدادات
        self.save_settings_btn = QPushButton("⚙️  حفظ الإعدادات")
        self.save_settings_btn.setFixedSize(180, 40)
        self.save_settings_btn.setObjectName("action_button")
        self.save_settings_btn.setFont(QFont("Arial", 11))
        self.save_settings_btn.setCursor(Qt.PointingHandCursor)
        self.save_settings_btn.clicked.connect(self.save_settings)
        header_layout.addStretch()
        header_layout.addWidget(self.save_settings_btn)

        layout.addLayout(header_layout)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("content_separator")
        layout.addWidget(separator)
        layout.addSpacing(20)

        # إنشاء تبويبات الإعدادات
        self.settings_tabs = QTabWidget()
        self.settings_tabs.setObjectName("settings_tabs")
        self.settings_tabs.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه التبويبات من اليمين إلى اليسار

        # إنشاء تبويبات مختلفة للإعدادات
        self.general_tab = self.create_general_tab()
        self.users_tab = self.create_users_tab()
        self.database_tab = self.create_database_tab()
        self.backup_tab = self.create_backup_tab()

        # إضافة التبويبات إلى widget التبويبات
        self.settings_tabs.addTab(self.general_tab, "عام")
        self.settings_tabs.addTab(self.users_tab, "المستخدمين")
        self.settings_tabs.addTab(self.database_tab, "قاعدة البيانات")
        self.settings_tabs.addTab(self.backup_tab, "النسخ الاحتياطي")

        # ربط تغيير التبويب بدالة تحديث المحتوى
        self.settings_tabs.currentChanged.connect(self.on_tab_changed)

        layout.addWidget(self.settings_tabs)

        # تحميل الإعدادات عند بدء التشغيل
        self.load_settings()

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def on_tab_changed(self, index):
        """معالجة تغيير التبويب وتحديث المحتوى المناسب"""
        tab_name = self.settings_tabs.tabText(index)

        if tab_name == "عام":
            # يمكن إضافة أي تحديثات مطلوبة للتاب العام
            pass
        elif tab_name == "المستخدمين":
            # يمكن إضافة أي تحديثات مطلوبة لتاب المستخدمين
            pass
        elif tab_name == "قاعدة البيانات":
            # يمكن إضافة أي تحديثات مطلوبة لتاب قاعدة البيانات
            pass
        elif tab_name == "النسخ الاحتياطي":
            # تحديث قائمة النسخ الاحتياطية
            self.refresh_backups_list()

        print(f"تم تحديث محتوى تاب {tab_name}")

    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        tab = QWidget()
        tab.setLayoutDirection(Qt.RightToLeft)

        # إنشاء التخطيط الرئيسي مباشرة بدون scroll area
        main_layout = QVBoxLayout(tab)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(20)

        # إضافة شريط العنوان
        title_header = self.create_title_header(
            "الإعدادات العامة للنظام",
            "إدارة وتخصيص إعدادات النظام"
        )
        main_layout.addWidget(title_header)

        # إنشاء widget متجاوب للشبكة
        self.responsive_grid_widget = QWidget()
        self.responsive_grid_layout = QGridLayout(self.responsive_grid_widget)
        self.responsive_grid_layout.setContentsMargins(0, 0, 0, 0)
        self.responsive_grid_layout.setSpacing(15)

        # تعيين خصائص التمدد للأعمدة والصفوف
        self.responsive_grid_layout.setColumnStretch(0, 1)  # العمود الأول قابل للتمدد
        self.responsive_grid_layout.setColumnStretch(1, 1)  # العمود الثاني قابل للتمدد
        self.responsive_grid_layout.setRowStretch(0, 1)     # الصف الأول قابل للتمدد
        self.responsive_grid_layout.setRowStretch(1, 1)     # الصف الثاني قابل للتمدد

        # قسم معلومات الشركة
        self.company_group = QGroupBox("معلومات الشركة")
        self.company_group.setMinimumWidth(300)  # حد أدنى للعرض
        company_layout = QFormLayout()
        company_layout.setLabelAlignment(Qt.AlignRight)
        company_layout.setSpacing(12)

        # حقول معلومات الشركة
        self.company_name = QLineEdit()
        self.company_name.setObjectName("search_input")
        self.company_name.setPlaceholderText("أدخل اسم الشركة")
        company_layout.addRow("اسم الشركة:", self.company_name)

        self.company_phone = QLineEdit()
        self.company_phone.setObjectName("search_input")
        self.company_phone.setPlaceholderText("أدخل رقم الهاتف")
        company_layout.addRow("رقم الهاتف:", self.company_phone)



        self.company_address = QLineEdit()
        self.company_address.setObjectName("search_input")
        self.company_address.setPlaceholderText("أدخل عنوان الشركة")
        company_layout.addRow("العنوان:", self.company_address)


        self.company_group.setLayout(company_layout)

        # قسم إعدادات التطبيق
        self.app_group = QGroupBox("إعدادات التطبيق")
        self.app_group.setMinimumWidth(300)  # حد أدنى للعرض
        app_layout = QFormLayout()
        app_layout.setLabelAlignment(Qt.AlignRight)
        app_layout.setSpacing(12)

        # عملة التطبيق
        self.currency_combo = RTLComboBox()
        self.currency_combo.setObjectName("combo_box")
        self.currency_combo.addItems(["ج.م", "$", "€", "£"])
        app_layout.addRow("العملة:", self.currency_combo)

        # لغة التطبيق
        self.language_combo = RTLComboBox()
        self.language_combo.setObjectName("combo_box")
        self.language_combo.addItems(["العربية"])
        app_layout.addRow("اللغة:", self.language_combo)

        # سمة التطبيق
        self.theme_combo = RTLComboBox()
        self.theme_combo.setObjectName("combo_box")
        self.theme_combo.addItems(["فاتح"])
        app_layout.addRow("سمة التطبيق:", self.theme_combo)

        self.app_group.setLayout(app_layout)

        # قسم إعدادات المخزون
        self.inventory_group = QGroupBox("إعدادات المخزون")
        self.inventory_group.setMinimumWidth(300)  # حد أدنى للعرض
        inventory_layout = QFormLayout()
        inventory_layout.setLabelAlignment(Qt.AlignRight)
        inventory_layout.setSpacing(12)

        # حد التنبيه للمخزون
        self.stock_alert_spin = QSpinBox()
        self.stock_alert_spin.setObjectName("search_input")
        self.stock_alert_spin.setRange(1, 100)
        self.stock_alert_spin.setValue(5)
        inventory_layout.addRow("حد التنبيه للمخزون:", self.stock_alert_spin)

        # تفعيل التنبيه التلقائي للمخزون
        self.auto_stock_alert = QCheckBox("تفعيل التنبيه التلقائي عند انخفاض المخزون")
        self.auto_stock_alert.setObjectName("checkbox")
        inventory_layout.addRow("", self.auto_stock_alert)

        self.inventory_group.setLayout(inventory_layout)

        # قسم إعدادات الفواتير
        self.invoice_group = QGroupBox("إعدادات الفواتير")
        self.invoice_group.setMinimumWidth(300)  # حد أدنى للعرض
        invoice_layout = QFormLayout()
        invoice_layout.setLabelAlignment(Qt.AlignRight)
        invoice_layout.setSpacing(12)



        # ملاحظات الفاتورة
        self.invoice_notes = QLineEdit()
        self.invoice_notes.setObjectName("search_input")
        self.invoice_notes.setPlaceholderText("أدخل ملاحظات إضافية للفاتورة")
        invoice_layout.addRow("ملاحظات الفاتورة:", self.invoice_notes)

        self.invoice_group.setLayout(invoice_layout)

        # ترتيب الأقسام في شبكة متجاوبة
        self.arrange_responsive_layout()

        # إضافة widget الشبكة المتجاوبة إلى التخطيط الرئيسي
        main_layout.addWidget(self.responsive_grid_widget, 1)  # إعطاء وزن 1 للتمدد

        return tab

    def create_title_header(self, title, subtitle):
        """إنشاء شريط عنوان موحد لجميع التابات"""
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-bottom: 3px solid #007bff;
                margin-bottom: 10px;
            }
        """)

        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(20, 15, 20, 15)
        title_layout.setSpacing(5)

        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 20, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: transparent;
                border: none;
            }
        """)

        subtitle_label = QLabel(subtitle)
        subtitle_label.setFont(QFont("Arial", 11))
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                background: transparent;
                border: none;
            }
        """)

        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)

        return title_container

    def arrange_responsive_layout(self):
        """ترتيب العناصر بشكل متجاوب حسب حجم النافذة"""
        # إزالة جميع العناصر من التخطيط أولاً
        for i in reversed(range(self.responsive_grid_layout.count())):
            self.responsive_grid_layout.itemAt(i).widget().setParent(None)

        # الحصول على عرض النافذة الحالي
        window_width = self.width() if hasattr(self, 'width') else 800

        # تحديد نقطة التحول للتخطيط المتجاوب
        breakpoint = 900  # عرض النافذة الذي يتم عنده التغيير من شبكة إلى عمود

        if window_width < breakpoint:
            # تخطيط عمودي للشاشات الصغيرة (عمود واحد)
            self.responsive_grid_layout.addWidget(self.company_group, 0, 0)
            self.responsive_grid_layout.addWidget(self.app_group, 1, 0)
            self.responsive_grid_layout.addWidget(self.inventory_group, 2, 0)
            self.responsive_grid_layout.addWidget(self.invoice_group, 3, 0)

            # إعادة تعيين خصائص التمدد للعمود الواحد
            self.responsive_grid_layout.setColumnStretch(0, 1)
            self.responsive_grid_layout.setColumnStretch(1, 0)
        else:
            # تخطيط شبكي للشاشات الكبيرة (2x2)
            self.responsive_grid_layout.addWidget(self.company_group, 0, 0)
            self.responsive_grid_layout.addWidget(self.app_group, 0, 1)
            self.responsive_grid_layout.addWidget(self.inventory_group, 1, 0)
            self.responsive_grid_layout.addWidget(self.invoice_group, 1, 1)

            # إعادة تعيين خصائص التمدد للشبكة
            self.responsive_grid_layout.setColumnStretch(0, 1)
            self.responsive_grid_layout.setColumnStretch(1, 1)

    def resizeEvent(self, event):
        """التعامل مع تغيير حجم النافذة"""
        super().resizeEvent(event)
        # إعادة ترتيب التخطيط عند تغيير حجم النافذة
        if hasattr(self, 'responsive_grid_layout'):
            self.arrange_responsive_layout()

    def create_users_tab(self):
        """إنشاء تبويب إعدادات المستخدمين"""
        tab = QWidget()
        tab.setLayoutDirection(Qt.RightToLeft)

        # إنشاء منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        # إنشاء محتوى التبويب
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(15, 15, 15, 15)
        content_layout.setSpacing(15)

        # قسم المستخدم الحالي - يفترض أن هناك نظام تسجيل دخول يحفظ معلومات المستخدم الحالي
        # هنا نعرض معلومات المستخدم admin افتراضياً
        current_user_group = QGroupBox("المستخدم الحالي")
        current_user_layout = QFormLayout()
        current_user_layout.setLabelAlignment(Qt.AlignRight)
        current_user_layout.setSpacing(12)

        # معلومات المستخدم
        self.current_user_name = QLabel("المدير")
        self.current_user_name.setFont(QFont("Arial", 10, QFont.Bold))
        current_user_layout.addRow("اسم المستخدم:", self.current_user_name)

        self.current_user_role = QLabel("مدير النظام")
        self.current_user_role.setStyleSheet("color: #3b82f6; font-weight: bold;")
        current_user_layout.addRow("الصلاحية:", self.current_user_role)

        # أزرار تغيير كلمة المرور وتسجيل الخروج
        buttons_layout = QHBoxLayout()

        change_password_btn = QPushButton("تغيير كلمة المرور")
        change_password_btn.setObjectName("secondary_button")
        change_password_btn.clicked.connect(self.change_current_password)

        logout_btn = QPushButton("تسجيل الخروج")
        logout_btn.setObjectName("danger_button")
        logout_btn.clicked.connect(self.logout_user)

        buttons_layout.addWidget(change_password_btn)
        buttons_layout.addWidget(logout_btn)
        buttons_layout.addStretch()

        current_user_layout.addRow("", buttons_layout)

        current_user_group.setLayout(current_user_layout)
        content_layout.addWidget(current_user_group)

        # قسم إدارة المستخدمين
        users_group = QGroupBox("إدارة المستخدمين")
        users_layout = QVBoxLayout()

        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(4)
        self.users_table.setHorizontalHeaderLabels(["اسم المستخدم", "الاسم الكامل", "الصلاحية", "الحالة"])

        # ضبط خصائص الجدول
        self.users_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.users_table.verticalHeader().setVisible(False)
        self.users_table.setAlternatingRowColors(True)
        self.users_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.users_table.setSelectionBehavior(QTableWidget.SelectRows)

        # تحميل بيانات المستخدمين من قاعدة البيانات
        self.load_users_data()

        users_layout.addWidget(self.users_table)

        # أزرار إدارة المستخدمين
        user_buttons_layout = QHBoxLayout()

        self.edit_user_btn = QPushButton("✏️  تعديل المستخدم المحدد")
        self.edit_user_btn.setObjectName("secondary_button")
        self.edit_user_btn.clicked.connect(self.edit_user)

        self.delete_user_btn = QPushButton("❌  حذف المستخدم المحدد")
        self.delete_user_btn.setObjectName("danger_button")
        self.delete_user_btn.clicked.connect(self.delete_user)

        user_buttons_layout.addWidget(self.edit_user_btn)
        user_buttons_layout.addWidget(self.delete_user_btn)
        user_buttons_layout.addStretch()

        users_layout.addLayout(user_buttons_layout)
        users_group.setLayout(users_layout)
        content_layout.addWidget(users_group)

        # قسم صلاحيات المستخدمين
        permissions_group = QGroupBox("صلاحيات المستخدمين")
        permissions_layout = QVBoxLayout()

        # شرح مختصر
        permissions_info = QLabel("تحديد الصلاحيات لكل فئة من المستخدمين")
        permissions_info.setObjectName("hint_label")
        permissions_info.setWordWrap(True)
        permissions_layout.addWidget(permissions_info)

        # جدول الصلاحيات
        self.permissions_table = QTableWidget()
        self.permissions_table.setColumnCount(4)
        self.permissions_table.setHorizontalHeaderLabels(["الصلاحية", "مدير", "كاشير", "مدير مخزون"])

        # ضبط خصائص الجدول
        self.permissions_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.permissions_table.verticalHeader().setVisible(False)
        self.permissions_table.setAlternatingRowColors(True)
        # زيادة ارتفاع الصفوف في جدول الصلاحيات
        self.permissions_table.verticalHeader().setDefaultSectionSize(40)

        # تحميل بيانات الصلاحيات من قاعدة البيانات
        self.load_permissions_data()

        permissions_layout.addWidget(self.permissions_table)

        # إضافة زر لحفظ الصلاحيات
        save_permissions_layout = QHBoxLayout()
        self.save_permissions_btn = QPushButton("حفظ الصلاحيات")
        self.save_permissions_btn.setObjectName("secondary_button")
        self.save_permissions_btn.clicked.connect(self.save_permissions)
        save_permissions_layout.addWidget(self.save_permissions_btn)
        save_permissions_layout.addStretch()
        permissions_layout.addLayout(save_permissions_layout)

        # إضافة زر إضافة مستخدم جديد بعد جدول الصلاحيات
        add_user_layout = QHBoxLayout()
        self.add_user_btn = QPushButton("➕  إضافة مستخدم جديد")
        self.add_user_btn.setObjectName("action_button")
        self.add_user_btn.clicked.connect(self.add_user)
        add_user_layout.addWidget(self.add_user_btn)
        add_user_layout.addStretch()
        permissions_layout.addLayout(add_user_layout)

        permissions_group.setLayout(permissions_layout)
        content_layout.addWidget(permissions_group)

        # إضافة مسافة قابلة للتمدد في نهاية التبويب
        content_layout.addStretch()

        # تعيين المحتوى للمنطقة القابلة للتمرير
        scroll_area.setWidget(content_widget)

        # إضافة المنطقة القابلة للتمرير إلى التبويب
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)

        return tab

    def load_users_data(self):
        """تحميل بيانات المستخدمين من قاعدة البيانات"""
        try:
            # تفريغ الجدول أولاً
            self.users_table.setRowCount(0)

            # الحصول على بيانات المستخدمين من قاعدة البيانات
            users = UserController.get_all_users()

            # ملء الجدول بالبيانات
            for row, user in enumerate(users):
                self.users_table.insertRow(row)
                self.users_table.setItem(row, 0, QTableWidgetItem(user['username']))
                self.users_table.setItem(row, 1, QTableWidgetItem(user['full_name']))
                self.users_table.setItem(row, 2, QTableWidgetItem(user['role']))

                status_item = QTableWidgetItem(user['status'])
                if user['is_active']:
                    status_item.setForeground(QColor("#27ae60"))  # لون أخضر للمستخدمين النشطين
                else:
                    status_item.setForeground(QColor("#e74c3c"))  # لون أحمر للمستخدمين غير النشطين
                self.users_table.setItem(row, 3, status_item)

                # تخزين معرف المستخدم كبيانات إضافية
                self.users_table.item(row, 0).setData(Qt.UserRole, user['id'])

            print(f"تم تحميل {len(users)} مستخدم من قاعدة البيانات")
        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل بيانات المستخدمين: {str(e)}")

    def load_permissions_data(self):
        """تحميل بيانات صلاحيات الأدوار من قاعدة البيانات"""
        try:
            # تفريغ الجدول أولاً
            self.permissions_table.setRowCount(0)

            # الحصول على بيانات الصلاحيات من قاعدة البيانات
            permissions = UserController.get_role_permissions()

            # الصلاحيات المتاحة
            available_permissions = [
                "إدارة المبيعات",
                "إدارة المنتجات",
                "إدارة الفواتير",
                "إدارة التقارير",
                "إدارة العملاء",
                "إدارة الموردين",
                "إدارة الإعدادات"
            ]

            # ملء الجدول بالبيانات
            for row, perm_name in enumerate(available_permissions):
                self.permissions_table.insertRow(row)
                self.permissions_table.setItem(row, 0, QTableWidgetItem(perm_name))

                # إنشاء خلايا للصلاحيات
                for col, role in enumerate(["مدير", "كاشير", "مدير مخزون"]):
                    checkbox = QCheckBox()
                    # تعيين حالة الـ checkbox بناءً على البيانات المستلمة
                    is_checked = True if role == "مدير" else False

                    if role in permissions and perm_name in permissions[role]:
                        is_checked = permissions[role][perm_name]

                    checkbox.setChecked(is_checked)

                    # تعطيل التعديل للمدير (يجب أن يكون لديه كل الصلاحيات)
                    if role == "مدير":
                        checkbox.setEnabled(False)
                    else:
                        # ربط الـ checkbox بوظيفة تغيير الصلاحيات
                        checkbox.stateChanged.connect(lambda state, r=row, c=col+1: self.on_permission_changed(r, c, state))

                    # إنشاء محتوى مخصص لوضع الـ checkbox في وسط الخلية
                    cell_widget = QWidget()
                    cell_layout = QHBoxLayout(cell_widget)
                    cell_layout.setAlignment(Qt.AlignCenter)
                    cell_layout.setContentsMargins(0, 0, 0, 0)
                    cell_layout.addWidget(checkbox)

                    self.permissions_table.setCellWidget(row, col + 1, cell_widget)

            print("تم تحميل بيانات الصلاحيات")
        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل بيانات الصلاحيات: {str(e)}")

    def change_current_password(self):
        """تغيير كلمة مرور المستخدم الحالي"""
        try:
            # في هذا المثال نفترض أن المستخدم الحالي هو admin
            # في التطبيق الحقيقي يجب استخدام معرف المستخدم الحالي من نظام تسجيل الدخول
            dialog = QDialog(self)
            dialog.setWindowTitle("تغيير كلمة المرور")
            dialog.setMinimumWidth(400)

            # تخطيط النموذج
            form_layout = QFormLayout(dialog)

            # حقول كلمة المرور
            old_password = QLineEdit()
            old_password.setObjectName("search_input")
            old_password.setPlaceholderText("أدخل كلمة المرور الحالية")
            old_password.setEchoMode(QLineEdit.Password)

            new_password = QLineEdit()
            new_password.setObjectName("search_input")
            new_password.setPlaceholderText("أدخل كلمة المرور الجديدة")
            new_password.setEchoMode(QLineEdit.Password)

            confirm_password = QLineEdit()
            confirm_password.setObjectName("search_input")
            confirm_password.setPlaceholderText("تأكيد كلمة المرور الجديدة")
            confirm_password.setEchoMode(QLineEdit.Password)

            # إضافة الحقول إلى التخطيط
            form_layout.addRow("كلمة المرور الحالية:", old_password)
            form_layout.addRow("كلمة المرور الجديدة:", new_password)
            form_layout.addRow("تأكيد كلمة المرور:", confirm_password)

            # أزرار الإجراءات
            button_box = QHBoxLayout()
            save_btn = QPushButton("تغيير كلمة المرور")
            save_btn.setObjectName("action_button")

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.setObjectName("secondary_button")

            button_box.addWidget(save_btn)
            button_box.addWidget(cancel_btn)

            form_layout.addRow("", button_box)

            # ربط الأزرار بالوظائف
            cancel_btn.clicked.connect(dialog.reject)

            # وظيفة تغيير كلمة المرور
            def change_password():
                # التحقق من الإدخال
                if not old_password.text() or not new_password.text() or not confirm_password.text():
                    QMessageBox.warning(dialog, "بيانات غير مكتملة", "يرجى ملء جميع الحقول")
                    return

                # التحقق من تطابق كلمة المرور الجديدة
                if new_password.text() != confirm_password.text():
                    QMessageBox.warning(dialog, "كلمات المرور غير متطابقة", "كلمة المرور الجديدة وتأكيدها غير متطابقين")
                    return

                # تغيير كلمة المرور - افتراضياً نستخدم المستخدم الأول (admin)
                admin_user = UserController.get_user_info(username="admin")
                if admin_user:
                    success, message = UserController.change_password(
                        admin_user['id'], old_password.text(), new_password.text()
                    )

                    if success:
                        QMessageBox.information(dialog, "تم تغيير كلمة المرور", message)
                        dialog.accept()
                    else:
                        QMessageBox.warning(dialog, "خطأ", message)
                else:
                    QMessageBox.warning(dialog, "خطأ", "لم يتم العثور على المستخدم")

            save_btn.clicked.connect(change_password)

            # عرض الحوار
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تغيير كلمة المرور: {str(e)}")

    def logout_user(self):
        """تسجيل خروج المستخدم"""
        # في هذا المثال نعرض رسالة فقط لأن النظام لا يتضمن بعد نظام تسجيل الدخول الكامل
        reply = QMessageBox.question(
            self,
            "تسجيل الخروج",
            "هل أنت متأكد من رغبتك بتسجيل الخروج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تسجيل الخروج", "تم تسجيل الخروج بنجاح")
            # هنا يمكن إضافة الكود اللازم لتسجيل الخروج وإعادة توجيه المستخدم إلى صفحة تسجيل الدخول

    def create_database_tab(self):
        """إنشاء تبويب إعدادات قاعدة البيانات"""
        tab = QWidget()
        tab.setLayoutDirection(Qt.RightToLeft)

        # إنشاء منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        # إنشاء محتوى التبويب
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(15, 15, 15, 15)
        content_layout.setSpacing(15)

        # قسم إعدادات الاتصال بقاعدة البيانات
        connection_group = QGroupBox("إعدادات قاعدة البيانات SQLite")
        connection_layout = QFormLayout()
        connection_layout.setLabelAlignment(Qt.AlignRight)
        connection_layout.setSpacing(12)

        # مسار ملف قاعدة البيانات (ل SQLite)
        self.db_path_layout = QHBoxLayout()
        self.db_path_input = QLineEdit()
        self.db_path_input.setObjectName("search_input")
        self.db_path_input.setPlaceholderText("مسار ملف قاعدة البيانات")
        self.db_path_input.setText("database.db")

        browse_btn = QPushButton("استعراض...")
        browse_btn.setObjectName("secondary_button")
        browse_btn.clicked.connect(self.browse_db_file)

        self.db_path_layout.addWidget(self.db_path_input)
        self.db_path_layout.addWidget(browse_btn)

        connection_layout.addRow("مسار قاعدة البيانات:", self.db_path_layout)

        # زر اختبار الاتصال
        test_connection_btn = QPushButton("اختبار الاتصال")
        test_connection_btn.setObjectName("secondary_button")
        test_connection_btn.clicked.connect(self.test_connection)
        connection_layout.addRow("", test_connection_btn)

        connection_group.setLayout(connection_layout)
        content_layout.addWidget(connection_group)

        # قسم صيانة قاعدة البيانات
        maintenance_group = QGroupBox("صيانة قاعدة البيانات")
        maintenance_layout = QVBoxLayout()

        maintenance_info = QLabel("استخدم هذه الأدوات بحذر لصيانة قاعدة البيانات.")
        maintenance_info.setObjectName("hint_label")
        maintenance_info.setWordWrap(True)
        maintenance_layout.addWidget(maintenance_info)

        maintenance_buttons_layout = QGridLayout()

        vacuum_btn = QPushButton("تنظيف قاعدة البيانات")
        vacuum_btn.setObjectName("secondary_button")
        vacuum_btn.setToolTip("إزالة المساحات الفارغة وتحسين أداء البيانات")
        vacuum_btn.clicked.connect(self.vacuum_database)

        rebuild_btn = QPushButton("إعادة بناء الفهارس")
        rebuild_btn.setObjectName("secondary_button")
        rebuild_btn.setToolTip("إعادة بناء فهارس قاعدة البيانات لتحسين سرعة البحث")

        analyze_btn = QPushButton("تحليل قاعدة البيانات")
        analyze_btn.setObjectName("secondary_button")
        analyze_btn.setToolTip("تحليل قاعدة البيانات وتحسين أداء الاستعلامات")

        reset_btn = QPushButton("إعادة تعيين قاعدة البيانات")
        reset_btn.setObjectName("danger_button")
        reset_btn.setToolTip("حذف جميع البيانات وإعادة تهيئة الجداول")
        reset_btn.clicked.connect(self.reset_database)

        maintenance_buttons_layout.addWidget(vacuum_btn, 0, 0)
        maintenance_buttons_layout.addWidget(rebuild_btn, 0, 1)
        maintenance_buttons_layout.addWidget(analyze_btn, 1, 0)
        maintenance_buttons_layout.addWidget(reset_btn, 1, 1)

        maintenance_layout.addLayout(maintenance_buttons_layout)
        maintenance_group.setLayout(maintenance_layout)
        content_layout.addWidget(maintenance_group)

        # قسم نقل البيانات
        export_group = QGroupBox("نقل البيانات")
        export_layout = QVBoxLayout()

        export_info = QLabel("استخدم هذه الأدوات لتصدير أو استيراد البيانات بتنسيق CSV.")
        export_info.setObjectName("hint_label")
        export_info.setWordWrap(True)
        export_layout.addWidget(export_info)

        export_buttons_layout = QHBoxLayout()

        export_data_btn = QPushButton("تصدير البيانات")
        export_data_btn.setObjectName("secondary_button")
        export_data_btn.setToolTip("تصدير جميع البيانات إلى ملفات CSV")

        import_data_btn = QPushButton("استيراد البيانات")
        import_data_btn.setObjectName("secondary_button")
        import_data_btn.setToolTip("استيراد البيانات من ملفات CSV")

        export_buttons_layout.addWidget(export_data_btn)
        export_buttons_layout.addWidget(import_data_btn)

        export_layout.addLayout(export_buttons_layout)
        export_group.setLayout(export_layout)
        content_layout.addWidget(export_group)

        # إضافة مسافة قابلة للتمدد في نهاية التبويب
        content_layout.addStretch()

        # تعيين المحتوى للمنطقة القابلة للتمرير
        scroll_area.setWidget(content_widget)

        # إضافة المنطقة القابلة للتمرير إلى التبويب
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)

        return tab

    def browse_db_file(self):
        """اختيار مسار ملف قاعدة البيانات"""
        file_name, _ = QFileDialog.getSaveFileName(
            self,
            "اختر مسار ملف قاعدة البيانات",
            "",
            "قواعد بيانات SQLite (*.db *.sqlite);;كل الملفات (*.*)"
        )
        if file_name:
            self.db_path_input.setText(file_name)

    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        # جمع إعدادات الاتصال الحالية من واجهة المستخدم
        db_settings = {
            "db_type": "sqlite",
            "db_path": self.db_path_input.text(),
            "db_host": "",
            "db_port": "",
            "db_name": "",
            "db_user": "",
            "db_password": ""
        }

        try:
            # استخدام المتحكم لاختبار الاتصال
            db_controller = DatabaseController()
            success, message = db_controller.test_connection(db_settings)

            if success:
                QMessageBox.information(
                    self,
                    "نجاح الاتصال",
                    f"تم الاتصال بقاعدة البيانات SQLite بنجاح.\n\n{message}"
                )
            else:
                QMessageBox.critical(
                    self,
                    "فشل الاتصال",
                    f"فشل الاتصال بقاعدة البيانات: {message}"
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء محاولة الاتصال: {str(e)}"
            )

    def vacuum_database(self):
        """تنظيف قاعدة البيانات"""
        reply = QMessageBox.question(
            self,
            "تنظيف قاعدة البيانات",
            "هذه العملية ستقوم بتنظيف قاعدة البيانات وتحسين أدائها. هل تريد المتابعة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # هنا يمكن إضافة كود لتنظيف قاعدة البيانات
            QMessageBox.information(
                self,
                "تم التنظيف",
                "تم تنظيف قاعدة البيانات بنجاح!"
            )

    def reset_database(self):
        """إعادة تعيين قاعدة البيانات"""
        reply = QMessageBox.warning(
            self,
            "إعادة تعيين قاعدة البيانات",
            "تحذير: هذه العملية ستحذف جميع البيانات في قاعدة البيانات وتعيد إنشاء الجداول! هذه العملية لا يمكن التراجع عنها. هل أنت متأكد من المتابعة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # طلب تأكيد إضافي
            confirmation = QMessageBox.warning(
                self,
                "تأكيد إضافي",
                "هذه العملية ستحذف جميع البيانات بشكل نهائي. اكتب 'نعم' للتأكيد:",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if confirmation == QMessageBox.Yes:
                # هنا يمكن إضافة كود لإعادة تعيين قاعدة البيانات
                QMessageBox.information(
                    self,
                    "تمت إعادة التعيين",
                    "تم إعادة تعيين قاعدة البيانات بنجاح!"
                )

    def create_backup_tab(self):
        """إنشاء تبويب إعدادات النسخ الاحتياطي"""
        tab = QWidget()
        tab.setLayoutDirection(Qt.RightToLeft)

        # إنشاء منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        # إنشاء محتوى التبويب
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(15, 15, 15, 15)
        content_layout.setSpacing(15)

        # قسم إعدادات النسخ الاحتياطي التلقائي
        auto_backup_group = QGroupBox("النسخ الاحتياطي التلقائي")
        auto_backup_layout = QFormLayout()
        auto_backup_layout.setLabelAlignment(Qt.AlignRight)
        auto_backup_layout.setSpacing(12)

        # تفعيل النسخ الاحتياطي التلقائي
        self.auto_backup = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        self.auto_backup.setObjectName("checkbox")
        self.auto_backup.setChecked(True)
        self.auto_backup.stateChanged.connect(self.on_auto_backup_changed)
        auto_backup_layout.addRow("", self.auto_backup)

        # فترة النسخ الاحتياطي
        self.backup_period_layout = QHBoxLayout()
        self.backup_period = QSpinBox()
        self.backup_period.setObjectName("search_input")
        self.backup_period.setRange(1, 60)
        self.backup_period.setValue(7)

        self.backup_period_unit = RTLComboBox()
        self.backup_period_unit.setObjectName("combo_box")
        self.backup_period_unit.addItems(["يوم", "أسبوع", "شهر"])

        self.backup_period_layout.addWidget(self.backup_period)
        self.backup_period_layout.addWidget(self.backup_period_unit)

        self.period_row = auto_backup_layout.addRow("فترة النسخ:", self.backup_period_layout)

        # مسار حفظ النسخ الاحتياطية
        self.backup_path_layout = QHBoxLayout()
        self.backup_path = QLineEdit()
        self.backup_path.setObjectName("search_input")
        self.backup_path.setPlaceholderText("مسار حفظ النسخ الاحتياطية")
        self.backup_path.setText("backups/")

        browse_backup_path_btn = QPushButton("استعراض...")
        browse_backup_path_btn.setObjectName("secondary_button")
        browse_backup_path_btn.clicked.connect(self.browse_backup_path)

        self.backup_path_layout.addWidget(self.backup_path)
        self.backup_path_layout.addWidget(browse_backup_path_btn)

        self.path_row = auto_backup_layout.addRow("مسار الحفظ:", self.backup_path_layout)

        # عدد النسخ الاحتياطية المحتفظ بها
        self.max_backups = QSpinBox()
        self.max_backups.setObjectName("search_input")
        self.max_backups.setRange(1, 100)
        self.max_backups.setValue(5)
        self.max_backups_row = auto_backup_layout.addRow("عدد النسخ المحتفظ بها:", self.max_backups)

        # ضغط ملفات النسخ الاحتياطي
        self.compress_backup = QCheckBox("ضغط ملفات النسخ الاحتياطي")
        self.compress_backup.setObjectName("checkbox")
        self.compress_backup.setChecked(True)
        auto_backup_layout.addRow("", self.compress_backup)

        auto_backup_group.setLayout(auto_backup_layout)
        content_layout.addWidget(auto_backup_group)

        # قسم النسخ الاحتياطي اليدوي
        manual_backup_group = QGroupBox("النسخ الاحتياطي اليدوي")
        manual_backup_layout = QVBoxLayout()

        # نص تعليمات
        manual_backup_info = QLabel("يمكنك إنشاء نسخة احتياطية يدوياً من قاعدة البيانات أو استعادة نسخة موجودة.")
        manual_backup_info.setObjectName("hint_label")
        manual_backup_info.setWordWrap(True)
        manual_backup_layout.addWidget(manual_backup_info)

        # أزرار النسخ الاحتياطي
        backup_buttons_layout = QHBoxLayout()

        create_backup_btn = QPushButton("إنشاء نسخة احتياطية الآن")
        create_backup_btn.setObjectName("action_button")
        create_backup_btn.clicked.connect(self.create_manual_backup)

        restore_backup_btn = QPushButton("استعادة نسخة احتياطية")
        restore_backup_btn.setObjectName("secondary_button")
        restore_backup_btn.clicked.connect(self.restore_backup)

        backup_buttons_layout.addWidget(create_backup_btn)
        backup_buttons_layout.addWidget(restore_backup_btn)
        backup_buttons_layout.addStretch()

        manual_backup_layout.addLayout(backup_buttons_layout)
        manual_backup_group.setLayout(manual_backup_layout)
        content_layout.addWidget(manual_backup_group)

        # قسم النسخ الاحتياطية الموجودة
        existing_backups_group = QGroupBox("النسخ الاحتياطية الموجودة")
        existing_backups_layout = QVBoxLayout()

        # جدول النسخ الاحتياطية
        self.backups_table = QTableWidget()
        self.backups_table.setColumnCount(4)
        self.backups_table.setHorizontalHeaderLabels(["اسم الملف", "تاريخ الإنشاء", "الحجم", "الإجراءات"])

        # ضبط خصائص الجدول
        self.backups_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.backups_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.backups_table.verticalHeader().setVisible(False)
        self.backups_table.setAlternatingRowColors(True)
        self.backups_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.backups_table.setSelectionBehavior(QTableWidget.SelectRows)

        # إضافة بيانات تجريبية
        backup_files = [
            {"name": "backup_2025-02-15.db", "date": "2025/02/15 10:30", "size": "2.4 MB"},
            {"name": "backup_2025-02-08.db", "date": "2025/02/08 09:15", "size": "2.3 MB"},
            {"name": "backup_2025-02-01.db", "date": "2025/02/01 08:45", "size": "2.1 MB"},
        ]

        for row, backup in enumerate(backup_files):
            self.backups_table.insertRow(row)
            self.backups_table.setItem(row, 0, QTableWidgetItem(backup["name"]))
            self.backups_table.setItem(row, 1, QTableWidgetItem(backup["date"]))
            self.backups_table.setItem(row, 2, QTableWidgetItem(backup["size"]))

            # إنشاء خلية للإجراءات
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(4, 2, 4, 2)
            actions_layout.setSpacing(4)

            restore_btn = QPushButton("استعادة")
            restore_btn.setObjectName("secondary_button")
            restore_btn.setFixedSize(60, 30)
            restore_btn.setFont(QFont("Arial", 8))

            delete_btn = QPushButton("حذف")
            delete_btn.setObjectName("danger_button")
            delete_btn.setFixedSize(50, 30)
            delete_btn.setFont(QFont("Arial", 8))

            actions_layout.addWidget(restore_btn)
            actions_layout.addWidget(delete_btn)

            self.backups_table.setCellWidget(row, 3, actions_widget)

        existing_backups_layout.addWidget(self.backups_table)

        # زر تحديث القائمة
        refresh_btn = QPushButton("تحديث القائمة")
        refresh_btn.setObjectName("secondary_button")
        refresh_btn.clicked.connect(self.refresh_backups_list)

        refresh_layout = QHBoxLayout()
        refresh_layout.addStretch()
        refresh_layout.addWidget(refresh_btn)

        existing_backups_layout.addLayout(refresh_layout)
        existing_backups_group.setLayout(existing_backups_layout)
        content_layout.addWidget(existing_backups_group)

        # إضافة مسافة قابلة للتمدد في نهاية التبويب
        content_layout.addStretch()

        # تعيين المحتوى للمنطقة القابلة للتمرير
        scroll_area.setWidget(content_widget)

        # إضافة المنطقة القابلة للتمرير إلى التبويب
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.addWidget(scroll_area)

        return tab

    def on_auto_backup_changed(self, state):
        """تحديث حالة إدخالات النسخ الاحتياطي بناء على حالة الخيار"""
        enabled = state == Qt.Checked
        self.backup_period.setEnabled(enabled)
        self.backup_period_unit.setEnabled(enabled)
        self.backup_path.setEnabled(enabled)
        self.max_backups.setEnabled(enabled)

    def browse_backup_path(self):
        """اختيار مسار حفظ النسخ الاحتياطية"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "اختر مسار حفظ النسخ الاحتياطية",
            ""
        )
        if dir_path:
            self.backup_path.setText(dir_path)

    def create_manual_backup(self):
        """إنشاء نسخة احتياطية يدوياً"""
        # هنا يمكن إضافة كود لإنشاء نسخة احتياطية

        # عرض رسالة تأكيد
        QMessageBox.information(
            self,
            "تم بنجاح",
            "تم إنشاء نسخة احتياطية بنجاح.\nاسم الملف: backup_2025-02-16.db\nالمسار: backups/"
        )

        # تحديث قائمة النسخ الاحتياطية
        self.refresh_backups_list()

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف النسخة الاحتياطية",
            "",
            "قواعد بيانات (*.db *.sqlite);;كل الملفات (*.*)"
        )
        if file_name:
            # التأكيد قبل الاستعادة
            reply = QMessageBox.warning(
                self,
                "تأكيد الاستعادة",
                f"هل أنت متأكد من استعادة النسخة الاحتياطية من الملف:\n{file_name}\n\nستتم استبدال البيانات الحالية بالكامل!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # هنا يمكن إضافة كود لاستعادة النسخة الاحتياطية
                QMessageBox.information(
                    self,
                    "تمت الاستعادة",
                    "تم استعادة النسخة الاحتياطية بنجاح!"
                )

    def refresh_backups_list(self):
        """تحديث قائمة النسخ الاحتياطية"""
        # هنا يمكن إضافة كود للبحث عن ملفات النسخ الاحتياطية وتحديث الجدول

        # تم إزالة رسالة التأكيد للتحسين من تجربة المستخدم
        # QMessageBox.information(
        #     self,
        #     "تم التحديث",
        #     "تم تحديث قائمة النسخ الاحتياطية"
        # )
        # يمكن بدلاً من ذلك عرض رسالة في شريط الحالة إذا لزم الأمر
        window = self.window()
        status_bar = window.statusBar if hasattr(window, 'statusBar') else None
        if status_bar:
            status_bar.showMessage("تم تحديث قائمة النسخ الاحتياطية", 2000)



    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        # إعدادات الفاتورة
        self.company_name.setText(self.settings.value("company_name", DEFAULT_SETTINGS["company_name"]))
        self.company_phone.setText(self.settings.value("company_phone", DEFAULT_SETTINGS["company_phone"]))

        self.company_address.setText(self.settings.value("company_address", DEFAULT_SETTINGS["company_address"]))
        self.invoice_notes.setText(self.settings.value("invoice_notes", DEFAULT_SETTINGS["invoice_notes"]))

        # إعدادات قاعدة البيانات SQLite
        self.db_path_input.setText(self.settings.value("db_path", "database.db"))

    def save_settings(self):
        """حفظ إعدادات التطبيق"""
        # جمع إعدادات الفاتورة
        invoice_settings = {
            "company_name": self.company_name.text(),
            "company_phone": self.company_phone.text(),

            "company_address": self.company_address.text(),
            "invoice_notes": self.invoice_notes.text()
        }

        # جمع إعدادات قاعدة البيانات SQLite
        db_settings = {
            "db_type": "sqlite",
            "db_path": self.db_path_input.text()
        }

        # التحقق من صحة مسار قاعدة البيانات SQLite
        if not self.db_path_input.text():
            QMessageBox.warning(
                self,
                "خطأ في الإعدادات",
                "يرجى تحديد مسار ملف قاعدة البيانات SQLite.",
                QMessageBox.Ok
            )
            return

        # حفظ إعدادات الفاتورة
        for key, value in invoice_settings.items():
            self.settings.setValue(key, value)

        # حفظ إعدادات قاعدة البيانات باستخدام وحدة التحكم
        success, message = DatabaseController.save_settings(db_settings)

        if success:
            QMessageBox.information(
                self,
                "تم حفظ الإعدادات",
                "تم حفظ الإعدادات بنجاح.\nقد تحتاج إلى إعادة تشغيل التطبيق لتطبيق جميع التغييرات.",
                QMessageBox.Ok
            )

            # إعادة تحميل الإعدادات في واجهة المستخدم
            self.load_settings()
        else:
            QMessageBox.critical(
                self,
                "خطأ في الحفظ",
                f"حدث خطأ أثناء حفظ إعدادات قاعدة البيانات: {message}",
                QMessageBox.Ok
            )

    def apply_styles(self):
        """تطبيق الأنماط على العناصر"""
        # استخدام التنسيقات من ملف الستايلات بدلاً من التعريف المحلي
        self.setStyleSheet(AppStyles.get_all_view_styles() + """
            QGroupBox {
                font-weight: bold;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 10px;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }

            QCheckBox {
                spacing: 8px;
            }

            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)

    def refresh_page(self):
        """تحديث بيانات الصفحة عند الانتقال إليها أو عند فتح التطبيق"""
        # تحديث قائمة النسخ الاحتياطية
        self.refresh_backups_list()

        # إعادة تحميل الإعدادات
        self.load_settings()

        print("تم تحديث صفحة الإعدادات")

    def add_user(self):
        """إضافة مستخدم جديد"""
        try:
            dialog = QDialog(self)
            dialog.setWindowTitle("إضافة مستخدم جديد")
            dialog.setMinimumWidth(400)

            # تخطيط النموذج
            form_layout = QFormLayout(dialog)

            # حقول المستخدم
            username = QLineEdit()
            username.setObjectName("search_input")
            username.setPlaceholderText("اسم المستخدم")

            full_name = QLineEdit()
            full_name.setObjectName("search_input")
            full_name.setPlaceholderText("الاسم الكامل")

            password = QLineEdit()
            password.setObjectName("search_input")
            password.setPlaceholderText("كلمة المرور")
            password.setEchoMode(QLineEdit.Password)

            confirm_password = QLineEdit()
            confirm_password.setObjectName("search_input")
            confirm_password.setPlaceholderText("تأكيد كلمة المرور")
            confirm_password.setEchoMode(QLineEdit.Password)

            role_combo = RTLComboBox()
            role_combo.addItems(["مدير", "كاشير", "مدير مخزون"])
            role_combo.setObjectName("search_input")

            # إضافة الحقول إلى التخطيط
            form_layout.addRow("اسم المستخدم:", username)
            form_layout.addRow("الاسم الكامل:", full_name)
            form_layout.addRow("كلمة المرور:", password)
            form_layout.addRow("تأكيد كلمة المرور:", confirm_password)
            form_layout.addRow("الصلاحية:", role_combo)

            # أزرار الإجراءات
            button_box = QHBoxLayout()
            save_btn = QPushButton("إضافة المستخدم")
            save_btn.setObjectName("action_button")

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.setObjectName("secondary_button")

            button_box.addWidget(save_btn)
            button_box.addWidget(cancel_btn)

            form_layout.addRow("", button_box)

            # ربط الأزرار بالوظائف
            cancel_btn.clicked.connect(dialog.reject)

            # وظيفة إضافة المستخدم
            def create_user():
                # التحقق من الإدخال
                if not username.text() or not full_name.text() or not password.text() or not confirm_password.text():
                    QMessageBox.warning(dialog, "بيانات غير مكتملة", "يرجى ملء جميع الحقول")
                    return

                # التحقق من تطابق كلمة المرور
                if password.text() != confirm_password.text():
                    QMessageBox.warning(dialog, "كلمات المرور غير متطابقة", "كلمة المرور وتأكيدها غير متطابقين")
                    return

                # إضافة المستخدم إلى قاعدة البيانات
                success, message = UserController.create_user(
                    username.text(),
                    full_name.text(),
                    password.text(),
                    role_combo.currentText()
                )

                if success:
                    QMessageBox.information(dialog, "تمت الإضافة", message)
                    self.load_users_data()  # إعادة تحميل بيانات المستخدمين
                    dialog.accept()
                else:
                    QMessageBox.warning(dialog, "خطأ", message)

            save_btn.clicked.connect(create_user)

            # عرض الحوار
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة مستخدم جديد: {str(e)}")

    def edit_user(self):
        """تعديل المستخدم المحدد"""
        try:
            # التحقق من اختيار صف في الجدول
            selected_rows = self.users_table.selectedItems()
            if not selected_rows:
                QMessageBox.warning(self, "تحذير", "الرجاء اختيار مستخدم لتعديله")
                return

            # الحصول على معرف المستخدم المحدد
            selected_row = self.users_table.currentRow()
            user_id = self.users_table.item(selected_row, 0).data(Qt.UserRole)

            # الحصول على معلومات المستخدم
            user_info = UserController.get_user_info(user_id=user_id)
            if not user_info:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على معلومات المستخدم")
                return

            # إنشاء نافذة الحوار لتعديل المستخدم
            dialog = QDialog(self)
            dialog.setWindowTitle("تعديل المستخدم")
            dialog.setMinimumWidth(400)

            # تخطيط النموذج
            form_layout = QFormLayout(dialog)

            # حقول المستخدم
            username = QLineEdit()
            username.setObjectName("search_input")
            username.setText(user_info['username'])
            if user_info['username'] == 'admin':  # منع تغيير اسم المستخدم للمدير الرئيسي
                username.setEnabled(False)

            full_name = QLineEdit()
            full_name.setObjectName("search_input")
            full_name.setText(user_info['full_name'])

            role_combo = RTLComboBox()
            role_combo.addItems(["مدير", "كاشير", "مدير مخزون"])
            role_combo.setCurrentText(user_info['role'])
            role_combo.setObjectName("search_input")
            if user_info['username'] == 'admin':  # منع تغيير صلاحية المدير الرئيسي
                role_combo.setEnabled(False)

            active_checkbox = QCheckBox("مستخدم نشط")
            active_checkbox.setChecked(user_info['is_active'])

            # إضافة الحقول إلى التخطيط
            form_layout.addRow("اسم المستخدم:", username)
            form_layout.addRow("الاسم الكامل:", full_name)
            form_layout.addRow("الصلاحية:", role_combo)
            form_layout.addRow("", active_checkbox)

            # أزرار الإجراءات
            button_box = QHBoxLayout()
            save_btn = QPushButton("حفظ التغييرات")
            save_btn.setObjectName("action_button")

            reset_password_btn = QPushButton("إعادة تعيين كلمة المرور")
            reset_password_btn.setObjectName("secondary_button")

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.setObjectName("secondary_button")

            button_box.addWidget(save_btn)
            button_box.addWidget(reset_password_btn)
            button_box.addWidget(cancel_btn)

            form_layout.addRow("", button_box)

            # ربط الأزرار بالوظائف
            cancel_btn.clicked.connect(dialog.reject)

            # وظيفة تحديث المستخدم
            def update_user():
                # التحقق من الإدخال
                if not username.text() or not full_name.text():
                    QMessageBox.warning(dialog, "بيانات غير مكتملة", "يرجى ملء جميع الحقول")
                    return

                # تحديث معلومات المستخدم
                success, message = UserController.update_user(
                    user_id,
                    username.text(),
                    full_name.text(),
                    role_combo.currentText(),
                    active_checkbox.isChecked()
                )

                if success:
                    QMessageBox.information(dialog, "تم التحديث", message)
                    self.load_users_data()  # إعادة تحميل بيانات المستخدمين
                    dialog.accept()
                else:
                    QMessageBox.warning(dialog, "خطأ", message)

            save_btn.clicked.connect(update_user)

            # وظيفة إعادة تعيين كلمة المرور
            def reset_password():
                password_dialog = QDialog(dialog)
                password_dialog.setWindowTitle("إعادة تعيين كلمة المرور")
                password_dialog.setMinimumWidth(400)

                password_layout = QFormLayout(password_dialog)

                # حقول كلمة المرور
                new_password = QLineEdit()
                new_password.setObjectName("search_input")
                new_password.setPlaceholderText("كلمة المرور الجديدة")
                new_password.setEchoMode(QLineEdit.Password)

                confirm_new_password = QLineEdit()
                confirm_new_password.setObjectName("search_input")
                confirm_new_password.setPlaceholderText("تأكيد كلمة المرور الجديدة")
                confirm_new_password.setEchoMode(QLineEdit.Password)

                # أزرار الإجراءات
                password_buttons = QHBoxLayout()
                reset_btn = QPushButton("تغيير كلمة المرور")
                reset_btn.setObjectName("action_button")

                cancel_reset_btn = QPushButton("إلغاء")
                cancel_reset_btn.setObjectName("secondary_button")

                password_buttons.addWidget(reset_btn)
                password_buttons.addWidget(cancel_reset_btn)

                # إضافة الحقول والأزرار إلى التخطيط
                password_layout.addRow("كلمة المرور الجديدة:", new_password)
                password_layout.addRow("تأكيد كلمة المرور:", confirm_new_password)
                password_layout.addRow("", password_buttons)

                # ربط الأزرار بالوظائف
                cancel_reset_btn.clicked.connect(password_dialog.reject)

                # وظيفة تغيير كلمة المرور
                def do_reset_password():
                    # التحقق من الإدخال
                    if not new_password.text() or not confirm_new_password.text():
                        QMessageBox.warning(password_dialog, "بيانات غير مكتملة", "يرجى ملء جميع الحقول")
                        return

                    # التحقق من تطابق كلمة المرور
                    if new_password.text() != confirm_new_password.text():
                        QMessageBox.warning(password_dialog, "كلمات المرور غير متطابقة", "كلمة المرور وتأكيدها غير متطابقين")
                        return

                    # تغيير كلمة المرور
                    success, message = UserController.reset_password(user_id, new_password.text())

                    if success:
                        QMessageBox.information(password_dialog, "تم تغيير كلمة المرور", message)
                        password_dialog.accept()
                    else:
                        QMessageBox.warning(password_dialog, "خطأ", message)

                reset_btn.clicked.connect(do_reset_password)

                # عرض الحوار
                password_dialog.exec_()

            reset_password_btn.clicked.connect(reset_password)

            # عرض الحوار
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل المستخدم: {str(e)}")

    def delete_user(self):
        """حذف المستخدم المحدد"""
        try:
            # التحقق من اختيار صف في الجدول
            selected_rows = self.users_table.selectedItems()
            if not selected_rows:
                QMessageBox.warning(self, "تحذير", "الرجاء اختيار مستخدم لحذفه")
                return

            # الحصول على معلومات المستخدم المحدد
            selected_row = self.users_table.currentRow()
            user_id = self.users_table.item(selected_row, 0).data(Qt.UserRole)
            username = self.users_table.item(selected_row, 0).text()

            # منع حذف المستخدم admin
            if username == 'admin':
                QMessageBox.warning(self, "تحذير", "لا يمكن حذف المستخدم الرئيسي (admin)")
                return

            # رسالة تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف المستخدم '{username}'؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # حذف المستخدم
                success, message = UserController.delete_user(user_id)

                if success:
                    QMessageBox.information(self, "تم الحذف", message)
                    self.load_users_data()  # إعادة تحميل بيانات المستخدمين
                else:
                    QMessageBox.warning(self, "خطأ", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف المستخدم: {str(e)}")

    def on_permission_changed(self, row, col, state):
        """تغيير صلاحيات المستخدم عند تغيير قيمة checkbox"""
        # تمييز أن هناك تغييرات لم يتم حفظها
        role_name = self.permissions_table.horizontalHeaderItem(col).text()
        permission_name = self.permissions_table.item(row, 0).text()

        print(f"تم تغيير صلاحية '{permission_name}' للدور '{role_name}' إلى {state > 0}")

    def save_permissions(self):
        """حفظ تغييرات الصلاحيات في قاعدة البيانات"""
        try:
            # إنشاء قاموس لتخزين الصلاحيات
            permissions = {}

            # استرجاع الصلاحيات من الجدول
            for row in range(self.permissions_table.rowCount()):
                permission_name = self.permissions_table.item(row, 0).text()

                for col in range(1, 4):  # الأعمدة 1, 2, 3 (مدير، كاشير، مدير مخزون)
                    role_name = self.permissions_table.horizontalHeaderItem(col).text()

                    # الحصول على حالة الـ checkbox
                    cell_widget = self.permissions_table.cellWidget(row, col)
                    checkbox = cell_widget.layout().itemAt(0).widget()
                    is_allowed = checkbox.isChecked()

                    # إضافة الصلاحية إلى القاموس
                    if role_name not in permissions:
                        permissions[role_name] = {}

                    permissions[role_name][permission_name] = is_allowed

            # حفظ الصلاحيات في قاعدة البيانات
            success, message = UserController.save_role_permissions(permissions)

            if success:
                QMessageBox.information(self, "تم الحفظ", message)
            else:
                QMessageBox.warning(self, "خطأ", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الصلاحيات: {str(e)}")
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ استعلام متعدد: no such column: name
الاستعلام: 
                SELECT name FROM expense_categories
                ORDER BY usage_count DESC, name ASC
            
المعلمات: None
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('إيجار', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('كهرباء', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('مياه', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('غاز', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('هاتف', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('إنترنت', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('مواصلات', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('وقود', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('صيانة', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('تنظيف', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('أدوات مكتبية', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('رواتب', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('مكافآت', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('تأمينات', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('ضرائب', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('رسوم حكومية', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('دعاية وإعلان', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('استشارات', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('تدريب', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('مؤتمرات', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('مواد خام', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('تعبئة وتغليف', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('شحن وتوصيل', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('مصاريف بنكية', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('أخرى', '2025-05-28 11:52:33', '2025-05-28 11:52:33')
2025-05-28 11:52:33 - DATABASE ERROR: خطأ في تنفيذ استعلام متعدد: no such column: name
الاستعلام: 
                SELECT name FROM expense_categories
                ORDER BY usage_count DESC, name ASC
            
المعلمات: None
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ استعلام متعدد: no such column: name
الاستعلام: 
                SELECT name FROM expense_categories
                ORDER BY usage_count DESC, name ASC
            
المعلمات: None
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('إيجار', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('كهرباء', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('مياه', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('غاز', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('هاتف', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('إنترنت', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('مواصلات', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('وقود', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('صيانة', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('تنظيف', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('أدوات مكتبية', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('رواتب', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('مكافآت', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('تأمينات', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('ضرائب', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('رسوم حكومية', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('دعاية وإعلان', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('استشارات', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('تدريب', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('مؤتمرات', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('مواد خام', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('تعبئة وتغليف', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('شحن وتوصيل', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('مصاريف بنكية', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT OR IGNORE INTO expense_categories (name, is_default, created_at, updated_at)
                    VALUES (?, 1, ?, ?)
                
المعلمات: ('أخرى', '2025-05-28 19:43:09', '2025-05-28 19:43:09')
2025-05-28 19:43:09 - DATABASE ERROR: خطأ في تنفيذ استعلام متعدد: no such column: name
الاستعلام: 
                SELECT name FROM expense_categories
                ORDER BY usage_count DESC, name ASC
            
المعلمات: None
2025-05-28 19:43:10 - DATABASE ERROR: خطأ في تنفيذ استعلام واحد: no such column: usage_count
الاستعلام: SELECT id, usage_count FROM expense_categories WHERE name = ?
المعلمات: ('أخرى',)
2025-05-28 19:43:10 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT INTO expense_categories (name, is_default, usage_count, created_at, updated_at)
                    VALUES (?, 0, 1, ?, ?)
                
المعلمات: ('أخرى', '2025-05-28 19:43:10', '2025-05-28 19:43:10')
2025-05-28 19:43:10 - DATABASE ERROR: خطأ في تنفيذ استعلام واحد: no such column: usage_count
الاستعلام: SELECT id, usage_count FROM expense_categories WHERE name = ?
المعلمات: ('أخرى',)
2025-05-28 19:43:10 - DATABASE ERROR: خطأ في تنفيذ الاستعلام: table expense_categories has no column named name
الاستعلام: 
                    INSERT INTO expense_categories (name, is_default, usage_count, created_at, updated_at)
                    VALUES (?, 0, 1, ?, ?)
                
المعلمات: ('أخرى', '2025-05-28 19:43:10', '2025-05-28 19:43:10')
2025-05-31 21:26:29 - DATABASE ERROR: العميل غير موجود: عميل غير مسجل
2025-05-31 21:29:51 - DATABASE ERROR: خطأ في تحديث الفاتورة مع العناصر: type object 'DateTimeUtils' has no attribute 'get_current_datetime'
2025-05-31 21:29:53 - DATABASE ERROR: خطأ في تحديث الفاتورة مع العناصر: type object 'DateTimeUtils' has no attribute 'get_current_datetime'
2025-05-31 21:30:44 - DATABASE ERROR: خطأ في تحديث الفاتورة مع العناصر: type object 'DateTimeUtils' has no attribute 'get_current_datetime'
2025-05-31 21:34:38 - DATABASE ERROR: العميل غير موجود: None
2025-05-31 22:04:16 - DATABASE ERROR: العميل غير موجود: None
2025-05-31 22:04:19 - DATABASE ERROR: العميل غير موجود: None

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QFrame,
                             QTableWidget, QTableWidgetItem, QHeaderView, QComboBox, QLineEdit,
                             QGridLayout, QSpacerItem, QSizePolicy, QAbstractItemView, QDialog,
                             QFormLayout, QMessageBox, QDoubleSpinBox, QSpinBox, QMenu, QAction,
                             QCheckBox, QTabWidget)


# Define RTL helper classes directly
class RTLComboBox(QComboBox):
    """Replacement class for RTLComboBox"""
    pass

def apply_rtl_to_all_widgets(widget):
    """Replacement function for apply_rtl_to_all_widgets"""
    pass

from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QFont, QIcon, QColor
from styles import AppStyles
from models.products import ProductModel
import datetime  # Importación añadida para el uso en add_inventory_record

class ProductsView(QWidget):
    def __init__(self):
        super().__init__()

        # بدلًا من البيانات النموذجية، سنقوم بتحميل البيانات من قاعدة البيانات
        self.products_data = []

        # قائمة التصنيفات - سيتم تحميلها من قاعدة البيانات
        self.categories = ["الكل"]

        # سجل حركة المخزون (سيتم استخدامه لتتبع الإضافات والتعديلات)
        self.inventory_history = []

        # إعداد التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)

        # إضافة عنوان الصفحة
        header_layout = QHBoxLayout()
        page_title = QLabel("المنتجات والخدمات")
        page_title.setObjectName("page_title")
        page_title.setFont(QFont("Arial", 20, QFont.Bold))
        header_layout.addWidget(page_title)

        # إضافة زر لإضافة منتج جديد
        self.new_product_btn = QPushButton("📦  منتج جديد")
        self.new_product_btn.setFixedSize(180, 40)
        self.new_product_btn.setObjectName("secondary_button")
        self.new_product_btn.setFont(QFont("Arial", 11))
        self.new_product_btn.setCursor(Qt.PointingHandCursor)
        self.new_product_btn.clicked.connect(self.add_new_product)

        # إضافة زر لإضافة خدمة جديدة
        self.new_service_btn = QPushButton("🛠️  خدمة جديدة")
        self.new_service_btn.setFixedSize(180, 40)
        self.new_service_btn.setObjectName("secondary_button")
        self.new_service_btn.setFont(QFont("Arial", 11))
        self.new_service_btn.setCursor(Qt.PointingHandCursor)
        self.new_service_btn.clicked.connect(self.add_new_service)

        # إضافة زر لعرض التصنيفات
        self.categories_btn = QPushButton("📋  التصنيفات")
        self.categories_btn.setFixedSize(180, 40)
        self.categories_btn.setObjectName("secondary_button")
        self.categories_btn.setFont(QFont("Arial", 11))
        self.categories_btn.setCursor(Qt.PointingHandCursor)
        self.categories_btn.clicked.connect(self.show_categories)

        header_layout.addStretch()
        header_layout.addWidget(self.categories_btn)
        header_layout.addSpacing(10)
        header_layout.addWidget(self.new_service_btn)
        header_layout.addSpacing(10)
        header_layout.addWidget(self.new_product_btn)

        layout.addLayout(header_layout)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("content_separator")
        layout.addWidget(separator)
        layout.addSpacing(20)

        # إضافة منطقة البحث والتصفية
        search_layout = QHBoxLayout()

        # مربع البحث
        search_label = QLabel("بحث:")
        search_label.setObjectName("field_label")
        self.search_input = QLineEdit()
        self.search_input.setObjectName("search_input")
        self.search_input.setPlaceholderText("ابحث عن منتج أو خدمة...")
        self.search_input.setClearButtonEnabled(True)
        self.search_input.setMinimumHeight(40)
        self.search_input.textChanged.connect(self.filter_products)

        # قائمة التصنيفات
        category_label = QLabel("التصنيف:")
        category_label.setObjectName("field_label")
        self.category_combo = RTLComboBox()
        self.category_combo.setObjectName("combo_box")
        self.category_combo.setFixedHeight(40)
        self.category_combo.setMinimumWidth(150)
        self.category_combo.setLayoutDirection(Qt.RightToLeft)
        self.category_combo.currentIndexChanged.connect(self.filter_products)

        # قائمة نوع المنتج (منتج/خدمة)
        type_label = QLabel("النوع:")
        type_label.setObjectName("field_label")
        self.type_combo = RTLComboBox()
        self.type_combo.setObjectName("combo_box")
        self.type_combo.setFixedHeight(40)
        self.type_combo.setMinimumWidth(120)
        self.type_combo.setLayoutDirection(Qt.RightToLeft)
        self.type_combo.addItems(["الكل", "منتجات", "خدمات"])
        self.type_combo.currentIndexChanged.connect(self.filter_products)

        # إضافة عناصر التصفية إلى التخطيط
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addSpacing(20)
        search_layout.addWidget(category_label)
        search_layout.addWidget(self.category_combo)
        search_layout.addSpacing(20)
        search_layout.addWidget(type_label)
        search_layout.addWidget(self.type_combo)

        layout.addLayout(search_layout)
        layout.addSpacing(15)

        # إضافة جدول المنتجات
        self.products_table = QTableWidget()
        self.products_table.setObjectName("products_table")
        self.products_table.setColumnCount(6)  # رقم المنتج, الكود, اسم المنتج, السعر, التصنيف, المخزون
        self.products_table.setHorizontalHeaderLabels(["#", "الكود", "اسم المنتج", "السعر", "التصنيف", "المخزون"])
        self.products_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.products_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.products_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.products_table.verticalHeader().setVisible(False)
        self.products_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.products_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.products_table.setSelectionMode(QAbstractItemView.SingleSelection)
        # تفعيل خاصية الألوان المتبادلة للصفوف مثل صفحة التقارير
        self.products_table.setAlternatingRowColors(False)

        # إضافة نمط مخصص للجدول لتوحيد لون الخلفية لجميع الصفوف
        self.products_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
            }
            QTableWidget::item {
                background-color: #f8fafc;  /* نفس اللون الغامق المستخدم في جدول العملاء */
            }
            QTableWidget::item:selected {
                background-color: rgba(59, 130, 246, 0.3);
                color: #0f172a;
            }
        """)

        # إضافة قائمة السياق
        self.products_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.products_table.customContextMenuRequested.connect(self.show_context_menu)

        # إضافة النقر المزدوج لفتح تعديل المنتج
        self.products_table.doubleClicked.connect(self.on_table_double_clicked)

        # تعديل: تكبير حجم صفوف الجدول لتكون أكثر قابلية للقراءة
        self.products_table.verticalHeader().setDefaultSectionSize(40)

        layout.addWidget(self.products_table)

        # إضافة تعليمات للمستخدم
        hint_label = QLabel("ملاحظة: يمكنك النقر بزر الماوس الأيمن على أي منتج للوصول إلى قائمة الخيارات")
        hint_label.setObjectName("hint_label")
        layout.addWidget(hint_label)

        # إضافة معلومات إحصائية
        stats_layout = QHBoxLayout()
        self.total_products_label = QLabel("إجمالي المنتجات: 0")
        self.total_products_label.setObjectName("stats_label")

        stats_layout.addWidget(self.total_products_label)
        stats_layout.addStretch()

        layout.addLayout(stats_layout)

        # تحميل الفئات من قاعدة البيانات
        self.load_categories()

        # تحميل وتصفية المنتجات (يضمن عدم ظهور منتجات التصنيف الوهمية)
        self.filter_products()

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def load_categories(self):
        """تحميل فئات المنتجات من قاعدة البيانات"""
        try:
            db_categories = ProductModel.get_product_categories()
            self.categories = ["الكل"] + db_categories

            # تحديث قائمة الفئات
            self.category_combo.clear()
            self.category_combo.addItems(self.categories)
        except Exception as e:
            QMessageBox.warning(self, "خطأ في تحميل الفئات", f"حدث خطأ أثناء تحميل فئات المنتجات: {str(e)}")

    def show_context_menu(self, position):
        """عرض قائمة السياق عند النقر بزر الماوس الأيمن"""
        # التحقق من وجود صف محدد
        selected_indexes = self.products_table.selectedIndexes()
        if not selected_indexes:
            return

        # الحصول على صف المنتج المحدد
        row = selected_indexes[0].row()
        product_id = int(self.products_table.item(row, 0).text())
        product_name = self.products_table.item(row, 2).text().replace(" 🛠️", "")  # إزالة رمز الخدمة إن وجد

        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            user_id = None

            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')

            # استيراد وحدة التحكم بالمستخدمين
            from controllers.user_controller import UserController

            # الحصول على بيانات المنتج من قاعدة البيانات
            product = ProductModel.get_product_by_id(product_id)
            product_type = product.get('product_type', 'physical')

            # إنشاء قائمة السياق
            context_menu = QMenu(self)
            context_menu.setLayoutDirection(Qt.RightToLeft)

            # إضافة عنوان المنتج
            title_action = QAction(f"{'الخدمة' if product_type == 'service' else 'المنتج'}: {product_name}", self)
            title_action.setEnabled(False)
            title_font = title_action.font()
            title_font.setBold(True)
            title_action.setFont(title_font)
            context_menu.addAction(title_action)

            # إضافة فاصل
            context_menu.addSeparator()

            # إضافة إجراءات
            # عرض خيار إضافة للمخزون فقط للمنتجات العادية وليس للخدمات
            if product_type == 'physical':
                if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "تعديل مخزون"):
                    add_stock_action = QAction("📥  إضافة للمخزون", self)
                    add_stock_action.triggered.connect(lambda: self.add_stock_to_product(product_id))
                    context_menu.addAction(add_stock_action)

            if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "تعديل منتج"):
                edit_action = QAction("✏️  تعديل", self)
                edit_action.triggered.connect(lambda: self.edit_product(product_id))
                context_menu.addAction(edit_action)

            # تحديد حالة المفضلة الحالية للمنتج
            is_favorite = product.get('is_favorite', 0)

            # إضافة الإجراء المناسب بناءً على حالة المفضلة
            if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "تعديل منتج"):
                if is_favorite:
                    favorite_action = QAction("⭐  إزالة من المفضلة", self)
                else:
                    favorite_action = QAction("⭐  إضافة للمفضلة", self)

                favorite_action.triggered.connect(lambda: self.toggle_favorite_product(product_id))
                context_menu.addAction(favorite_action)

            if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "حذف منتج"):
                delete_action = QAction("🗑️  حذف", self)
                delete_action.triggered.connect(lambda: self.delete_product(product_id))
                context_menu.addAction(delete_action)

            # عرض القائمة في موقع النقر
            context_menu.exec_(self.products_table.mapToGlobal(position))
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")

    def on_table_double_clicked(self, index):
        """معالجة حدث النقر المزدوج على صف في الجدول"""
        row = index.row()
        product_id = int(self.products_table.item(row, 0).text())
        self.edit_product(product_id)

    def populate_products_table(self):
        """ملء جدول المنتجات بالبيانات المفلترة"""
        try:
            # التحقق من وجود بيانات منتجات
            if not hasattr(self, 'products_data'):
                # إذا لم تكن البيانات محملة، قم بتحميلها
                self.products_data = ProductModel.get_all_products()

            # تصفية منتجات التصنيف الوهمية (category_placeholder)
            displayed_products = [p for p in self.products_data if p.get('product_type') != 'category_placeholder']

            # تعيين عدد صفوف الجدول
            self.products_table.setRowCount(0)  # إفراغ الجدول أولاً
            self.products_table.setRowCount(len(displayed_products))

            # ملء الجدول بالبيانات
            for row, product in enumerate(displayed_products):
                self._populate_product_row(row, product)

            # تفعيل الجدول
            self.products_table.setEnabled(True)

            # تحديث الإحصائيات
            self.update_stats()

        except Exception as e:
            print(f"خطأ في ملء جدول المنتجات: {str(e)}")
            self.products_table.setRowCount(1)
            error_item = QTableWidgetItem("فشل تحميل المنتجات")
            error_item.setTextAlignment(Qt.AlignCenter)
            self.products_table.setItem(0, 2, error_item)
            self.products_table.setEnabled(True)

    def _populate_product_row(self, row, product):
        """ملء صف واحد في جدول المنتجات"""
        # رقم المنتج
        id_item = QTableWidgetItem(str(product.get('id', '')))
        id_item.setTextAlignment(Qt.AlignCenter)
        self.products_table.setItem(row, 0, id_item)

        # كود المنتج
        code_item = QTableWidgetItem(str(product.get('code', '')))
        code_item.setTextAlignment(Qt.AlignCenter)
        self.products_table.setItem(row, 1, code_item)

        # اسم المنتج/الخدمة (إضافة رمز للخدمات)
        product_name = product.get('name', '')
        product_type = product.get('product_type', 'physical')

        # إضافة رمز للخدمات لتمييزها
        if product_type == 'service':
            product_name = product_name + " 🛠️"  # رمز الخدمة

        name_item = QTableWidgetItem(product_name)
        name_item.setTextAlignment(Qt.AlignCenter)
        self.products_table.setItem(row, 2, name_item)

        # سعر المنتج - تنسيق السعر مثل صفحة التقارير
        price = product.get('price', 0)

        # إذا كان المنتج خدمة، عرض رمز اللانهاية بدلاً من السعر
        if product_type == 'service':
            price_item = QTableWidgetItem("∞")  # رمز لا نهائي للخدمات
            price_item.setForeground(QColor('#8E44AD'))  # لون أرجواني للخدمات
        else:
            # استخدام دالة format_price للمنتجات العادية
            formatted_price = self.format_price(price)
            price_item = QTableWidgetItem(formatted_price)

        price_item.setTextAlignment(Qt.AlignCenter)
        self.products_table.setItem(row, 3, price_item)

        # التصنيف
        category_item = QTableWidgetItem(product.get('category', ''))
        category_item.setTextAlignment(Qt.AlignCenter)
        self.products_table.setItem(row, 4, category_item)

        # المخزون
        stock = product.get('stock', 0)

        # تمييز الخدمات في عمود المخزون
        if product_type == 'service':
            stock_item = QTableWidgetItem("∞")  # رمز لا نهائي للخدمات
            stock_item.setForeground(QColor('#8E44AD'))  # لون أرجواني للخدمات
        else:
            stock_item = QTableWidgetItem(str(stock))
            # تطبيق اللون حسب حالة المخزون
            if stock <= 0:
                stock_item.setForeground(QColor('#E74C3C'))  # أحمر للمنتجات غير المتوفرة
            elif stock < 10:
                stock_item.setForeground(QColor('#F39C12'))  # برتقالي للمنتجات قليلة المخزون
            else:
                stock_item.setForeground(QColor('#27AE60'))  # أخضر للمنتجات المتوفرة بكثرة

        stock_item.setTextAlignment(Qt.AlignCenter)
        self.products_table.setItem(row, 5, stock_item)

        # تغيير لون خلفية صفوف الخدمات
        if product_type == 'service':
            for col in range(self.products_table.columnCount()):
                cell_item = self.products_table.item(row, col)
                if cell_item:
                    cell_item.setBackground(QColor('#ECF0F1'))  # لون فاتح لتمييز الخدمات

    def format_price(self, price):
        """تنسيق السعر بإضافة فواصل الآلاف وتقريب الأرقام العشرية"""
        if price == 0:
            return "0.00"

        # التأكد من أن السعر هو رقم
        try:
            price_float = float(price)
        except (ValueError, TypeError):
            return str(price)

        # تقريب الرقم إلى رقمين عشريين وتحويله إلى نص
        price_formatted = f"{price_float:.2f}"

        # تقسيم الرقم إلى جزء صحيح وجزء عشري
        whole, decimal = price_formatted.split(".")

        # إضافة فواصل الآلاف إلى الجزء الصحيح
        whole_with_commas = ""
        for i, digit in enumerate(reversed(whole)):
            if i > 0 and i % 3 == 0:
                whole_with_commas = "," + whole_with_commas
            whole_with_commas = digit + whole_with_commas

        # دمج الجزأين مرة أخرى
        result = whole_with_commas + "." + decimal

        return result

    def filter_products(self):
        """تصفية المنتجات بناءً على النص المدخل والتصنيف المختار"""
        search_text = self.search_input.text().strip()
        category = self.category_combo.currentText()
        product_type = self.type_combo.currentText()

        try:
            # تحديث قائمة المنتجات من قاعدة البيانات
            self.products_data = ProductModel.get_all_products()

            # تطبيق التصفية
            filtered_products = []
            for product in self.products_data:
                # تجاهل منتجات التصنيف الوهمية (category_placeholder)
                if product.get('product_type') == 'category_placeholder':
                    continue

                # التصفية حسب النص المدخل
                text_match = True  # افتراضياً صحيح إذا كان النص فارغاً
                if search_text:  # إذا كان هناك نص للبحث
                    name_match = search_text.lower() in product.get('name', '').lower()
                    code_match = search_text.lower() in str(product.get('code', '')).lower()
                    text_match = name_match or code_match

                # التصفية حسب التصنيف
                category_match = category == "الكل" or category == product.get('category', '')

                # التصفية حسب نوع المنتج (منتج/خدمة)
                type_match = True
                if product_type == "منتجات":
                    type_match = product.get('product_type', 'physical') == 'physical'
                elif product_type == "خدمات":
                    type_match = product.get('product_type', 'physical') == 'service'

                # إضافة المنتج إذا كان متطابقًا مع جميع شروط التصفية
                if text_match and category_match and type_match:
                    filtered_products.append(product)

            # تأكد من أن المنتجات مرتبة حسب المعرف
            filtered_products.sort(key=lambda x: x.get('id', 0))

            # تحديث الجدول
            self.products_data = filtered_products
            self.populate_products_table()

        except Exception as e:
            QMessageBox.warning(self, "خطأ في البحث", f"حدث خطأ أثناء البحث عن المنتجات: {str(e)}")

    def update_stats(self):
        """تحديث معلومات الإحصائيات"""
        try:
            # عدد المنتجات والخدمات
            products_count = 0
            services_count = 0

            for product in self.products_data:
                product_type = product.get('product_type', 'physical')

                # تجاهل منتجات التصنيف الوهمية (category_placeholder)
                if product_type == 'category_placeholder':
                    continue

                if product_type == 'physical':
                    products_count += 1
                elif product_type == 'service':
                    services_count += 1

            # تحديث النصوص
            total_items = products_count + services_count
            self.total_products_label.setText(f"إجمالي العناصر: {total_items} (منتجات: {products_count}، خدمات: {services_count})")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {str(e)}")

    def add_new_product(self):
        """فتح نافذة إضافة منتج جديد"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية إضافة منتج
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "إضافة منتج", show_message=True, parent_widget=self):
                    return

            dialog = ProductDialog(self)
            if dialog.exec_():
                product_data = dialog.get_product_data()

                try:
                    # إضافة product_type كمنتج فعلي
                    product_data['product_type'] = 'physical'

                    # التحقق من وضع التعديل أو الإضافة
                    if product_data.get('is_editing', False) and product_data.get('existing_product_id'):
                        # وضع التعديل - تحديث المنتج الموجود
                        product_id = product_data['existing_product_id']

                        # الحصول على الكمية القديمة لحساب الفرق
                        old_product = ProductModel.get_product_by_id(product_id)
                        old_quantity = old_product.get('stock', 0) if old_product else 0
                        new_quantity = product_data['stock']
                        quantity_change = new_quantity - old_quantity

                        # تحديث المنتج
                        success = ProductModel.update_product(product_id, product_data)

                        if success:
                            # إضافة السجل إلى سجل المخزون إذا تغيرت الكمية
                            if quantity_change != 0:
                                note = f"تعديل مخزون المنتج - تغيير بمقدار {quantity_change}"
                                self.add_inventory_record(
                                    product_id=product_id,
                                    product_name=product_data['name'],
                                    old_quantity=old_quantity,
                                    new_quantity=new_quantity,
                                    added_quantity=quantity_change,
                                    note=note
                                )

                            QMessageBox.information(self, "تم التحديث", "تم تحديث المنتج بنجاح")
                        else:
                            QMessageBox.warning(self, "خطأ في التحديث", "حدث خطأ أثناء تحديث المنتج")
                    else:
                        # وضع الإضافة - إضافة منتج جديد
                        success = ProductModel.add_product(product_data)

                        if success:
                            # إضافة السجل إلى سجل المخزون
                            self.add_inventory_record(
                                product_id=success,  # نفترض أن add_product يرجع معرف المنتج
                                product_name=product_data['name'],
                                old_quantity=0,
                                new_quantity=product_data['stock'],
                                added_quantity=product_data['stock'],
                                note="إضافة منتج جديد"
                            )

                            QMessageBox.information(self, "تمت الإضافة", "تمت إضافة المنتج بنجاح")
                        else:
                            QMessageBox.warning(self, "خطأ في الإضافة", "حدث خطأ أثناء إضافة المنتج")

                    # تحديث البيانات الداخلية قبل تحديث الجدول
                    self.products_data = ProductModel.get_all_products()
                    # تحديث الجدول
                    self.populate_products_table()

                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء معالجة المنتج: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")

    def add_new_service(self):
        """فتح نافذة إضافة خدمة جديدة"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية إضافة منتج
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "إضافة منتج", show_message=True, parent_widget=self):
                    return

            dialog = ServiceDialog(self)
            if dialog.exec_():
                service_data = dialog.get_service_data()

                try:
                    # إضافة product_type كخدمة
                    service_data['product_type'] = 'service'
                    service_data['stock'] = 0  # الخدمات ليس لها مخزون

                    # حفظ بيانات الخدمة في قاعدة البيانات
                    success = ProductModel.add_product(service_data)

                    if success:
                        QMessageBox.information(self, "تمت الإضافة", "تمت إضافة الخدمة بنجاح")
                        # تحديث البيانات الداخلية قبل تحديث الجدول
                        self.products_data = ProductModel.get_all_products()
                        # تحديث الجدول
                        self.populate_products_table()
                    else:
                        QMessageBox.warning(self, "خطأ في الإضافة", "حدث خطأ أثناء إضافة الخدمة")
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")

    def edit_product(self, product_id):
        """فتح نافذة تعديل المنتج أو الخدمة"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية تعديل منتج
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "تعديل منتج", show_message=True, parent_widget=self):
                    return

            # الحصول على بيانات المنتج
            product = ProductModel.get_product_by_id(product_id)

            if not product:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على المنتج")
                return

            # التحقق من نوع المنتج وفتح النافذة المناسبة
            product_type = product.get('product_type', 'physical')

            if product_type == 'service':
                # فتح نافذة تعديل الخدمة
                dialog = ServiceDialog(self, product)
            else:
                # فتح نافذة تعديل المنتج
                dialog = ProductDialog(self, product)

            if dialog.exec_():
                # الحصول على البيانات المحدثة
                if product_type == 'service':
                    updated_data = dialog.get_service_data()
                    updated_data['product_type'] = 'service'
                    updated_data['stock'] = 0  # الخدمات ليس لها مخزون
                else:
                    updated_data = dialog.get_product_data()
                    updated_data['product_type'] = 'physical'

                # تحديث المنتج في قاعدة البيانات
                success = ProductModel.update_product(product_id, updated_data)

                if success:
                    QMessageBox.information(self, "تم التحديث", "تم تحديث المنتج بنجاح")
                    # تحديث البيانات الداخلية قبل تحديث الجدول
                    self.products_data = ProductModel.get_all_products()
                    # تحديث الجدول
                    self.populate_products_table()
                else:
                    QMessageBox.warning(self, "خطأ في التحديث", "حدث خطأ أثناء تحديث المنتج")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")

    def delete_product(self, product_id):
        """حذف منتج من قاعدة البيانات"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية حذف منتج
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "حذف منتج", show_message=True, parent_widget=self):
                    return

            # التأكد من رغبة المستخدم في الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                "هل أنت متأكد من رغبتك في حذف هذا المنتج؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # حذف المنتج من قاعدة البيانات
                success = ProductModel.delete_product(product_id)

                if success:
                    QMessageBox.information(self, "تم الحذف", "تم حذف المنتج بنجاح")

                    # تحديث البيانات الداخلية قبل تحديث الجدول
                    self.products_data = ProductModel.get_all_products()
                    # تحديث جدول المنتجات
                    self.populate_products_table()
                else:
                    QMessageBox.warning(self, "خطأ", "فشلت عملية حذف المنتج")

        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحذف", f"حدث خطأ أثناء حذف المنتج: {str(e)}")

    def apply_styles(self):
        """تطبيق التنسيقات على عناصر الواجهة"""
        # استخدام التنسيقات من ملف الستايلات - استخدام get_all_view_styles مثل صفحة التقارير
        self.setObjectName("products_view")
        self.setStyleSheet(AppStyles.get_all_view_styles())

        # تنسيق ملصق التلميح
        hint_label_style = """
            #hint_label {
                color: #64748b;
                font-style: italic;
                padding: 5px;
                font-size: 11px;
            }
        """

        # تنسيق ملصقات الإحصائيات - تعديل التنسيق ليطابق صفحة التقارير
        stats_label_style = """
            #stats_label {
                background-color: #eef2ff;
                color: #2563eb;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                border: 1px solid #dbeafe;
                margin-top: 10px;
            }
        """

        # تنسيق إضافي للأزرار والجدول
        additional_styles = """
            /* تنسيق أزرار الإجراءات مثل صفحة التقارير */
            #action_button {
                background-color: #3b82f6;
                color: white;
                border: 1px solid #60a5fa;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                text-align: center;
            }

            #action_button:hover {
                background-color: #2563eb;
                border: 1px solid #3b82f6;
            }

            #action_button:pressed {
                background-color: #1d4ed8;
                border: 1px solid #2563eb;
            }

            #secondary_button {
                background-color: white;
                color: #3b82f6;
                border: 1px solid #3b82f6;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                text-align: center;
            }

            #secondary_button:hover {
                background-color: #e2e8f0;
                border: 1px solid #2563eb;
                color: #2563eb;
            }

            #secondary_button:pressed {
                background-color: #f1f5f9;
                border: 1px solid #1d4ed8;
                color: #1d4ed8;
            }

            /* تنسيق الجدول مثل صفحة التقارير */
            QTableView, QTableWidget {
                border: 1px solid #e2e8f0;
                background-color: white;
                gridline-color: #e2e8f0;
                border-radius: 6px;
                selection-background-color: rgba(59, 130, 246, 0.2);
                selection-color: #0f172a;
            }

            QTableView::item, QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e2e8f0;
            }

            QTableView::item:selected, QTableWidget::item:selected {
                background-color: rgba(59, 130, 246, 0.2);
                color: #0f172a;
            }

            QHeaderView::section {
                background-color: #f1f5f9;
                padding: 10px;
                border: none;
                border-bottom: 1px solid #cbd5e1;
                font-weight: bold;
                color: #334155;
            }

            QTableView QHeaderView::section:first, QTableWidget QHeaderView::section:first {
                border-top-right-radius: 6px;
            }

            QTableView QHeaderView::section:last, QTableWidget QHeaderView::section:last {
                border-top-left-radius: 6px;
            }
        """

        # تطبيق التنسيقات
        self.setStyleSheet(self.styleSheet() + hint_label_style + stats_label_style + additional_styles)

    def add_stock_to_inventory(self):
        """فتح نافذة لإضافة مخزون لمنتج"""
        # التحقق من وجود منتج محدد
        selected_indexes = self.products_table.selectedIndexes()
        if not selected_indexes:
            QMessageBox.warning(self, "تحذير", "الرجاء تحديد منتج أولاً")
            return

        row = selected_indexes[0].row()
        product_id = int(self.products_table.item(row, 0).text())
        self.add_stock_to_product(product_id)

    def add_stock_to_product(self, product_id):
        """إضافة كمية للمخزون لمنتج محدد"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية تعديل مخزون
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "تعديل مخزون", show_message=True, parent_widget=self):
                    return

            # الحصول على بيانات المنتج
            product = ProductModel.get_product_by_id(product_id)

            if not product:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على المنتج")
                return

            product_name = product.get('name', '')
            current_stock = product.get('stock', 0)

            # فتح نافذة إضافة المخزون
            dialog = AddStockDialog(self, product_name, current_stock)
            result = dialog.exec_()

            if result == QDialog.Accepted:
                # الحصول على الكمية المضافة وملاحظة
                added_quantity = dialog.get_quantity()
                note = dialog.get_note()

                # حساب الكمية الجديدة
                new_quantity = current_stock + added_quantity

                # تحديث كمية المنتج في قاعدة البيانات
                success = ProductModel.update_product_quantity(product_id, new_quantity)

                if success:
                    # إضافة سجل لحركة المخزون
                    self.add_inventory_record(
                        product_id,
                        product_name,
                        current_stock,
                        new_quantity,
                        added_quantity,
                        note
                    )

                    QMessageBox.information(
                        self,
                        "تمت الإضافة",
                        f"تمت إضافة {added_quantity} وحدة إلى مخزون {product_name} بنجاح"
                    )

                    # تحديث البيانات الداخلية قبل تحديث الجدول
                    self.products_data = ProductModel.get_all_products()
                    # تحديث جدول المنتجات
                    self.populate_products_table()
                else:
                    QMessageBox.warning(self, "خطأ", "فشلت عملية تحديث المخزون")

        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحديث المخزون", f"حدث خطأ أثناء تحديث المخزون: {str(e)}")

    def toggle_favorite_product(self, product_id):
        """إضافة أو إزالة المنتج من المفضلة"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية تعديل منتج
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "تعديل منتج", show_message=True, parent_widget=self):
                    return

            # الحصول على بيانات المنتج
            product = ProductModel.get_product_by_id(product_id)

            if not product:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على المنتج")
                return

            # تبديل حالة المفضلة
            current_favorite = product.get('is_favorite', 0)
            new_favorite = not current_favorite

            # تحديث حالة المفضلة في قاعدة البيانات
            success = ProductModel.toggle_favorite(product_id, new_favorite)

            if success:
                product_name = product.get('name', '')

                if new_favorite:
                    status_title = "تمت الإضافة للمفضلة"
                    status_message = f"تم إضافة {product_name} إلى المفضلة بنجاح"
                else:
                    status_title = "تمت الإزالة من المفضلة"
                    status_message = f"تم إزالة {product_name} من المفضلة بنجاح"

                QMessageBox.information(
                    self,
                    status_title,
                    status_message
                )

                # تحديث البيانات الداخلية قبل تحديث الجدول
                self.products_data = ProductModel.get_all_products()
                # تحديث جدول المنتجات
                self.populate_products_table()
            else:
                QMessageBox.warning(self, "خطأ", "فشلت عملية تحديث حالة المفضلة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحديث المفضلة", f"حدث خطأ أثناء تحديث حالة المفضلة: {str(e)}")

    def add_inventory_record(self, product_id, product_name, old_quantity, new_quantity, added_quantity, note):
        """إضافة سجل لحركة المخزون"""
        # هنا يمكن إضافة سجل لحركة المخزون في جدول منفصل إذا لزم الأمر
        # لكن حاليًا سنحتفظ بالسجلات في الذاكرة فقط
        record = {
            "product_id": product_id,
            "product_name": product_name,
            "date": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "old_quantity": old_quantity,
            "new_quantity": new_quantity,
            "change": added_quantity,
            "note": note
        }

        self.inventory_history.append(record)

    def refresh_page(self):
        """تحديث بيانات الصفحة عند الانتقال إليها أو عند فتح التطبيق"""
        # تحديث قائمة الفئات
        self.load_categories()

        # تحديث بيانات المنتجات في الجدول
        self.populate_products_table()

        # تحديث الإحصائيات
        self.update_stats()

        # تحديث حالة تفعيل الأزرار بناءً على صلاحيات المستخدم
        self.update_buttons_state()

        print("تم تحديث صفحة المنتجات")

    def update_buttons_state(self):
        """تحديث حالة تفعيل الأزرار بناءً على صلاحيات المستخدم"""
        # التحقق من وجود مستخدم حالي
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        if not main_window or not main_window.current_user:
            return

        user_id = main_window.current_user.get('id')
        username = main_window.current_user.get('username')

        # المستخدم admin له جميع الصلاحيات
        if username == 'admin':
            # تفعيل جميع الأزرار
            self.new_product_btn.setEnabled(True)
            self.new_service_btn.setEnabled(True)
            self.categories_btn.setEnabled(True)
            self.new_product_btn.setStyleSheet("")
            self.new_service_btn.setStyleSheet("")
            self.categories_btn.setStyleSheet("")
            return

        # استيراد وحدة التحكم بالمستخدمين
        from controllers.user_controller import UserController

        # تحديث حالة زر إضافة منتج جديد
        has_add_product_permission = UserController.check_permission(user_id, "إضافة منتج")
        self.new_product_btn.setEnabled(has_add_product_permission)
        if not has_add_product_permission:
            self.new_product_btn.setStyleSheet("""
                background-color: #f0f0f0;
                color: #999999;
                border: 1px solid #cccccc;
            """)
        else:
            self.new_product_btn.setStyleSheet("")

        # تحديث حالة زر إضافة خدمة جديدة
        has_add_service_permission = UserController.check_permission(user_id, "إضافة خدمة")
        self.new_service_btn.setEnabled(has_add_service_permission)
        if not has_add_service_permission:
            self.new_service_btn.setStyleSheet("""
                background-color: #f0f0f0;
                color: #999999;
                border: 1px solid #cccccc;
            """)
        else:
            self.new_service_btn.setStyleSheet("")

        # تحديث حالة زر التصنيفات
        has_manage_categories_permission = UserController.check_permission(user_id, "إدارة التصنيفات")
        self.categories_btn.setEnabled(has_manage_categories_permission)
        if not has_manage_categories_permission:
            self.categories_btn.setStyleSheet("""
                background-color: #f0f0f0;
                color: #999999;
                border: 1px solid #cccccc;
            """)
        else:
            self.categories_btn.setStyleSheet("")

    def show_categories(self):
        """عرض نافذة التصنيفات المسجلة في البرنامج"""
        try:
            # إنشاء نافذة التصنيفات
            dialog = CategoryListDialog(self)

            # عرض النافذة
            result = dialog.exec_()

            # إذا تم إجراء تغييرات وتم الحفظ، نقوم بتحديث الفئات
            if result == QDialog.Accepted:
                # تحديث قائمة الفئات
                self.load_categories()
                # تحديث جدول المنتجات
                self.populate_products_table()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض التصنيفات: {str(e)}")


class ProductDialog(QDialog):
    def __init__(self, parent=None, product_data=None):
        super().__init__(parent)

        # متغيرات لتتبع حالة المنتج
        self.existing_product = None
        self.is_editing_mode = False

        # تعيين عنوان النافذة حسب الوضع (إضافة/تعديل)
        self.setWindowTitle("إضافة منتج جديد" if not product_data else "تعديل منتج")
        self.setFixedSize(450, 380)  # تقليل العرض من 500 إلى 450 بكسل
        self.setLayoutDirection(Qt.RightToLeft)

        # إعداد التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 10, 8, 10)  # تقليل الهوامش الجانبية إلى 8 بكسل
        layout.setSpacing(3)  # تقليل المسافة بين العناصر من 10 إلى 3

        # إنشاء نموذج بيانات
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setSpacing(5)  # تقليل المسافة بين العناصر من 10 إلى 5

        # حقل كود المنتج
        code_label = QLabel("كود المنتج:")

        # إنشاء ويدجت حاوي لحقل الإدخال وزر التوليد
        code_widget = QWidget()

        # جعل الويدجت الحاوي شفافًا (إزالة الخلفية البيضاء)
        code_widget.setStyleSheet("background-color: transparent;")

        # إنشاء تخطيط أفقي للويدجت الحاوي
        code_layout = QHBoxLayout(code_widget)
        code_layout.setSpacing(5)
        code_layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء حقل إدخال الكود وتحديد حجمه
        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText("أدخل كود المنتج")
        self.code_input.setMaxLength(50)
        self.code_input.setFixedWidth(150)  # تقليل العرض من 180 إلى 150 بكسل
        self.code_input.setAlignment(Qt.AlignCenter)  # توسيط النص في حقل الإدخال

        # ربط حدث تغيير النص للتحقق من وجود المنتج
        self.code_input.textChanged.connect(self.check_existing_product)

        # إنشاء زر توليد كود بار عشوائي
        self.generate_barcode_btn = QPushButton("توليد")
        self.generate_barcode_btn.setObjectName("secondary_button")
        self.generate_barcode_btn.setFixedWidth(50)  # تقليل العرض من 60 إلى 50 بكسل
        self.generate_barcode_btn.clicked.connect(self.generate_random_barcode)

        # إضافة مساحة مرنة لدفع الزر إلى أقصى اليسار
        code_layout.addWidget(self.code_input)
        code_layout.addStretch(1)  # إضافة مساحة مرنة بين حقل الإدخال والزر
        code_layout.addWidget(self.generate_barcode_btn)

        # إضافة الويدجت الحاوي إلى نموذج البيانات
        form_layout.addRow(code_label, code_widget)

        # حقل اسم المنتج
        name_label = QLabel("اسم المنتج:")
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("أدخل اسم المنتج")
        self.name_input.setMaxLength(100)
        self.name_input.setAlignment(Qt.AlignCenter)  # توسيط النص في حقل الإدخال
        form_layout.addRow(name_label, self.name_input)

        # حقل تصنيف المنتج
        category_label = QLabel("تصنيف المنتج:")
        self.category_input = RTLComboBox()
        self.category_input.setObjectName("combo_box")
        self.category_input.setEditable(True)  # جعل الكومبوبوكس قابل للتحرير
        self.category_input.lineEdit().setAlignment(Qt.AlignCenter)  # توسيط النص

        # جلب قائمة التصنيفات من قاعدة البيانات
        try:
            categories = ProductModel.get_product_categories()
            self.category_input.addItems(categories)
            self.category_input.setCurrentText("")  # جعل الحقل فارغًا بشكل افتراضي
        except:
            # في حالة وجود مشكلة في جلب التصنيفات، إضافة تصنيفات افتراضية
            self.category_input.addItems(["مشروبات", "حلويات", "مأكولات", "أخرى"])

        form_layout.addRow(category_label, self.category_input)

        # حقل سعر المنتج
        price_label = QLabel("سعر البيع:")
        self.price_input = QDoubleSpinBox()
        self.price_input.setRange(0, 100000)
        self.price_input.setDecimals(2)
        self.price_input.setSuffix(" ج.م")
        self.price_input.setMinimumHeight(25)  # تقليل الارتفاع من 28 إلى 25
        self.price_input.setAlignment(Qt.AlignCenter)  # توسيط النص
        form_layout.addRow(price_label, self.price_input)

        # حقل تكلفة المنتج
        cost_label = QLabel("تكلفة المنتج:")
        self.cost_input = QDoubleSpinBox()
        self.cost_input.setRange(0, 100000)
        self.cost_input.setDecimals(2)
        self.cost_input.setSuffix(" ج.م")
        self.cost_input.setMinimumHeight(25)  # تقليل الارتفاع من 28 إلى 25
        self.cost_input.setAlignment(Qt.AlignCenter)  # توسيط النص
        form_layout.addRow(cost_label, self.cost_input)

        # حقل كمية المخزون
        quantity_label = QLabel("كمية المخزون:")
        self.quantity_input = QSpinBox()
        self.quantity_input.setRange(0, 10000)
        self.quantity_input.setMinimumHeight(25)  # تقليل الارتفاع من 28 إلى 25
        self.quantity_input.setAlignment(Qt.AlignCenter)  # توسيط النص
        form_layout.addRow(quantity_label, self.quantity_input)

        # تم إزالة حقل الحد الأدنى للمخزون

        # تم إزالة خيار إضافة للمفضلة

        layout.addLayout(form_layout)

        # إضافة أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(0, 5, 0, 0)
        buttons_layout.setSpacing(10)  # تعيين المسافة بين الأزرار

        # زر الحفظ
        self.save_button = QPushButton("حفظ")
        self.save_button.setObjectName("action_button")
        self.save_button.setMinimumHeight(30)  # تقليل الارتفاع من 35 إلى 30
        self.save_button.setFixedWidth(100)  # تقليل العرض من 120 إلى 100
        self.save_button.clicked.connect(self.validate_and_accept)

        # زر الإلغاء
        cancel_button = QPushButton("إلغاء")
        cancel_button.setObjectName("secondary_button")
        cancel_button.setMinimumHeight(30)  # تقليل الارتفاع من 35 إلى 30
        cancel_button.setFixedWidth(100)  # تقليل العرض من 120 إلى 100
        cancel_button.clicked.connect(self.reject)

        # إضافة مساحة مرنة على الجانبين لتوسيط الأزرار
        buttons_layout.addStretch(1)
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addStretch(1)

        layout.addLayout(buttons_layout)

        # ملء البيانات الحالية إذا كان التعديل
        if product_data:
            self.code_input.setText(str(product_data.get('code', '')))
            self.name_input.setText(str(product_data.get('name', '')))
            self.category_input.setCurrentText(str(product_data.get('category', '')))
            self.price_input.setValue(float(product_data.get('price', 0)))
            self.cost_input.setValue(float(product_data.get('cost', 0)))
            self.quantity_input.setValue(int(product_data.get('stock', 0)))
            # تم إزالة سطر تعيين قيمة الحد الأدنى للمخزون
            # تم إزالة سطر تعيين حالة خيار المفضلة

        # إعداد ترتيب التنقل بين الحقول
        self.setTabOrder(self.code_input, self.name_input)
        self.setTabOrder(self.name_input, self.category_input)
        self.setTabOrder(self.category_input, self.price_input)
        self.setTabOrder(self.price_input, self.cost_input)
        self.setTabOrder(self.cost_input, self.quantity_input)
        self.setTabOrder(self.quantity_input, self.save_button)
        self.setTabOrder(self.save_button, cancel_button)

        # تركيز البداية على حقل الكود
        self.code_input.setFocus()

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def keyPressEvent(self, event):
        """معالجة أحداث المفاتيح للتنقل بين الحقول"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            # الحصول على الحقل الحالي الذي له التركيز
            current_widget = self.focusWidget()

            # تحديد الحقل التالي للانتقال إليه
            if current_widget == self.code_input:
                self.name_input.setFocus()
            elif current_widget == self.name_input:
                self.category_input.setFocus()
            elif current_widget == self.category_input:
                self.price_input.setFocus()
            elif current_widget == self.price_input:
                self.cost_input.setFocus()
            elif current_widget == self.cost_input:
                self.quantity_input.setFocus()
            elif current_widget == self.quantity_input:
                # الانتقال إلى زر الحفظ
                self.save_button.setFocus()
            else:
                # السلوك الافتراضي للمفاتيح الأخرى
                super().keyPressEvent(event)
        else:
            # السلوك الافتراضي للمفاتيح الأخرى
            super().keyPressEvent(event)

    def generate_random_barcode(self):
        """توليد كود بار عشوائي فريد مكون من 8 أرقام فقط"""
        import random
        from models.products import ProductModel

        # توليد رقم عشوائي مكون من 8 أرقام
        barcode = str(random.randint(10000000, 99999999))

        # التحقق من عدم وجود المنتج بنفس الكود
        existing_product = ProductModel.get_product_by_code(barcode)

        # في حالة وجود منتج بنفس الكود، نولد كود جديد
        while existing_product:
            barcode = str(random.randint(10000000, 99999999))
            existing_product = ProductModel.get_product_by_code(barcode)

        self.code_input.setText(barcode)
        # تنشيط خانة الباركود بعد التوليد
        self.code_input.setFocus()

    def check_existing_product(self, barcode_text):
        """التحقق من وجود منتج بالباركود المدخل وملء الحقول إذا وجد"""
        # تجاهل النصوص الفارغة أو القصيرة جداً
        if not barcode_text or len(barcode_text.strip()) < 3:
            self.existing_product = None
            self.is_editing_mode = False
            self.setWindowTitle("إضافة منتج جديد")
            self.save_button.setText("حفظ")
            return

        try:
            from models.products import ProductModel

            # البحث عن المنتج بالباركود
            product = ProductModel.get_product_by_code(barcode_text.strip())

            if product:
                # تم العثور على المنتج - ملء الحقول
                self.existing_product = product
                self.is_editing_mode = True

                # تغيير عنوان النافذة
                self.setWindowTitle(f"تعديل منتج: {product.get('name', '')}")
                self.save_button.setText("تحديث")

                # ملء الحقول ببيانات المنتج الموجود
                self.fill_product_fields(product)

            else:
                # لم يتم العثور على المنتج - وضع الإضافة
                self.existing_product = None
                self.is_editing_mode = False
                self.setWindowTitle("إضافة منتج جديد")
                self.save_button.setText("حفظ")

        except Exception as e:
            print(f"خطأ في التحقق من وجود المنتج: {str(e)}")
            self.existing_product = None
            self.is_editing_mode = False

    def fill_product_fields(self, product):
        """ملء حقول النافذة ببيانات المنتج الموجود"""
        try:
            # منع إطلاق حدث textChanged أثناء ملء الحقول
            self.code_input.blockSignals(True)

            # ملء الحقول
            self.name_input.setText(str(product.get('name', '')))
            self.category_input.setCurrentText(str(product.get('category', '')))
            self.price_input.setValue(float(product.get('price', 0)))
            self.cost_input.setValue(float(product.get('cost', 0)))
            self.quantity_input.setValue(int(product.get('stock', 0)))

            # إعادة تفعيل الإشارات
            self.code_input.blockSignals(False)

        except Exception as e:
            print(f"خطأ في ملء حقول المنتج: {str(e)}")

    def validate_and_accept(self):
        """التحقق من صحة البيانات قبل الحفظ"""
        # التحقق من كود المنتج
        if not self.code_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "الرجاء إدخال كود المنتج")
            self.code_input.setFocus()
            return

        # التحقق من اسم المنتج
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "الرجاء إدخال اسم المنتج")
            self.name_input.setFocus()
            return

        # التحقق من التصنيف
        if not self.category_input.currentText().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "الرجاء إدخال تصنيف المنتج")
            self.category_input.setFocus()
            return

        # التحقق من سعر البيع
        if self.price_input.value() <= 0:
            QMessageBox.warning(self, "بيانات غير صحيحة", "الرجاء إدخال سعر بيع صحيح أكبر من صفر")
            self.price_input.setFocus()
            return

        # التحقق من تكلفة المنتج
        if self.cost_input.value() < 0:
            QMessageBox.warning(self, "بيانات غير صحيحة", "تكلفة المنتج لا يمكن أن تكون أقل من صفر")
            self.cost_input.setFocus()
            return

        # التحقق من كمية المخزون
        if self.quantity_input.value() < 0:
            QMessageBox.warning(self, "بيانات غير صحيحة", "كمية المخزون لا يمكن أن تكون أقل من صفر")
            self.quantity_input.setFocus()
            return

        # إذا تم اجتياز جميع التحققات، قبول الحوار
        self.accept()

    def get_product_data(self):
        """استخراج بيانات المنتج من الحقول"""
        data = {
            'code': self.code_input.text(),
            'name': self.name_input.text(),
            'category': self.category_input.currentText(),
            'price': self.price_input.value(),
            'cost': self.cost_input.value(),
            'stock': self.quantity_input.value(),
            'min_quantity': 5,  # تم تعيين قيمة افتراضية بدلاً من استخدام الحقل
            'is_favorite': 0  # تم تعيين قيمة افتراضية بدلاً من استخدام الـ checkbox
        }

        # إضافة معلومات إضافية في حالة التعديل
        if self.is_editing_mode and self.existing_product:
            data['existing_product_id'] = self.existing_product.get('id')
            data['is_editing'] = True
        else:
            data['is_editing'] = False

        return data

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        try:
            # استخدام تنسيقات من AppStyles
            from styles import AppStyles
            self.setStyleSheet(AppStyles.get_dialog_style())

            # تطبيق الأنماط على جميع الأزرار في النافذة
            for button in self.findChildren(QPushButton):
                # تطبيق الحد الأدنى للعرض والارتفاع على جميع الأزرار
                button.setMinimumWidth(80)
                button.setMinimumHeight(24)
        except Exception as e:
            print(f"خطأ في تطبيق الأنماط: {str(e)}")
            # تنسيقات افتراضية
            self.setStyleSheet("""
                QDialog {
                    background-color: #f0f0f0;
                    border: 1px solid #d0d0d0;
                }

                QLabel {
                    color: #000000;
                    background-color: transparent;
                }

                QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 6px;
                    background-color: white;
                    color: #000000;
                }

                QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                    border: 1px solid #0078d7;
                }

                /* تنسيق الزر الافتراضي للويندوز */
                QPushButton {
                    background-color: #e1e1e1;
                    color: #000000;
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 5px 12px;
                    text-align: center;
                    min-width: 80px;
                    min-height: 24px;
                }

                QPushButton:hover {
                    background-color: #e5f1fb;
                    border: 1px solid #0078d7;
                }

                QPushButton:pressed {
                    background-color: #cce4f7;
                    border: 1px solid #0078d7;
                }

                QPushButton:focus {
                    border: 1px solid #0078d7;
                    outline: none;
                }

                QPushButton:default {
                    background-color: #0078d7;
                    color: white;
                    border: 1px solid #0078d7;
                }

                QPushButton:default:hover {
                    background-color: #1a86d9;
                    border: 1px solid #1a86d9;
                }

                QPushButton:default:pressed {
                    background-color: #006cc1;
                    border: 1px solid #006cc1;
                }

                QPushButton:disabled {
                    background-color: #f0f0f0;
                    color: #666666;
                    border: 1px solid #d0d0d0;
                }

                QPushButton#action_button {
                    background-color: #0078d7;
                    color: white;
                    border: 1px solid #0078d7;
                    border-radius: 2px;
                    padding: 5px 12px;
                    min-width: 80px;
                }

                QPushButton#action_button:hover {
                    background-color: #1a86d9;
                    border: 1px solid #1a86d9;
                }

                QPushButton#action_button:pressed {
                    background-color: #006cc1;
                    border: 1px solid #006cc1;
                }

                QPushButton#secondary_button {
                    background-color: #e1e1e1;
                    color: #000000;
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 5px 12px;
                    min-width: 80px;
                }

                QPushButton#secondary_button:hover {
                    background-color: #e5f1fb;
                    border: 1px solid #0078d7;
                }

                QPushButton#secondary_button:pressed {
                    background-color: #cce4f7;
                    border: 1px solid #0078d7;
                }
            """)


class AddStockDialog(QDialog):
    def __init__(self, parent=None, product_name="", current_stock=0):
        super().__init__(parent)

        self.setWindowTitle("إضافة للمخزون")
        self.setFixedSize(400, 350)
        self.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان المنتج
        title_label = QLabel(f"إضافة كمية للمخزون: {product_name}")
        title_label.setAlignment(Qt.AlignCenter)
        font = title_label.font()
        font.setPointSize(12)
        font.setBold(True)
        title_label.setFont(font)
        layout.addWidget(title_label)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator)

        # عرض الكمية الحالية
        stock_info = QLabel(f"الكمية الحالية في المخزون: {current_stock}")
        stock_info.setAlignment(Qt.AlignCenter)
        layout.addWidget(stock_info)

        # نموذج إدخال البيانات
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setSpacing(15)

        # حقل الكمية المضافة
        quantity_label = QLabel("الكمية المضافة:")
        self.quantity_input = QSpinBox()
        self.quantity_input.setRange(1, 1000)
        self.quantity_input.setValue(1)
        self.quantity_input.setMinimumHeight(30)
        self.quantity_input.valueChanged.connect(self.update_preview)
        form_layout.addRow(quantity_label, self.quantity_input)

        # معاينة الكمية الجديدة
        self.preview_label = QLabel(f"الكمية بعد الإضافة: {current_stock + 1}")
        self.preview_label.setStyleSheet("color: #2563eb; font-weight: bold;")
        form_layout.addRow("", self.preview_label)

        # حقل ملاحظات
        note_label = QLabel("ملاحظات:")
        self.note_input = QLineEdit()
        self.note_input.setPlaceholderText("سبب الإضافة (اختياري)")
        form_layout.addRow(note_label, self.note_input)

        layout.addLayout(form_layout)

        # إضافة زر التأكيد
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)  # تعيين المسافة بين الأزرار

        # زر الإضافة
        add_button = QPushButton("إضافة للمخزون")
        add_button.setObjectName("action_button")
        add_button.setMinimumHeight(35)
        add_button.setFixedWidth(120)  # تعيين عرض ثابت للزر
        add_button.clicked.connect(self.accept)

        # زر الإلغاء
        cancel_button = QPushButton("إلغاء")
        cancel_button.setObjectName("secondary_button")
        cancel_button.setMinimumHeight(35)
        cancel_button.setFixedWidth(120)  # تعيين نفس العرض الثابت للزر
        cancel_button.clicked.connect(self.reject)

        # إضافة مساحة مرنة على الجانبين لتوسيط الأزرار
        buttons_layout.addStretch(1)
        buttons_layout.addWidget(add_button)
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addStretch(1)

        layout.addLayout(buttons_layout)

        # تخزين الكمية الحالية
        self.current_stock = current_stock

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def update_preview(self, value):
        """تحديث معاينة الكمية الجديدة"""
        self.preview_label.setText(f"الكمية بعد الإضافة: {self.current_stock + value}")

    def get_quantity(self):
        """استرجاع الكمية المضافة"""
        return self.quantity_input.value()

    def get_note(self):
        """استرجاع الملاحظات"""
        return self.note_input.text()

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        try:
            # استخدام تنسيقات من AppStyles
            from styles import AppStyles
            self.setStyleSheet(AppStyles.get_dialog_style())

            # تطبيق الأنماط على جميع الأزرار في النافذة
            for button in self.findChildren(QPushButton):
                # تطبيق الحد الأدنى للعرض والارتفاع على جميع الأزرار
                button.setMinimumWidth(80)
                button.setMinimumHeight(24)
        except Exception as e:
            print(f"خطأ في تطبيق الأنماط: {str(e)}")
            # تنسيقات افتراضية
            self.setStyleSheet("""
                QDialog {
                    background-color: #f0f0f0;
                    border: 1px solid #d0d0d0;
                }

                QLabel {
                    color: #000000;
                    background-color: transparent;
                }

                QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 6px;
                    background-color: white;
                    color: #000000;
                }

                QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                    border: 1px solid #0078d7;
                }

                /* تنسيق الزر الافتراضي للويندوز */
                QPushButton {
                    background-color: #e1e1e1;
                    color: #000000;
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 5px 12px;
                    text-align: center;
                    min-width: 80px;
                    min-height: 24px;
                }

                QPushButton:hover {
                    background-color: #e5f1fb;
                    border: 1px solid #0078d7;
                }

                QPushButton:pressed {
                    background-color: #cce4f7;
                    border: 1px solid #0078d7;
                }

                QPushButton:focus {
                    border: 1px solid #0078d7;
                    outline: none;
                }

                QPushButton:default {
                    background-color: #0078d7;
                    color: white;
                    border: 1px solid #0078d7;
                }

                QPushButton:default:hover {
                    background-color: #1a86d9;
                    border: 1px solid #1a86d9;
                }

                QPushButton:default:pressed {
                    background-color: #006cc1;
                    border: 1px solid #006cc1;
                }

                QPushButton:disabled {
                    background-color: #f0f0f0;
                    color: #666666;
                    border: 1px solid #d0d0d0;
                }

                QPushButton#action_button {
                    background-color: #0078d7;
                    color: white;
                    border: 1px solid #0078d7;
                    border-radius: 2px;
                    padding: 5px 12px;
                    min-width: 80px;
                }

                QPushButton#action_button:hover {
                    background-color: #1a86d9;
                    border: 1px solid #1a86d9;
                }

                QPushButton#action_button:pressed {
                    background-color: #006cc1;
                    border: 1px solid #006cc1;
                }

                QPushButton#secondary_button {
                    background-color: #e1e1e1;
                    color: #000000;
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 5px 12px;
                    min-width: 80px;
                }

                QPushButton#secondary_button:hover {
                    background-color: #e5f1fb;
                    border: 1px solid #0078d7;
                }

                QPushButton#secondary_button:pressed {
                    background-color: #cce4f7;
                    border: 1px solid #0078d7;
                }
            """)


class ServiceDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل خدمة"""
    def __init__(self, parent=None, service_data=None):
        super().__init__(parent)

        # تعيين خصائص النافذة
        self.setWindowTitle("إضافة خدمة جديدة" if service_data is None else "تعديل الخدمة")
        self.setMinimumWidth(500)
        self.setLayoutDirection(Qt.RightToLeft)

        # تخزين بيانات الخدمة (إذا كانت موجودة)
        self.service_data = service_data

        # تخزين قائمة الفئات
        self.categories = []

        # إنشاء تخطيط النموذج
        layout = QVBoxLayout(self)
        form_layout = QFormLayout()
        form_layout.setSpacing(15)

        # حقل اسم الخدمة
        self.name_input = QLineEdit()
        self.name_input.setObjectName("text_input")
        self.name_input.setMinimumHeight(40)
        self.name_input.setPlaceholderText("أدخل اسم الخدمة")
        form_layout.addRow("اسم الخدمة:", self.name_input)

        # حقل كود الخدمة
        code_layout = QHBoxLayout()
        self.code_input = QLineEdit()
        self.code_input.setObjectName("text_input")
        self.code_input.setMinimumHeight(40)
        self.code_input.setPlaceholderText("أدخل كود الخدمة")

        generate_btn = QPushButton("توليد تلقائي")
        generate_btn.setObjectName("small_button")
        generate_btn.setFixedHeight(40)
        generate_btn.clicked.connect(self.generate_random_barcode)

        code_layout.addWidget(self.code_input)
        code_layout.addWidget(generate_btn)
        form_layout.addRow("كود الخدمة:", code_layout)

        # ملاحظة توضيحية حول سعر الخدمة
        note_label = QLabel("سيتم تحديد سعر الخدمة عند إضافتها إلى الفاتورة")
        note_label.setObjectName("hint_label")
        note_label.setStyleSheet("color: #666; font-style: italic;")
        form_layout.addRow("ملاحظة:", note_label)

        # حقل التصنيف
        self.category_combo = RTLComboBox()
        self.category_combo.setObjectName("combo_box")
        self.category_combo.setMinimumHeight(40)
        self.category_combo.setEditable(True)
        self.category_combo.setLayoutDirection(Qt.RightToLeft)
        form_layout.addRow("تصنيف الخدمة:", self.category_combo)

        # حقل الوصف
        self.description_input = QLineEdit()
        self.description_input.setObjectName("text_input")
        self.description_input.setMinimumHeight(40)
        self.description_input.setPlaceholderText("وصف اختياري للخدمة")
        form_layout.addRow("وصف الخدمة:", self.description_input)

        # تحميل الفئات
        try:
            self.categories = ProductModel.get_product_categories()
            self.category_combo.addItems(self.categories)
            # إضافة خيار "خدمات" إذا لم يكن موجودًا
            if "خدمات" not in self.categories:
                self.category_combo.addItem("خدمات")
                self.category_combo.setCurrentText("خدمات")
        except Exception as e:
            QMessageBox.warning(self, "خطأ في تحميل الفئات", f"حدث خطأ أثناء تحميل فئات المنتجات: {str(e)}")

        # ملء البيانات إذا كانت موجودة للتعديل
        if self.service_data:
            self.name_input.setText(self.service_data.get('name', ''))
            self.code_input.setText(str(self.service_data.get('code', '')))
            self.category_combo.setCurrentText(self.service_data.get('category', ''))
            self.description_input.setText(self.service_data.get('description', ''))

        # إضافة تخطيط النموذج
        layout.addLayout(form_layout)

        # إضافة أزرار الحفظ والإلغاء
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)  # تعيين المسافة بين الأزرار

        # زر الحفظ
        self.save_btn = QPushButton("حفظ")
        self.save_btn.setObjectName("primary_button")
        self.save_btn.setMinimumHeight(35)
        self.save_btn.setFixedWidth(120)  # تعيين عرض ثابت للزر
        self.save_btn.clicked.connect(self.validate_and_accept)

        # زر الإلغاء
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setObjectName("secondary_button")
        self.cancel_btn.setMinimumHeight(35)
        self.cancel_btn.setFixedWidth(120)  # تعيين نفس العرض الثابت للزر
        self.cancel_btn.clicked.connect(self.reject)

        # إضافة مساحة مرنة على الجانبين لتوسيط الأزرار
        button_layout.addStretch(1)
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addStretch(1)

        layout.addLayout(button_layout)

        # إعداد ترتيب التنقل بين الحقول
        self.setTabOrder(self.name_input, self.code_input)
        self.setTabOrder(self.code_input, self.category_combo)
        self.setTabOrder(self.category_combo, self.description_input)
        self.setTabOrder(self.description_input, self.save_btn)
        self.setTabOrder(self.save_btn, self.cancel_btn)

        # تطبيق الأنماط
        self.apply_styles()

        # تركيز البداية على حقل اسم الخدمة
        self.name_input.setFocus()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def keyPressEvent(self, event):
        """معالجة أحداث المفاتيح للتنقل بين الحقول"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            # الحصول على الحقل الحالي الذي له التركيز
            current_widget = self.focusWidget()

            # تحديد الحقل التالي للانتقال إليه
            if current_widget == self.name_input:
                self.code_input.setFocus()
            elif current_widget == self.code_input:
                self.category_combo.setFocus()
            elif current_widget == self.category_combo:
                self.description_input.setFocus()
            elif current_widget == self.description_input:
                # الانتقال إلى زر الحفظ
                self.save_btn.setFocus()
            else:
                # السلوك الافتراضي للمفاتيح الأخرى
                super().keyPressEvent(event)
        else:
            # السلوك الافتراضي للمفاتيح الأخرى
            super().keyPressEvent(event)

    def generate_random_barcode(self):
        """توليد كود باركود عشوائي"""
        # نفس الدالة من ProductDialog
        import random
        barcode = "S" + ''.join(random.choices('0123456789', k=7))
        self.code_input.setText(barcode)
        # تنشيط خانة الباركود بعد التوليد
        self.code_input.setFocus()

    def validate_and_accept(self):
        """التحقق من صحة البيانات قبل الحفظ"""
        # التحقق من اسم الخدمة
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "الرجاء إدخال اسم الخدمة")
            self.name_input.setFocus()
            return

        # التحقق من كود الخدمة
        if not self.code_input.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "الرجاء إدخال كود الخدمة")
            self.code_input.setFocus()
            return

        # التحقق من التصنيف
        if not self.category_combo.currentText().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "الرجاء إدخال تصنيف الخدمة")
            self.category_combo.setFocus()
            return

        # إذا تم اجتياز جميع التحققات، قبول الحوار
        self.accept()

    def get_service_data(self):
        """إرجاع بيانات الخدمة"""
        return {
            'name': self.name_input.text(),
            'code': self.code_input.text(),
            'price': 0,  # تعيين السعر صفر دائماً للخدمات
            'category': self.category_combo.currentText(),
            'description': self.description_input.text(),
            'favorite': False  # افتراضيًا ليست مفضلة
        }

    def apply_styles(self):
        """تطبيق الأنماط على النافذة والمكونات"""
        try:
            # استخدام تنسيقات من AppStyles
            from styles import AppStyles
            self.setStyleSheet(AppStyles.get_dialog_style())

            # تطبيق الأنماط على جميع الأزرار في النافذة
            for button in self.findChildren(QPushButton):
                # تطبيق الحد الأدنى للعرض والارتفاع على جميع الأزرار
                button.setMinimumWidth(80)
                button.setMinimumHeight(24)
        except Exception as e:
            print(f"خطأ في تطبيق الأنماط: {str(e)}")
            # تنسيقات افتراضية
            self.setStyleSheet("""
                QDialog {
                    background-color: #f0f0f0;
                    border: 1px solid #d0d0d0;
                }

                QLabel {
                    color: #000000;
                    background-color: transparent;
                }

                QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 6px;
                    background-color: white;
                    color: #000000;
                }

                QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                    border: 1px solid #0078d7;
                }

                /* تنسيق الزر الافتراضي للويندوز */
                QPushButton {
                    background-color: #e1e1e1;
                    color: #000000;
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 5px 12px;
                    text-align: center;
                    min-width: 80px;
                    min-height: 24px;
                }

                QPushButton:hover {
                    background-color: #e5f1fb;
                    border: 1px solid #0078d7;
                }

                QPushButton:pressed {
                    background-color: #cce4f7;
                    border: 1px solid #0078d7;
                }

                QPushButton:focus {
                    border: 1px solid #0078d7;
                    outline: none;
                }

                QPushButton:default {
                    background-color: #0078d7;
                    color: white;
                    border: 1px solid #0078d7;
                }

                QPushButton:default:hover {
                    background-color: #1a86d9;
                    border: 1px solid #1a86d9;
                }

                QPushButton:default:pressed {
                    background-color: #006cc1;
                    border: 1px solid #006cc1;
                }

                QPushButton:disabled {
                    background-color: #f0f0f0;
                    color: #666666;
                    border: 1px solid #d0d0d0;
                }

                QPushButton#primary_button, QPushButton#action_button {
                    background-color: #0078d7;
                    color: white;
                    border: 1px solid #0078d7;
                    border-radius: 2px;
                    padding: 5px 12px;
                    min-width: 80px;
                }

                QPushButton#primary_button:hover, QPushButton#action_button:hover {
                    background-color: #1a86d9;
                    border: 1px solid #1a86d9;
                }

                QPushButton#primary_button:pressed, QPushButton#action_button:pressed {
                    background-color: #006cc1;
                    border: 1px solid #006cc1;
                }

                QPushButton#secondary_button {
                    background-color: #e1e1e1;
                    color: #000000;
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 5px 12px;
                    min-width: 80px;
                }

                QPushButton#secondary_button:hover {
                    background-color: #e5f1fb;
                    border: 1px solid #0078d7;
                }

                QPushButton#secondary_button:pressed {
                    background-color: #cce4f7;
                    border: 1px solid #0078d7;
                }

                QPushButton#small_button {
                    background-color: #e1e1e1;
                    color: #000000;
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 5px 10px;
                    min-width: 60px;
                    min-height: 20px;
                }

                QPushButton#small_button:hover {
                    background-color: #e5f1fb;
                    border: 1px solid #0078d7;
                }

                QPushButton#small_button:pressed {
                    background-color: #cce4f7;
                    border: 1px solid #0078d7;
                }
            """)


class CategoryListDialog(QDialog):
    """نافذة عرض وإدارة التصنيفات"""
    def __init__(self, parent=None):
        super().__init__(parent)

        # تعيين خصائص النافذة
        self.setWindowTitle("التصنيفات المسجلة")
        self.setMinimumSize(500, 400)
        self.setLayoutDirection(Qt.RightToLeft)

        # تخزين قائمة الفئات
        self.categories = []

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان النافذة
        title_label = QLabel("التصنيفات المسجلة في النظام")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("page_title")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(title_label)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("content_separator")
        layout.addWidget(separator)

        # إنشاء نموذج للتصنيفات
        self.categories_layout = QVBoxLayout()
        self.categories_layout.setSpacing(5)

        # إنشاء قائمة العناصر
        self.categories_list = QTableWidget()
        self.categories_list.setColumnCount(2)
        self.categories_list.setHorizontalHeaderLabels(["اسم التصنيف", "عدد المنتجات"])
        self.categories_list.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.categories_list.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.categories_list.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.categories_list.setSelectionMode(QAbstractItemView.SingleSelection)
        self.categories_list.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.categories_list.verticalHeader().setVisible(False)
        self.categories_list.setAlternatingRowColors(True)

        # إضافة القائمة للتخطيط
        layout.addWidget(self.categories_list)

        # إضافة أزرار التحكم
        control_layout = QHBoxLayout()
        control_layout.setSpacing(10)  # تعيين المسافة بين الأزرار

        # زر إضافة تصنيف جديد
        self.add_btn = QPushButton("➕ إضافة تصنيف جديد")
        self.add_btn.setObjectName("action_button")
        self.add_btn.setMinimumHeight(35)
        self.add_btn.setFixedWidth(150)  # تعيين عرض ثابت للزر
        self.add_btn.clicked.connect(self.add_category)

        # زر تعديل التصنيف
        self.edit_btn = QPushButton("✏️ تعديل التصنيف")
        self.edit_btn.setObjectName("secondary_button")
        self.edit_btn.setMinimumHeight(35)
        self.edit_btn.setFixedWidth(150)  # تعيين نفس العرض الثابت للزر
        self.edit_btn.clicked.connect(self.edit_category)

        # زر حذف التصنيف
        self.delete_btn = QPushButton("🗑️ حذف التصنيف")
        self.delete_btn.setObjectName("secondary_button")
        self.delete_btn.setMinimumHeight(35)
        self.delete_btn.setFixedWidth(150)  # تعيين نفس العرض الثابت للزر
        self.delete_btn.clicked.connect(self.delete_category)

        # إضافة مساحة مرنة على الجانبين لتوسيط الأزرار
        control_layout.addStretch(1)
        control_layout.addWidget(self.add_btn)
        control_layout.addWidget(self.edit_btn)
        control_layout.addWidget(self.delete_btn)
        control_layout.addStretch(1)

        layout.addLayout(control_layout)

        # أزرار الإغلاق والحفظ
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)  # تعيين المسافة بين الأزرار

        # زر الإغلاق
        self.close_btn = QPushButton("إغلاق")
        self.close_btn.setObjectName("secondary_button")
        self.close_btn.setMinimumHeight(35)
        self.close_btn.setFixedWidth(120)  # تعيين عرض ثابت للزر
        self.close_btn.clicked.connect(self.accept)

        # إضافة مساحة مرنة على الجانبين لتوسيط الأزرار
        buttons_layout.addStretch(1)
        buttons_layout.addWidget(self.close_btn)
        buttons_layout.addStretch(1)

        layout.addLayout(buttons_layout)

        # تحميل التصنيفات
        self.load_categories()

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def load_categories(self):
        """تحميل التصنيفات من قاعدة البيانات"""
        try:
            # جلب التصنيفات
            self.categories = ProductModel.get_product_categories()

            # تهيئة الجدول
            self.categories_list.setRowCount(len(self.categories))

            # ملء الجدول بالبيانات
            for row, category in enumerate(self.categories):
                # اسم التصنيف
                category_item = QTableWidgetItem(category)
                category_item.setTextAlignment(Qt.AlignCenter)
                self.categories_list.setItem(row, 0, category_item)

                # عدد المنتجات
                count = ProductModel.get_products_count_by_category(category)
                count_item = QTableWidgetItem(str(count))
                count_item.setTextAlignment(Qt.AlignCenter)
                self.categories_list.setItem(row, 1, count_item)

        except Exception as e:
            QMessageBox.warning(self, "خطأ في تحميل التصنيفات", f"حدث خطأ أثناء تحميل التصنيفات: {str(e)}")

    def add_category(self):
        """إضافة تصنيف جديد"""
        try:
            # طلب اسم التصنيف من المستخدم مع تطبيق التنسيقات
            from PyQt5.QtWidgets import QInputDialog
            from styles import AppStyles

            # إنشاء QInputDialog وتطبيق التنسيقات عليه
            input_dialog = QInputDialog(self)
            input_dialog.setWindowTitle("إضافة تصنيف جديد")
            input_dialog.setLabelText("أدخل اسم التصنيف الجديد:")
            input_dialog.setInputMode(QInputDialog.TextInput)
            input_dialog.setTextValue("")

            # تطبيق التنسيقات على النافذة
            input_dialog.setStyleSheet(AppStyles.get_input_dialog_style())

            # عرض النافذة والحصول على النتيجة
            ok = input_dialog.exec_() == QInputDialog.Accepted
            category_name = input_dialog.textValue() if ok else ""

            if ok and category_name:
                # التحقق من عدم وجود التصنيف بالفعل
                if category_name in self.categories:
                    QMessageBox.warning(self, "تنبيه", "هذا التصنيف موجود بالفعل!")
                    return

                # إضافة التصنيف إلى قاعدة البيانات
                success = ProductModel.add_category(category_name)

                if success:
                    QMessageBox.information(self, "تمت الإضافة", f"تم إضافة التصنيف '{category_name}' بنجاح")
                    # تحديث قائمة التصنيفات
                    self.load_categories()
                else:
                    QMessageBox.warning(self, "خطأ", "فشلت عملية إضافة التصنيف")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة التصنيف: {str(e)}")

    def edit_category(self):
        """تعديل التصنيف المحدد"""
        try:
            # التحقق من وجود تصنيف محدد
            selected_items = self.categories_list.selectedItems()
            if not selected_items:
                QMessageBox.warning(self, "تنبيه", "الرجاء تحديد تصنيف أولاً")
                return

            # الحصول على التصنيف المحدد
            row = self.categories_list.currentRow()
            old_category = self.categories_list.item(row, 0).text()

            # طلب الاسم الجديد للتصنيف مع تطبيق التنسيقات
            from PyQt5.QtWidgets import QInputDialog
            from styles import AppStyles

            # إنشاء QInputDialog وتطبيق التنسيقات عليه
            input_dialog = QInputDialog(self)
            input_dialog.setWindowTitle("تعديل التصنيف")
            input_dialog.setLabelText("أدخل الاسم الجديد للتصنيف:")
            input_dialog.setInputMode(QInputDialog.TextInput)
            input_dialog.setTextValue(old_category)

            # تطبيق التنسيقات على النافذة
            input_dialog.setStyleSheet(AppStyles.get_input_dialog_style())

            # عرض النافذة والحصول على النتيجة
            ok = input_dialog.exec_() == QInputDialog.Accepted
            new_category = input_dialog.textValue() if ok else old_category

            if ok and new_category and new_category != old_category:
                # التحقق من عدم وجود التصنيف بالفعل
                if new_category in self.categories:
                    QMessageBox.warning(self, "تنبيه", "هذا التصنيف موجود بالفعل!")
                    return

                # تأكيد التغيير
                reply = QMessageBox.question(
                    self,
                    "تأكيد التعديل",
                    f"هل أنت متأكد من تغيير اسم التصنيف من '{old_category}' إلى '{new_category}'؟\n"
                    "سيتم تحديث جميع المنتجات المرتبطة بهذا التصنيف.",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    # تحديث التصنيف في قاعدة البيانات
                    success = ProductModel.update_category(old_category, new_category)

                    if success:
                        QMessageBox.information(self, "تم التعديل", f"تم تعديل التصنيف من '{old_category}' إلى '{new_category}' بنجاح")
                        # تحديث قائمة التصنيفات
                        self.load_categories()
                    else:
                        QMessageBox.warning(self, "خطأ", "فشلت عملية تعديل التصنيف")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل التصنيف: {str(e)}")

    def delete_category(self):
        """حذف التصنيف المحدد"""
        try:
            # التحقق من وجود تصنيف محدد
            selected_items = self.categories_list.selectedItems()
            if not selected_items:
                QMessageBox.warning(self, "تنبيه", "الرجاء تحديد تصنيف أولاً")
                return

            # الحصول على التصنيف المحدد
            row = self.categories_list.currentRow()
            category = self.categories_list.item(row, 0).text()
            count = int(self.categories_list.item(row, 1).text())

            # التحقق من وجود منتجات مرتبطة بالتصنيف
            if count > 0:
                QMessageBox.warning(
                    self,
                    "لا يمكن الحذف",
                    f"لا يمكن حذف التصنيف '{category}' لأنه يحتوي على {count} منتج/خدمة.\n"
                    "قم بتغيير تصنيف هذه المنتجات أولاً."
                )
                return

            # تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف التصنيف '{category}'؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # حذف التصنيف من قاعدة البيانات
                success = ProductModel.delete_category(category)

                if success:
                    QMessageBox.information(self, "تم الحذف", f"تم حذف التصنيف '{category}' بنجاح")
                    # تحديث قائمة التصنيفات
                    self.load_categories()
                else:
                    QMessageBox.warning(self, "خطأ", "فشلت عملية حذف التصنيف")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف التصنيف: {str(e)}")

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        try:
            # استخدام تنسيقات من AppStyles
            from styles import AppStyles
            self.setStyleSheet(AppStyles.get_dialog_style() + """
                #page_title {
                    color: #1e3a8a;
                    padding: 10px;
                }

                QTableWidget {
                    border: 1px solid #e2e8f0;
                    background-color: white;
                    gridline-color: #e2e8f0;
                    border-radius: 6px;
                    selection-background-color: rgba(59, 130, 246, 0.2);
                    selection-color: #0f172a;
                }

                QTableWidget::item {
                    padding: 10px;
                    border-bottom: 1px solid #e2e8f0;
                }

                QTableWidget::item:selected {
                    background-color: rgba(59, 130, 246, 0.2);
                    color: #0f172a;
                }

                QHeaderView::section {
                    background-color: #f1f5f9;
                    padding: 10px;
                    border: none;
                    border-bottom: 1px solid #cbd5e1;
                    font-weight: bold;
                    color: #334155;
                }
            """)

            # تطبيق الأنماط على جميع الأزرار في النافذة
            for button in self.findChildren(QPushButton):
                # تطبيق الحد الأدنى للعرض والارتفاع على جميع الأزرار
                button.setMinimumWidth(80)
                button.setMinimumHeight(24)
        except Exception as e:
            print(f"خطأ في تطبيق الأنماط: {str(e)}")
            # تنسيقات افتراضية
            self.setStyleSheet("""
                QDialog {
                    background-color: #f0f0f0;
                    border: 1px solid #d0d0d0;
                }

                QLabel {
                    color: #000000;
                    background-color: transparent;
                }

                #page_title {
                    color: #1e3a8a;
                    padding: 10px;
                }

                QTableWidget {
                    border: 1px solid #e2e8f0;
                    background-color: white;
                    gridline-color: #e2e8f0;
                    border-radius: 6px;
                    selection-background-color: rgba(59, 130, 246, 0.2);
                    selection-color: #0f172a;
                }

                QTableWidget::item {
                    padding: 10px;
                    border-bottom: 1px solid #e2e8f0;
                }

                QTableWidget::item:selected {
                    background-color: rgba(59, 130, 246, 0.2);
                    color: #0f172a;
                }

                QHeaderView::section {
                    background-color: #f1f5f9;
                    padding: 10px;
                    border: none;
                    border-bottom: 1px solid #cbd5e1;
                    font-weight: bold;
                    color: #334155;
                }

                /* تنسيق الزر الافتراضي للويندوز */
                QPushButton {
                    background-color: #e1e1e1;
                    color: #000000;
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 5px 12px;
                    text-align: center;
                    min-width: 80px;
                    min-height: 24px;
                }

                QPushButton:hover {
                    background-color: #e5f1fb;
                    border: 1px solid #0078d7;
                }

                QPushButton:pressed {
                    background-color: #cce4f7;
                    border: 1px solid #0078d7;
                }

                QPushButton:focus {
                    border: 1px solid #0078d7;
                    outline: none;
                }

                QPushButton:default {
                    background-color: #0078d7;
                    color: white;
                    border: 1px solid #0078d7;
                }

                QPushButton:default:hover {
                    background-color: #1a86d9;
                    border: 1px solid #1a86d9;
                }

                QPushButton:default:pressed {
                    background-color: #006cc1;
                    border: 1px solid #006cc1;
                }

                QPushButton:disabled {
                    background-color: #f0f0f0;
                    color: #666666;
                    border: 1px solid #d0d0d0;
                }

                QPushButton#action_button {
                    background-color: #0078d7;
                    color: white;
                    border: 1px solid #0078d7;
                    border-radius: 2px;
                    padding: 5px 12px;
                    min-width: 80px;
                }

                QPushButton#action_button:hover {
                    background-color: #1a86d9;
                    border: 1px solid #1a86d9;
                }

                QPushButton#action_button:pressed {
                    background-color: #006cc1;
                    border: 1px solid #006cc1;
                }

                QPushButton#secondary_button {
                    background-color: #e1e1e1;
                    color: #000000;
                    border: 1px solid #adadad;
                    border-radius: 2px;
                    padding: 5px 12px;
                    min-width: 80px;
                }

                QPushButton#secondary_button:hover {
                    background-color: #e5f1fb;
                    border: 1px solid #0078d7;
                }

                QPushButton#secondary_button:pressed {
                    background-color: #cce4f7;
                    border: 1px solid #0078d7;
                }
            """)